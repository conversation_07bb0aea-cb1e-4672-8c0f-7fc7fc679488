-- BD月度绩效明细表，2025新版，每天一份数据，每个月的最后一天的数据是当月的汇总数据

CREATE TABLE IF NOT EXISTS app_bd_mtd_comm_mi(
	last_bd_name STRING COMMENT 'BD名字',
	 last_bd_id STRING COMMENT 'BD_ID',
	 dep_level3 STRING COMMENT 'BD归属的销售大区',
	 dep_name STRING COMMENT 'BD所在的销售区域',
	 total_score_num DOUBLE COMMENT '利润积分',
	 bd_performance_rate DOUBLE COMMENT '利润积分系数',
	 total_comm_amt DOUBLE COMMENT '本月截止今天的总佣金总额',
	 a_commisstion_amt DOUBLE COMMENT '高价值客户总佣金',
	 a_cust_cnt STRING COMMENT '高价值客户数',
	 a_cust_comm_amt DOUBLE COMMENT '高价值客户数佣金',
	 more_than_spu_cnt BIGINT COMMENT '高价值客户超额SPU数',
	 a_spu_comm_amt DOUBLE COMMENT '高价值超额spu佣金',
	 category_comm_amt DOUBLE COMMENT '品类推广总佣金',
	 old_cust_comm DOUBLE COMMENT '存量客户品类佣金',
	 new_cust_comm DOUBLE COMMENT '新增客户品类佣金',
	 big_sku_cnt DOUBLE COMMENT '品类推广件数_大规格',
	 old_big_sku_cnt DOUBLE COMMENT '存量客户推广件数_大规格',
	 new_big_sku_cnt DOUBLE COMMENT '新增客户推广件数_大规格',
	 dlv_real_amt DOUBLE COMMENT '当月截止今天为止的履约实付总GMV',
	 item_profit_amt DOUBLE COMMENT '当月截止今天为止的履约商品总毛利润',
	 dlv_spu_cnt BIGINT COMMENT '当月截止今天为止的履约SPU数',
	 more_than_spu_cust BIGINT COMMENT '高价值超额spu客户数',
	 score_target BIGINT COMMENT '利润积分目标'
) 
PARTITIONED BY (ds STRING COMMENT '数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='每个BD月度销售绩效汇总表（2025新版绩效）') 
LIFECYCLE 720;

-- 取1月至10月每个月的所有BD的月度绩效明细
SELECT * FROM
(SELECT  last_bd_name
        ,total_comm_amt
        ,SUBSTR(ds,1,6) 绩效月份
        ,RANK() OVER (PARTITION BY last_bd_name,SUBSTR(ds,1,6) ORDER BY ds DESC ) AS rnk --取每个月的最后一天的数据
FROM    app_bd_mtd_comm_mi
WHERE   ds BETWEEN '20250101' AND '20251001')
WHERE rnk=1;