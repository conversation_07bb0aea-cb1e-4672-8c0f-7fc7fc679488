-- 注意这个表里面有个typo:amout，我们改变不了，使用时请一定注意

CREATE TABLE IF NOT EXISTS app_crm_bd_month_gmv_mi(
	bd_id BIGINT COMMENT '销售id',
	 bd_name STRING COMMENT '销售姓名',
	 total_gmv DECIMAL(38,18) COMMENT '总gmv',
	 single_gmv DECIMAL(38,18) COMMENT '单店gmv',
	 vip_gmv DECIMAL(38,18) COMMENT '大客户gmv',
	 fruit_gmv DECIMAL(38,18) COMMENT '鲜果gmv',
	 dairy_gmv DECIMAL(38,18) COMMENT '乳制品gmv',
	 non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品gmv',
	 brand_gmv DECIMAL(38,18) COMMENT '自营品牌gmv',
	 reward_gmv DECIMAL(38,18) COMMENT '固定奖励sku的gmv',
	 reward_amout BIGINT COMMENT '固定奖励sku销量',
	 category_award DECIMAL(38,18) COMMENT '品类提成金额',
	 core_merchant_amout BIGINT COMMENT '核心客户数',
	 core_merchant_card_level DECIMAL(38,18) COMMENT '核心商户数净增长牌级',
	 month_live_amout BIGINT COMMENT '本月的月活门店数',
	 pull_new_amout BIGINT COMMENT '本月拉新门店数',
	 performance DECIMAL(38,18) COMMENT 'bd本月绩效',
	 ordinary_num BIGINT COMMENT '本月普通拜访次数',
	 drop_in_visit_num BIGINT COMMENT '本月普通上门拜访次数',
	 efficient_num BIGINT COMMENT '本月有效拜访次数，‘有效拜访’是特指销售上门成功见到核心 KP（老板 / 下单负责人）并向其介绍商城活动、推荐产品、进行客情维护等',
	 worth_num BIGINT COMMENT '本月价值拜访次数',
	 single_month_live_num BIGINT COMMENT '单店本月月活',
	 vip_month_live_num BIGINT COMMENT '大客户门店本月月活',
	 delivery_gmv DECIMAL(38,18) COMMENT '本月配送gmv',
	 spu_average DECIMAL(38,18) COMMENT '配送spu均值',
	 single_spu_average DECIMAL(38,18) COMMENT '单店配送spu均值',
	 vip_spu_average DECIMAL(38,18) COMMENT '大客户门店配送spu均值',
	 category_multiply_gmv DECIMAL(38,18) COMMENT '周期品类提成*配送金额',
	 normal_pull_new_amout BIGINT COMMENT '普通新门店数，即新客户中，不算pull_new_amout的门店数',
	 no_order_register BIGINT COMMENT '注册未下单的客户数'
) 
PARTITIONED BY (ym STRING) -- 分区格式:yyyyMM 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='bd每个月的绩效gmv明细表(按月分区)');