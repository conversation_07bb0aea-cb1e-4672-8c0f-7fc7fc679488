CREATE TABLE `xianmudb`.`merchant` (
  `m_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '商户ID，代表一家门店的唯一ID',
  `mname` varchar(255) COMMENT '商户名称，或者门店名称',
  `phone` varchar(20) COMMENT '商户的手机，指注册时使用的手机号',
  `islock` int(11) DEFAULT '1' COMMENT '审核状态：0、审核通过 1、审核中 2、审核未通过 3、账号被拉黑 4、注销',
  `register_time` datetime COMMENT '注册时间',
  `audit_time` datetime COMMENT '审核时间',
  `audit_user` int(11) COMMENT '审核人ID，取自`admin`.`admin_id`',
  `province` varchar(20) COMMENT '商户的注册地址所在省份，比如【广东、江苏、浙江、上海、福建、四川、湖南、湖北、重庆、江西、安徽、山东、广西壮族自治区、云南、贵州】，请注意这个字段不会以‘省’结尾。',
  `city` varchar(20) COMMENT '商户的注册地址所在城市, 比如【南宁市、宁波市、杭州市】',
  `area` varchar(50) COMMENT '商户的注册地址所在区或者县，比如【江南区、海曙区、余杭区、连江县】等',
  `address` varchar(255) COMMENT '商户的详细地址，如：建政街道东葛路1055号众享披萨(青秀万达店)。注意这个字段不会包含省份、城市、区县信息，只包含街道门牌号等信息。',
  `last_order_time` datetime COMMENT '上次下单时间',
  `area_no` int(11) DEFAULT '1001' COMMENT '商户被划分到的运营服务区编号，取自`area`.`area_no`, 一个商户必定有一个唯一的area_no',
  `size` varchar(50) DEFAULT '单店' COMMENT '门店的规模：[大客户，大连锁，小连锁，单店]',
  `type` varchar(50) COMMENT '客户类型:[其他,其它,加盟店,咖啡,水果/果切/榨汁店,水果店,水果捞/果切店,甜品冰淇淋,社区生鲜店,茶饮,菜市场水果摊,西餐,请选经营类型,面包蛋糕,面包蛋糕点心]',
  `admin_id` int(11) COMMENT '当此字段不为`NULL`时，表示此商户所属的大客户ID(门店所属的连锁品牌ID)。如果此字段为空，则表示这是一家单店(也称为平台客户)，不属于任何连锁品牌。关联查询：admin.admin_id and admin_type = 0。',
  `direct` int(11) COMMENT '是否支持账期，1：账期，2：现结（必须要现金结算货款）',
  `member_integral` decimal(10,2) DEFAULT '0.00' COMMENT '会员当月积分',
  `grade` int(2) COMMENT '会员等级[0,1,2,3]',
  `recharge_amount` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '鲜沐卡余额（一种预充值服务，门店需要先充值给我司，然后下单时可用鲜沐卡余额支付）',
  `update_time` datetime ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `operate_status` tinyint(3) unsigned DEFAULT '0' COMMENT '商户经营状态。0:正常, 1:倒闭, 2:待提交核验, 3:核验中, 4:已核验拒绝(补充资料后可再次发起核验)',
  `business_line` int(11) DEFAULT '0' COMMENT '业务线，0=鲜沐; 1=pop(顺鹿达)',
  `submit_review_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '用户提交审核时间',
  PRIMARY KEY (`m_id`),
  UNIQUE KEY `Unique_openid` (`openid`) USING BTREE,
  UNIQUE KEY `i_phone` (`phone`),
  KEY `index_mname` (`mname`),
  KEY `merchant_admin_index` (`admin_id`,`direct`,`m_id`),
  KEY `merchant_area_index` (`area_no`,`size`,`type`) USING BTREE,
  KEY `merchant_islock_m_id_index` (`islock`,`admin_id`),
  KEY `idx_channel_code` (`channel_code`),
  KEY `idx_province_city_area` (`province`,`city`,`area`),
  KEY `idx_islock_register_time` (`islock`,`register_time`)
) ENGINE=InnoDB AUTO_INCREMENT=525609 DEFAULT CHARSET=utf8 COMMENT='商户信息主表。存储了商户的名字、注册地址以及省市区、手机号、所属的大客户admin_id（如有）、所属的运营服务区编号(area_no)等核心信息。一个门店有可能是连锁品牌的加盟店之一，此时它的admin_id字段应该有值；否则为NULL时，表示这是一个单店（独立门店）。一个门店可能有多个子账户（店长和店员），关联查询merchant_sub_account表。';