-- 请注意，‘有效拜访’是特指销售上门成功见到核心 KP（老板 / 下单负责人）并向其介绍商城活动、推荐产品、进行客情维护等。
-- 其他拜访方式，比如电话拜访、微信拜访等，都是指 BD 通过电话、微信等方式与客户进行沟通，但没有见到核心 KP。但其本身也是合法有效的拜访，也算作一次拜访记录。

CREATE TABLE IF NOT EXISTS ods_follow_up_record_df(
	 m_id BIGINT COMMENT '商户id, 别的表经常使用cust_id来表示商户id',
	 admin_id BIGINT COMMENT '即bd_id',
	 admin_name STRING COMMENT '即bd_name',
	 follow_up_way STRING COMMENT '拜访方式。枚举值:[普通上门拜访, 普通拜访-微信, 普通拜访-电话, 普通拜访-企微, 有效拜访]',
	 condition STRING COMMENT '本次拜访中，BD对门店经营情况的客观描述',
	 `status` BIGINT COMMENT '跟进记录状态，0未跟进，1已跟进，2已跟进且下单，3联系不上，4放弃跟进,9重置',
	 add_time DATETIME COMMENT '拜访时间，例如2023-07-20 12:44:39',
	 visit_objective BIGINT COMMENT '拜访目的:0拉新,1催月活,2客户维护,3拓品,4售后处理,5催省心送',
	 visit_type BIGINT COMMENT '拜访类型:0普通拜访,1陪访(销售主管M1/M2等才会有陪访记录)',
	 escort_admin_id BIGINT COMMENT '陪访人的bd_id（销售主管M1/M2等）',
	 `location` STRING COMMENT '拜访地址，即门店的地址之一',
	 province STRING COMMENT '客户所在的省，比如广东、浙江',
	 city STRING COMMENT '客户所在的市，比如广州市、杭州市',
	 area STRING COMMENT '客户所在的区，比如天河区、西湖区',
	 area_no BIGINT COMMENT '客户所属的运营服务区编码',
) 
PARTITIONED BY (ds STRING) 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='BD对其私海客户的拜访记录明细表，含每一次BD对门店的拜访记录明细，包括门店ID、BD ID、拜访方式、拜访目的、拜访时间等') 
LIFECYCLE 30;