CREATE TABLE IF NOT EXISTS ods_merchant_leads_df(
	id BIGINT,
	 mname STRING COMMENT '商户名称',
	 admin_id BIGINT COMMENT 'BD的ID',
	 admin_name STRING COMMENT 'BD的名字',
	 `status` BIGINT COMMENT '0 待跟进 1 待注册 2 已注册 3 已失效',
	 addtime DATETIME comment '创建时间',
	 m_id BIGINT COMMENT '门店ID，也经常用做cust_id'
) 
PARTITIONED BY (ds STRING) 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='记录门店注册时使用的是哪个BD的地推码。如果一个门店没有使用BD的地推码注册(status=2)，那么就表示它是自主注册的。');