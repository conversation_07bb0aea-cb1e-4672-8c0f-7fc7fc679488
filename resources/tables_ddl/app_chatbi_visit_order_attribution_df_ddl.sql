CREATE TABLE IF NOT EXISTS app_chatbi_visit_order_attribution_df
(
    follow_up_record_id   BIGINT COMMENT '拜访记录ID，对应ods_follow_up_record_df表的主键'
    ,bd_id                BIGINT COMMENT 'BD人员ID，业务开发人员标识'
    ,bd_name              STRING COMMENT 'BD人员姓名，如：蒋万君、马华健等'
    ,visit_time           DATETIME COMMENT '拜访时间，BD实际拜访客户的时间'
    ,follow_up_way        STRING COMMENT '跟进方式，如：普通拜访-微信、普通拜访-电话、有效拜访、普通拜访-企微等'
    ,visit_objective      STRING COMMENT '拜访目的, 可选值[拉新,催月活,客户维护,拓品,售后处理,催省心送]'
    ,cust_id              BIGINT COMMENT '客户ID，门店商户标识'
    ,cust_name            STRING COMMENT '客户名称，门店名称如：星期天吐司屋、HAVEN、小时光一起吃私房烘焙等'
    ,cust_register_time   DATETIME COMMENT '客户注册时间，门店首次注册平台的时间'
    ,cust_register_date   STRING COMMENT '客户注册日期，格式为yyyyMMdd'
    ,cust_size            STRING COMMENT '客户规模类型，主要为：单店（占绝大多数）、大客户等'
    ,`condition`          STRING COMMENT '跟进情况描述，BD记录的具体拜访内容和客户反馈，包含竞对信息、推荐商品、下单原因、待办事项等详细信息'
    ,area_no              BIGINT COMMENT '运营服务区编号，用于区分不同的服务区域'
    ,cust_province        STRING COMMENT '客户省份，门店所在省份如：上海、广东、福建、四川等'
    ,cust_city            STRING COMMENT '客户城市，门店所在城市如：上海市、肇庆市、泉州市、乐山市等'
    ,cust_area            STRING COMMENT '客户区域，门店所在区县如：浦东新区、高要区、惠安县、市中区等'
    ,orders_cnt           BIGINT COMMENT '归因订单数量，该次拜访后15天内归因的订单总数'
    ,total_gmv            DECIMAL(38,18) COMMENT '归因订单总GMV，该次拜访归因的所有订单金额总和'
    ,total_sku_cnt        BIGINT COMMENT '归因商品总数量，该次拜访归因订单中的商品件数总和'
    ,same_day_orders_cnt  BIGINT COMMENT '当天下单订单数量，拜访当天即下单的订单数'
    ,same_day_total_gmv   DECIMAL(38,18) COMMENT '当天下单订单总GMV，拜访当天下单的订单金额总和'
    ,fruit_gmv            DECIMAL(38,18) COMMENT '鲜果品类GMV，归因订单中鲜果类商品的金额'
    ,others_gmv           DECIMAL(38,18) COMMENT '其他品类GMV，归因订单中其他分类商品的金额'
    ,dairy_gmv            DECIMAL(38,18) COMMENT '乳制品品类GMV，归因订单中乳制品类商品的金额'
    ,self_owned_brand_gmv DECIMAL(38,18) COMMENT '自有品牌GMV，归因订单中自有品牌商品的金额'
    ,at_gmv               DECIMAL(38,18) COMMENT 'AT商品GMV，特定SKU(N001S01R005,N001S01R002)的订单金额'
    ,min_time_gap_days    BIGINT COMMENT '最小时间间隔天数，拜访时间到订单下单时间的最小间隔'
    ,max_time_gap_days    BIGINT COMMENT '最大时间间隔天数，拜访时间到订单下单时间的最大间隔'
    ,avg_time_gap_days    DOUBLE COMMENT '平均时间间隔天数，拜访时间到订单下单时间的平均间隔'
    ,order_no_list        STRING COMMENT '归因的订单列表'
)
PARTITIONED BY 
(
    ds                    STRING COMMENT '分区日期，数据统计日期'
)
STORED AS ALIORC
TBLPROPERTIES ('comment' = 'BD拜访归因订单效果分析表：记录BD拜访后15天内客户下单情况，用于分析拜访效果和转化情况。每行代表一次拜访记录及其归因的所有订单汇总数据。')
LIFECYCLE 30
;