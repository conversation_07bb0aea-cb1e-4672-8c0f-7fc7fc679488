agent_name: deep_research_agent
model_provider: openrouter
model: x-ai/grok-code-fast-1
model_settings:
  temperature: 0.1
  extra_body: {"provider": {"sort": "throughput"}}
need_system_prompt: true
tools:
  - name: fetch_odps_sql_result
  - name: fetch_ddl_for_table
  - name: get_sales_manager_team_members
# 配置在哪些工具执行后停止，允许用户查看结果
stop_at_tool_names:
  - fetch_odps_sql_result
agent_description: deep_research_agent.md
agent_tables:
  - name: app_chatbi_cust_orders_df
    desc: 客户订单分析结果表，该表包含了近3年以来的所有订单数据，已经打平到了sku, cust_id, order_date, bd_id, warehouse_name 等维度，可以直接用于仓库维度、BD维度的分析等。
  - name: app_chatbi_mall_cust_analytics_di
    desc: 商城客户行为分析日汇总表，按天汇总的客户行为与下单汇总，适用于天级别的客户新注册、首单统计、客户登录统计、客户转化率统计等。支持用户活跃度、留存率、转化漏斗等深度分析。
  - name: dwd_trd_saas_order_df
    desc: SaaS订单明细表，该表包含了近3年以来的所有订单数据，已经打平到了sku, cust_id, order_date, bd_id, warehouse_name 等维度，可以直接用于仓库维度、BD维度的分析等。支持SaaS业务的深度数据挖掘。
  - name: app_chatbi_supplier_order_analysis_df
    desc: 供应商订单分析结果表，整合订单、商品、仓库、供应商等维度信息，重点支持供应商维度的销售情况分析。可用于供应商绩效评估、供应链优化、采购策略制定等深度研究。
  - name: app_chatbi_search_analysis_df
    desc: 客户的搜索分析汇总，以客户ID作为维度，可以用于查询搜索过特定商品的客户行为分析。支持用户意图识别、商品推荐优化、搜索算法改进等研究。
  - name: app_crm_cust_label_df
    desc: 客户画像表，详细记录了客户归属的BD、注册日、审核日、最近N天的下单金额、订单数、商品数、履约订单数、履约金额、履约成本、客户价值评估等。
  - name: app_bd_mtd_comm_mi
    desc: BD月度绩效明细表，2025新版，每天一份数据，每个月的最后一天的数据是当月的汇总数据
  - name: app_crm_bd_day_gmv_di
    desc: BD月度GMV明细表，记录每个BD销售人员的月度GMV业绩数据，包括总GMV、各类型客户GMV、品类GMV、拜访数据、客户活跃度等关键指标。与bd_mtd_comm_df表配合使用，提供BD绩效考核、业务分析的全面数据支持。
  - name: dws_cust_after_dlv_profit_label_mi
    desc: 客户分层标签表，可查询A类、B类客户等
  - name: app_chatbi_bd_hierarchy_df
    desc: BD人员层级关系表，包含BD的完整层级关系和以及BD是否在职、是否是M1/M2/M3管理者等信息
  - name: app_chatbi_visit_order_attribution_df
    desc: BD拜访归因订单效果分析表：记录BD拜访后15天内客户下单情况，用于分析拜访效果和转化情况。每行代表一次拜访记录及其归因的所有订单汇总数据。
  - name: ods_merchant_leads_df
    desc: 记录门店注册时使用的是哪个BD的地推码。如果一个门店没有使用BD的地推码注册(status=2)，那么就表示它是自主注册的。
agent_as_tool_description: |
  这是一个专门用于ODPS表深度分析的AI代理，基于5个核心ODPS表提供历史数据研究服务。

  **核心分析能力：**
  - 历史订单分析：基于app_chatbi_cust_orders_df表，统计近3年订单数据，按SKU、客户ID、订单日期、BD、仓库维度进行销售分析、客户购买行为研究（不适用于今日数据）。
    - 该表的is_self_registered字段，表示门店是否是自主注册的。门店的注册有两种方式：自主注册、BD地推码注册。
  - 客户行为汇总分析：基于app_chatbi_mall_cust_analytics_di表，按天汇总新注册客户、首单客户、登录客户、转化率等，支持活跃度、留存率、转化漏斗分析。
  - SaaS订单研究：基于dwd_trd_saas_order_df表，分析近3年SaaS订单特征、付费行为、续费模式，按SKU、客户、日期等维度。
  - 供应商订单分析：基于app_chatbi_supplier_order_analysis_df表，统计供应商销售情况、绩效评估、供应链优化、采购策略，支持订单、商品、仓库、供应商维度交叉分析。
  - 搜索行为洞察：基于app_chatbi_search_analysis_df表，以客户ID维度查询搜索特定商品的客户行为，支持意图识别、推荐优化。
  - 客户画像分析：基于app_crm_cust_label_df表，分析客户画像，包括BD、注册日、审核日、最近N天的下单金额、订单数、商品数、履约订单数、履约金额、履约成本、客户价值评估等。
  - BD绩效分析：基于app_bd_mtd_comm_mi表，分析BD的月度绩效，包括利润积分、佣金、高价值客户数、超额SPU数等。
  - BD月度GMV分析(旧版绩效，不含高价值客户)：基于app_crm_bd_day_gmv_di表，分析BD的月度GMV，包括总GMV、各类型客户GMV、品类GMV、拜访数据、客户活跃度等关键指标。
  - 客户分层标签分析：基于dws_cust_after_dlv_profit_label_mi表，分析客户分层标签，包括A类、B类客户等。
  - BD拜访归因订单效果分析：基于app_chatbi_visit_order_attribution_df表，分析BD拜访后15天内客户下单情况，用于分析拜访效果和转化情况。

  **通用数据能力：**
  - 时间序列统计：支持日、周、月、季度、年粒度趋势分析。
  - 多维度交叉：客户×商品、区域×时间、供应商×品类等OLAP查询。
  - SQL支持：复杂查询包括跨表关联、子查询、窗口函数，限于历史数据。

  **与sales_order_analytics的区别：**
  - sales_order_analytics关注的是实时数据，而本代理关注的是历史数据。
  - sales_order_analytics无法提供用户登录统计、用户活跃度分析等能力。
  - sales_order_analytics无法提供SaaS订单分析、供应商订单分析、搜索行为洞察、仓库销售分析等能力。

  该代理使用ODPS SQL工具执行查询，提供精准的历史数据洞察，支持销售管理、客户分析、供应链优化等场景。