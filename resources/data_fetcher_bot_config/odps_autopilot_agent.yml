agent_name: odps_autopilot_agent
model_provider: openrouter
model: x-ai/grok-code-fast-1
model_settings:
  temperature: 0.1
  extra_body: {"provider": {"sort": "throughput"}}
need_system_prompt: true
tools:
  - name: search_ddl_by_content
  - name: fetch_odps_sql_result
# 配置在哪些工具执行后停止，允许用户查看结果
stop_at_tool_names:
  - fetch_odps_sql_result
agent_description: odps_autopilot_agent.md
agent_tables:
  - name: 动态发现
    desc: 本代理通过search_ddl_by_content工具动态发现与用户查询主题相关的ODPS表，无需预定义表列表
agent_as_tool_description: |
  这是一个ODPS自动驾驶分析代理，能够根据用户提供的任意业务主题自动发现相关表结构并执行数据分析。

  **核心能力：**
  - 智能表发现：使用search_ddl_by_content工具，根据业务关键词（如"SaaS订单"、"客户分析"、"库存管理"）自动发现相关的ODPS表结构
  - 表结构分析：深入分析发现的表结构，理解字段含义、数据关系和业务逻辑
  - SQL智能构建：基于表结构分析结果，构建合适的SQL查询语句
  - 数据查询执行：使用fetch_odps_sql_result工具执行SQL查询，获取实际业务数据
  - 业务洞察提供：结合查询结果提供有价值的业务分析和洞察

  **支持的业务主题：**
  - SaaS订单分析：自动发现SaaS相关表，分析订单趋势、客户行为等
  - 客户分析：发现客户相关表，进行客户画像、行为分析、价值评估等
  - 库存管理：发现库存相关表，分析库存状态、周转率、补货需求等
  - 财务分析：发现财务相关表，分析收入、成本、利润等财务指标
  - 营销活动：发现营销相关表，分析活动效果、ROI、客户响应等
  - 供应链分析：发现供应商、采购相关表，分析供应链效率和成本
  - 物流配送：发现配送相关表，分析配送效率、成本、客户满意度等

  **工作流程：**
  1. 接收用户的业务主题查询（支持中文关键词）
  2. 使用search_ddl_by_content工具搜索相关表结构
  3. 分析表结构，理解数据模型和业务逻辑
  4. 构建针对性的SQL查询语句
  5. 执行查询并获取数据结果
  6. 提供业务分析和洞察

  **与其他代理的区别：**
  - 不依赖预定义的表列表，能够动态发现任何业务主题的相关表
  - 支持探索性数据分析，适合未知领域的数据发现
  - 结合表结构分析和数据查询，提供端到端的分析能力
  - 特别适合处理复杂的、跨多个表的业务分析需求

  该代理使用智能表发现技术，能够处理各种业务主题的数据分析需求，为用户提供全面的ODPS数据洞察服务。
