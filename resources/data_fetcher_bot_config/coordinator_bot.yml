agent_name: coordinator_bot
model_provider: openrouter
model: anthropic/claude-sonnet-4
model_settings:
  temperature: 0.1
  tool_choice: "required"
  extra_body: {"provider": {"only": ["anthropic","openai","google-vertex","amazon-bedrock"],"sort": "latency"}}
tools:
  - name: search_product_by_name
  - name: ask_user_to_confirm_sku
  - name: get_user_location
  - name: search_poi_by_keyword
  - name: search_nearby_customers
agent_tools:
  - name: sales_order_analytics
  - name: deep_research_agent
  - name: warehouse_and_fulfillment
  - name: general_chat_bot
# coordinator_bot 不需要在工具执行后停止，因为它负责协调多个专业Agent
stop_at_tool_names: []
need_system_prompt: false
agent_description: coordinator_bot.md