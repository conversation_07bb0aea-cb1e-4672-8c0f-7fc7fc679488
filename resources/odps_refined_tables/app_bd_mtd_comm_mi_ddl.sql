```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_bd_mtd_comm_mi` (
  `last_bd_name` STRING COMMENT '最新归属BD姓名',
  `last_bd_id` STRING COMMENT 'BD_ID，销售人员的唯一标识',
  `dep_level3` STRING COMMENT '大区名称，枚举值：华南大区、上海大区、浙江大区、苏皖大区、西南大区、闽桂大区、山东大区、华中大区、电销、昆明大区',
  `dep_name` STRING COMMENT '区域名称，枚举值：深圳、浦东、浙南、浦西、无锡、杭州、贵阳、广西、杭州湾、青岛、东莞、武汉、电销、重庆、江西、四川、长沙、广州、大粤西、苏州、佛山、徽京、福泉、苏北、济南、昆明、厦门',
  `total_score_num` DOUBLE COMMENT '利润积分，销售人员的利润积分总额',
  `bd_performance_rate` DOUBLE COMMENT '利润积分系数，销售人员的绩效系数，取值范围：1.0-1.35',
  `total_comm_amt` DOUBLE COMMENT '佣金总额，销售人员获得的总佣金金额',
  `a_commisstion_amt` DOUBLE COMMENT '高价值客户总佣金，来自高价值客户的总佣金',
  `a_cust_cnt` STRING COMMENT '高价值客户数，高价值客户的数量统计',
  `a_cust_comm_amt` DOUBLE COMMENT '高价值客户数佣金，基于高价值客户数计算的佣金',
  `more_than_spu_cnt` BIGINT COMMENT '高价值客户超额SPU数，高价值客户超出标准的SPU数量',
  `a_spu_comm_amt` DOUBLE COMMENT '高价值超额spu佣金，基于超额SPU数计算的佣金',
  `category_comm_amt` DOUBLE COMMENT '品类推广总佣金，品类推广活动获得的总佣金',
  `old_cust_comm` DOUBLE COMMENT '存量客户品类佣金，来自存量客户的品类推广佣金',
  `new_cust_comm` DOUBLE COMMENT '新增客户品类佣金，来自新增客户的品类推广佣金',
  `big_sku_cnt` DOUBLE COMMENT '品类推广件数_大规格，大规格商品的推广件数',
  `old_big_sku_cnt` DOUBLE COMMENT '存量客户推广件数_大规格，存量客户的大规格商品推广件数',
  `new_big_sku_cnt` DOUBLE COMMENT '新增客户推广件数_大规格，新增客户的大规格商品推广件数',
  `dlv_real_amt` DOUBLE COMMENT 'MTD履约实付GMV，本月至今履约的实际支付GMV金额',
  `item_profit_amt` DOUBLE COMMENT 'MTD履约商品毛利润，本月至今履约商品的毛利润金额',
  `dlv_spu_cnt` BIGINT COMMENT '履约SPU数，本月至今履约的SPU数量',
  `more_than_spu_cust` BIGINT COMMENT '高价值超额spu客户数，拥有超额SPU的高价值客户数量',
  `score_target` BIGINT COMMENT '利润积分目标，销售人员本月的利润积分目标值',
  `arpu_comm_amt` DOUBLE COMMENT '高价值客户超ARPU佣金，高价值客户超出ARPU标准获得的佣金'
)
COMMENT 'MTD单销售绩效汇总表，记录销售人员本月至今的各项绩效指标和佣金数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，数据日期，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='MTD单销售绩效汇总表，包含销售人员的利润积分、佣金金额、客户数量等关键绩效指标')
LIFECYCLE 720
```