CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_lifecycle_change_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202508表示2025年8月',
  `cust_team` STRING COMMENT '客户团队类型，取值范围：Mars大客户、平台客户、集团大客户（喜茶）、集团大客户（茶百道）',
  `cust_type` STRING COMMENT '客户行业类型，取值范围：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、水果店、水果捞/果切店、社区生鲜店、菜市场水果摊、请选经营类型、面包蛋糕点心、其他',
  `register_province` STRING COMMENT '注册时省份，包含中国各省份及直辖市名称',
  `life_cycle_detail_begin` STRING COMMENT '当月生命周期标签，取值范围：A1、A2、A3、B1、B2、L1、L2、L3、N0、N1、N2、S1、S2、W',
  `life_cycle_detail_last` STRING COMMENT '次月生命周期标签，取值范围：A1、A2、A3、B1、B2、L1、L2、L3、N0、N1、N2、S1、S2、W、无效',
  `cust_cnt` BIGINT COMMENT '客户数量，统计客户流转数量'
)
COMMENT '客户等级流转表，记录客户生命周期标签在月度间的流转变化情况'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户等级流转表，用于分析客户生命周期标签的月度变化趋势',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;