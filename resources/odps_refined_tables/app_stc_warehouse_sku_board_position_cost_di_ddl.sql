```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_stc_warehouse_sku_board_position_cost_di` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd',
  `warehouse_no` BIGINT COMMENT '库存仓ID，唯一标识仓库的编号',
  `warehouse_name` STRING COMMENT '库存仓名称，如：上海总仓、嘉兴总仓等',
  `batch_no` STRING COMMENT '批次编号，用于标识同一批次的商品',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位的唯一标识',
  `sku_type` STRING COMMENT '商品类型：自营-平台自营商品，代仓-第三方仓储商品，代售-代销商品',
  `spu_id` BIGINT COMMENT 'SPU ID，标准产品单元的唯一标识',
  `spu_no` STRING COMMENT 'SPU编号，标准产品单元的编码',
  `spu_name` STRING COMMENT 'SPU名称，标准产品单元的名称',
  `sku_disc` STRING COMMENT 'SKU描述，包含规格、包装等信息',
  `category` STRING COMMENT '一级类目：乳制品-奶制品类，其他-其他类别，鲜果-新鲜水果类',
  `storage_way` STRING COMMENT '存储方式：冷冻-零下温度存储，冷藏-低温存储，常温-室温存储',
  `pack_unit` STRING COMMENT '包装单位：箱、桶、袋、罐、瓶、筐、包、盒、块、卷、个、件、组、份、条、台、套',
  `production_date` DATETIME COMMENT '生产日期，格式：yyyy-MM-dd HH:mm:ss，商品的生产时间',
  `quality_date` DATETIME COMMENT '保质期，格式：yyyy-MM-dd HH:mm:ss，商品的有效期限',
  `init_quantity` BIGINT COMMENT '期初库存数量，统计周期开始时的库存数量',
  `warehouse_sku_quantity` BIGINT COMMENT '仓库库存数量，当前仓库中的SKU库存数量',
  `storage_type` STRING COMMENT '储存类别：整件-整件存储，拆包-拆包存储，None-无存储类型',
  `storage_num` BIGINT COMMENT '箱入数，每箱包含的商品数量',
  `layer_height` BIGINT COMMENT '层高，货架层数的高度',
  `layer_total` BIGINT COMMENT '层码放数量，单层可码放的商品总数',
  `layer_cnt` BIGINT COMMENT '单板码放数量，单个托盘可码放的商品数量',
  `layer_cnt_up` DECIMAL(38,18) COMMENT '板位数（原始），原始计算的板位数量',
  `board_position_fee_cnt` DECIMAL(38,18) COMMENT '板位数（计费），用于计费的板位数量',
  `board_position_warehouse_cnt` DECIMAL(38,18) COMMENT '板位数（仓维汇总），按仓库维度汇总的板位数量',
  `board_position_temperature_cnt` DECIMAL(38,18) COMMENT '板位数（仓温维汇总），按仓库温度维度汇总的板位数量',
  `warehouse_layer_tempature_amt` DECIMAL(38,18) COMMENT '板位费（板位费-板位数（仓温维汇总）），基于仓温维度的板位费用',
  `board_position_unit` DECIMAL(38,18) COMMENT '板位价格，单个板位的价格'
)
COMMENT '板位费汇总数据表，记录仓库SKU板位费用的详细数据，包括库存、存储方式和费用计算等信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，用于按日期分区'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '板位费汇总数据表，用于分析和计算仓库SKU的板位费用',
  'lifecycle' = '30'
)
```