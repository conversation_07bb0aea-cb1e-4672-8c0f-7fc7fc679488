```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_cust_order_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位的唯一标识',
  `spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格、包装等详细信息',
  `cust_type` STRING COMMENT '客户业态，取值范围：其他、咖啡、水果/果切/榨汁店、甜品冰淇淋、茶饮、西餐、面包蛋糕、水果店、蛋糕店、面包蛋糕点心、西餐披萨、加盟店、糖水/水果捞',
  `cust_cnt` BIGINT COMMENT '交易客户数，统计周期内购买该SKU的客户数量',
  `group_cust_cnt` BIGINT COMMENT '业态总客户数，统计周期内该业态的总客户数量'
)
COMMENT '区域渗透数据分析表，统计各SKU在不同客户业态中的渗透情况，用于分析商品的市场覆盖度和客户群体分布'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '区域渗透数据分析表，提供SKU级别的客户业态分布和渗透率分析',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```