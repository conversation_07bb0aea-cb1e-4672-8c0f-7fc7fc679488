CREATE TABLE IF NOT EXISTS app_bms_purchase_stock_detail_df(
	`operate_stock_time` DATETIME COMMENT '操作出入库时间，格式：年月日时分秒',
	`operate_type` BIGINT COMMENT '操作类型：56-采购退货出库',
	`operate_type_name` STRING COMMENT '操作类型名称：采购退货出库',
	`purchase_create_time` DATETIME COMMENT '采购单创建时间，格式：年月日时分秒',
	`purchaser` STRING COMMENT '采购人姓名',
	`supplier_id` BIGINT COMMENT '供应商ID',
	`supplier_name` STRING COMMENT '供应商名称',
	`relation_no` STRING COMMENT '关联单号：采购入库-预约单号；采购退货出库-采退单号',
	`warehouse_no` BIGINT COMMENT '仓库编号',
	`warehouse_name` STRING COMMENT '仓库名称，枚举值包括：广州总仓、昆明总仓、福州总仓、嘉兴总仓、华西总仓、苏州总仓、上海总仓等',
	`cost_batch` STRING COMMENT '批次编号',
	`sku_id` BIGINT COMMENT 'saas_sku_id',
	`sku` STRING COMMENT 'sku编码',
	`custom_sku_code` STRING COMMENT '自有编码',
	`title` STRING COMMENT '货品名称',
	`specification` STRING COMMENT '货品规格描述',
	`specification_unit` STRING COMMENT '规格单位，枚举值包括：包、桶、箱、件、盒、组、筐、袋、瓶、罐、块、盆、条、卷等',
	`produce_at` DATETIME COMMENT '生产日期，格式：年月日',
	`shelf_life` DATETIME COMMENT '保质期，格式：年月日',
	`biz_order_num` BIGINT COMMENT '采购单数量(/采购退货单数量)',
	`confirm_order_num` BIGINT COMMENT '预约数量（/采购退货单数量）',
	`actual_num` BIGINT COMMENT '实际入库(/实际退货出库)数量',
	`eprice_with_tax` DECIMAL(38,18) COMMENT '含税单价',
	`price_without_tax` DECIMAL(38,18) COMMENT '不含税单价',
	`tax_rate` DECIMAL(38,18) COMMENT '税率',
	`tax_price` DECIMAL(38,18) COMMENT '税额',
	`total_price_with_tax` DECIMAL(38,18) COMMENT '含税总价',
	`total_price_without_tax` DECIMAL(38,18) COMMENT '不含税总价',
	`tenant_id` BIGINT COMMENT '租户ID'
) 
COMMENT '采购对账相关库存操作信息表，记录采购入库和采购退货出库的详细操作数据'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment'='采购对账相关库存操作信息表',
	'columnar.nested.type'='true'
)
LIFECYCLE 30;