CREATE TABLE IF NOT EXISTS app_stc_warehouse_category_allocate_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	warehouse_no BIGINT COMMENT '库存仓编号，取值范围：10-170',
	warehouse_name STRING COMMENT '库存仓名称，枚举值包括：嘉兴总仓、华西总仓、重庆总仓、福州总仓、长沙总仓、昆明总仓、苏州总仓、贵阳总仓、青岛总仓、东莞总仓、美团虚拟代下单总仓、美团代加工虚拟仓、东莞冷冻总仓、嘉兴海盐总仓、南京总仓、多多买菜宁波仓、多多买菜温州仓、多多买菜揭阳仓、多多买菜武汉云仓',
	allocate_cnt BIGINT COMMENT '调拨出库数量，取值范围：1-28536',
	allocate_cost DECIMAL(38,18) COMMENT '调拨出库成本，货币金额',
	allocate_amt DECIMAL(38,18) COMMENT '调拨出库金额，货币金额',
	deliver_cnt BIGINT COMMENT '履约数量，取值范围：0-130294',
	deliver_amt DECIMAL(38,18) COMMENT '履约金额，货币金额'
) 
COMMENT '调拨出库月数据表，记录各仓库每月的调拨出库和履约情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='调拨出库月维度统计表，包含各仓库的调拨数量、成本、金额以及履约情况') 
LIFECYCLE 30;