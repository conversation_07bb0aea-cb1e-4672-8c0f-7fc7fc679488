CREATE TABLE IF NOT EXISTS app_dlv_deliver_timing_rate_df(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`timing_deliver_30_cust_cnt` BIGINT COMMENT '30天内省心送完成的客户数，取值范围：0-2917',
	`timing_deliver_30_60_cust_cnt` BIGINT COMMENT '30-60天内省心送完成的客户数，取值范围：0-2412',
	`timing_deliver_60_90_cust_cnt` BIGINT COMMENT '60-90天内省心送完成的客户数，取值范围：0-1697',
	`timing_deliver_90_cust_cnt` BIGINT COMMENT '90天以上省心送完成的客户数，取值范围：0-1802',
	`timing_deliver_30_origin_amt` DECIMAL(38,18) COMMENT '30天内省心送完成应付总金额',
	`timing_deliver_30_60_origin_amt` DECIMAL(38,18) COMMENT '30-60天内省心送完成应付总金额',
	`timing_deliver_60_90_origin_amt` DECIMAL(38,18) COMMENT '60-90天内省心送完成应付总金额',
	`timing_deliver_90_origin_amt` DECIMAL(38,18) COMMENT '90天以上省心送完成应付总金额',
	`timing_deliver_30_real_amt` DECIMAL(38,18) COMMENT '30天内省心送完成实付总金额',
	`timing_deliver_30_60_real_amt` DECIMAL(38,18) COMMENT '30-60天内省心送完成实付总金额',
	`timing_deliver_60_90_real_amt` DECIMAL(38,18) COMMENT '60-90天内省心送完成实付总金额',
	`timing_deliver_90_real_amt` DECIMAL(38,18) COMMENT '90天以上省心送完成实付总金额',
	`timing_order_cust_cnt` BIGINT COMMENT '当日省心送客户数，取值范围：1-6246',
	`timing_order_origin_amt` DECIMAL(38,18) COMMENT '当日省心送应付总金额',
	`timing_order_real_amt` DECIMAL(38,18) COMMENT '当日省心送实付总金额'
) 
COMMENT '完成配送省心送明细数据表，统计不同时间段的省心送业务完成情况，包括客户数和金额指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据计算日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='省心送业务完成情况统计表，按不同时间段维度统计客户完成数和金额指标') 
LIFECYCLE 30;