CREATE TABLE IF NOT EXISTS app_cust_grade_df(
	cust_id BIGINT COMMENT '客户ID，唯一标识客户的编号',
	cust_name STRING COMMENT '客户名称，客户的企业或店铺名称',
	dlv_real_amt DECIMAL(38,18) COMMENT '当前月累计配送实付金额（积分），精确到小数点后18位',
	grade BIGINT COMMENT '当前等级：0-普通客户，1-银牌客户，2-金牌客户，3-钻石客户',
	next_grade BIGINT COMMENT '下一等级：0-普通客户，1-银牌客户，2-金牌客户，3-钻石客户',
	date_tag STRING COMMENT '日期标签，格式为yyyyMMdd，表示数据对应的业务日期'
) 
COMMENT '客户等级表，记录客户的等级信息和配送金额数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='客户等级分析表，用于客户分层和等级管理') 
LIFECYCLE 30;