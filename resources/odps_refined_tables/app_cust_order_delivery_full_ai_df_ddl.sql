CREATE TABLE IF NOT EXISTS app_cust_order_delivery_full_ai_df(
	`cust_id` BIGINT COMMENT '客户ID',
	`cust_name` STRING COMMENT '客户名称',
	`cust_source` STRING COMMENT '客户来源，枚举值：XM-SaaS,XM-小满',
	`cust_phone` STRING COMMENT '客户手机号',
	`register_time` STRING COMMENT '注册时间，格式：yyyy-MM-dd HH:mm:ss',
	`register_province` STRING COMMENT '注册省份',
	`register_city` STRING COMMENT '注册城市',
	`register_area` STRING COMMENT '注册区域',
	`register_address` STRING COMMENT '注册详细地址',
	`cust_type` STRING COMMENT '客户类型，枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他,水果/果切/榨汁店',
	`bd_id` BIGINT COMMENT 'BD ID',
	`bd_name` STRING COMMENT 'BD姓名',
	`m1_name` STRING COMMENT 'M1管理者姓名',
	`m2_name` STRING COMMENT 'M2管理者姓名',
	`m3_name` STRING COMMENT 'M3管理者姓名',
	`zone_name` STRING COMMENT '销售区域名称',
	`cust_group` STRING COMMENT '客户分组类型，枚举值：大客户,平台客户,批发客户',
	`brand_id` BIGINT COMMENT '品牌ID',
	`brand_name` STRING COMMENT '品牌名称',
	`order_no` STRING COMMENT '订单编号',
	`order_type` STRING COMMENT '订单类型，枚举值：是-省心送,否-非省心送',
	`order_status` BIGINT COMMENT '订单状态，枚举值：1-待支付,2-待配送,3-待收货,6-已收货,8-已退款,10-支付中断超时关闭,11-已撤销,12-待支付尾款,13-尾款支付超时,14-手动关闭,15-人工退款中',
	`order_item_status` BIGINT COMMENT '订单项状态，枚举值：1-待支付,2-待配送,3-待收货,6-已收货,8-已退款,10-支付中断超时关闭,11-已撤销,12-待支付尾款,13-尾款支付超时,14-手动关闭,15-人工退款中',
	`order_time` DATETIME COMMENT '下单时间，格式：yyyy-MM-dd HH:mm:ss',
	`sku_id` STRING COMMENT '商品SKU ID',
	`sku_type` STRING COMMENT '商品类型，枚举值：0-自营,2-全品类',
	`spu_id` BIGINT COMMENT '商品SPU ID',
	`sku_no` STRING COMMENT '商品SKU编号',
	`spu_name` STRING COMMENT '商品名称',
	`category1` STRING COMMENT '商品一级类目',
	`category2` STRING COMMENT '商品二级类目',
	`category3` STRING COMMENT '商品三级类目',
	`category4` STRING COMMENT '商品四级类目',
	`origin_total_amt` DECIMAL(38,18) COMMENT '订单应付总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '订单实付总金额',
	`sku_cnt` BIGINT COMMENT '商品数量（不含赠品）',
	`gift_cnt` BIGINT COMMENT '赠品数量',
	`delivery_date` DATETIME COMMENT '履约日期，格式：yyyy-MM-dd HH:mm:ss',
	`finished_date` DATETIME COMMENT '履约完成日期，格式：yyyy-MM-dd HH:mm:ss',
	`delivery_status` BIGINT COMMENT '履约状态，枚举值：6-已完成',
	`delivery_sku_cnt` BIGINT COMMENT '本次履约商品数量',
	`delivery_total_sku_cnt` BIGINT COMMENT '累计履约商品数量',
	`no_delivery_sku_cnt` BIGINT COMMENT '剩余待履约商品数量',
	`no_delivery_day_cnt` BIGINT COMMENT '剩余履约天数',
	`dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
	`dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
	`dlv_sku_weight` DECIMAL(38,18) COMMENT '履约商品重量（kg）',
	`warehouse_no` BIGINT COMMENT '库存仓库ID',
	`warehouse_name` STRING COMMENT '库存仓库名称',
	`store_no` BIGINT COMMENT '城配仓库ID',
	`area_name` STRING COMMENT '城配仓库区域名称',
	`point_id` BIGINT COMMENT '配送点位ID',
	`delivery_path_id` BIGINT COMMENT '配送线路ID'
) 
COMMENT 'AI小助手底层数据表，包含客户信息、订单信息、商品信息、履约配送信息等完整数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='AI小助手底层数据表，用于支持AI小助手的客户服务、订单跟踪、履约配送等业务场景') 
LIFECYCLE 30;