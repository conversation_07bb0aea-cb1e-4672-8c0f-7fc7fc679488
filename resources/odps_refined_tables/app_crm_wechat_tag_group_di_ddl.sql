CREATE TABLE IF NOT EXISTS app_crm_wechat_tag_group_di(
	group_name STRING COMMENT '标签组名称，枚举值包括：企微标签组、全平台热门商品(鲜果)、自营品牌热门商品、全平台热门商品(乳制品)、全品类热门商品、全平台热门商品(其他)等',
	merchant_label STRING COMMENT '客户标签，枚举值包括：月活老客户、月活新客户、活跃客户、羊场客户、新客N0、首单N1、复购N2、成长A1、成长A2、成长A3、优质S1、优质S2、下降B1、下降B2、复活W、睡眠L1、流失L3等200+个标签值',
	type BIGINT COMMENT '变动类型：0-新增标签；1-删除标签；2-不变标签',
	day_tag STRING COMMENT '数据日期，格式为yyyyMMdd，表示年月日',
	rank BIGINT COMMENT '组内排序，数值型字段，取值范围1-39'
) 
COMMENT 'CRM企业微信标签组表，记录企业微信客户标签的分组信息和变动情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='CRM企业微信标签组表，包含标签组名称、客户标签、变动类型等信息') 
LIFECYCLE 30;