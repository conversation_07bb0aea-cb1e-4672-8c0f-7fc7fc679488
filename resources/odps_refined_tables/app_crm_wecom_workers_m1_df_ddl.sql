CREATE TABLE IF NOT EXISTS app_crm_wecom_workers_m1_df(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `m1_name` STRING COMMENT '城市负责人名称（M1），即销售主管',
    `m2_name` STRING COMMENT '区域负责人名称（M2），即销售经理',
    `m3_name` STRING COMMENT '部门负责人名称（M3），即销售总监',
    `region` STRING COMMENT '大区名称，取值范围：华中大区、川渝大区、上海大区、苏皖大区、山东大区、虚拟区域、云贵桂大区、华南一区、福泉、华南二区、浙江大区、四川',
    `zz_bd_cnt` BIGINT COMMENT '在职销售数量，统计当前在职的销售人员数量',
    `lz_bd_cnt` BIGINT COMMENT '离职销售数量，统计已离职的销售人员数量',
    `zz_jh_bd_cnt` BIGINT COMMENT '在职且激活企微销售数量，统计在职且已激活企业微信的销售人员数量',
    `lz_jh_bd_cnt` BIGINT COMMENT '离职且激活企微销售数量，统计离职但曾经激活过企业微信的销售人员数量'
)
COMMENT '销售企微激活状态看板，用于监控销售人员的企业微信激活状态和在职离职情况'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '销售企微激活状态看板表，包含销售人员层级关系、区域分布和企微激活状态统计',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;