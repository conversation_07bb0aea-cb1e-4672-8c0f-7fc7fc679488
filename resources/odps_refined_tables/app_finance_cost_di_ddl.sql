CREATE TABLE IF NOT EXISTS app_finance_cost_di(
	`date` STRING COMMENT '业务日期，格式为yyyyMMdd，表示数据对应的业务发生日期',
	`service_area` STRING COMMENT '大区：云南、华东、华中、山东、广西、福建、西南、贵州、未知',
	`warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识，取值范围2-155',
	`warehouse_name` STRING COMMENT '库存仓名称：昆明总仓、上海总仓、嘉兴总仓、苏州总仓、长沙总仓、青岛总仓、南宁总仓、东莞总仓、东莞冷冻总仓、嘉兴海盐总仓、南京总仓、济南总仓、武汉总仓、福州总仓、华西总仓、重庆总仓、贵阳总仓',
	`category1` STRING COMMENT '商品一级类目：鲜果-新鲜水果类商品、乳制品-奶制品类商品、其他-其他类别商品',
	`sell_cost_amt` DECIMAL(38,18) COMMENT '含税销售成本，包含增值税的成本金额',
	`damage_cost_amt` DECIMAL(38,18) COMMENT '含税货损成本，商品损坏或过期产生的含税成本',
	`sample_cost_amt` DECIMAL(38,18) COMMENT '含税出样成本，样品展示产生的含税成本',
	`stocktake_cost_amt` DECIMAL(38,18) COMMENT '含税盘盈盘亏成本，库存盘点差异产生的含税成本',
	`sell_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税销售成本，扣除增值税后的净成本金额',
	`damage_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税货损成本，商品损坏或过期产生的不含税成本',
	`sample_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税出样成本，样品展示产生的不含税成本',
	`stocktake_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税盘盈盘亏成本，库存盘点差异产生的不含税成本'
) 
COMMENT '财务口径收入数据表，记录各仓库按商品类目划分的销售成本、货损成本、出样成本和盘点成本等财务数据，包含含税和不含税两种口径'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='财务成本明细表，用于财务分析和成本核算') 
LIFECYCLE 30;