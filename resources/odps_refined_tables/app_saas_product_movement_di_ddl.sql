```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_product_movement_di` (
  `tenant_id` BIGINT COMMENT '租户ID，取值范围：2-123',
  `type` BIGINT COMMENT '时间标签类型：1-日，2-周，3-月',
  `time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd（年月日）',
  `on_sale_num` BIGINT COMMENT '在售商品数，取值范围：0-1645',
  `pay_success_num` BIGINT COMMENT '支付成功商品数，取值范围：0-166',
  `sale_rate` DECIMAL(38,18) COMMENT '动销率（支付成功商品数/在售商品数），百分比值',
  `last_on_sale_num` BIGINT COMMENT '上个周期在售商品数，取值范围：0-1645',
  `on_sale_chain` DECIMAL(38,18) COMMENT '在售环比增长率，百分比值',
  `last_pay_success_num` BIGINT COMMENT '上个周期支付成功商品数，取值范围：0-130',
  `pay_success_chain` DECIMAL(38,18) COMMENT '支付成功环比增长率，百分比值',
  `last_sale_rate` DECIMAL(38,18) COMMENT '上个周期动销率，百分比值',
  `sale_rate_chain` DECIMAL(38,18) COMMENT '动销环比增长率，百分比值',
  `warehouse_type` BIGINT COMMENT '归属类型：0-自营品，1-三方品',
  `delivery_type` BIGINT COMMENT '配送方式：0-品牌方配送，1-三方配送',
  `goods_type` BIGINT COMMENT '商品类型：0-无货商品，1-报价货品，2-自营货品，3-其他类型'
)
COMMENT 'SaaS商品维度动销表，统计商品在不同时间维度的销售情况，包括在售商品数、支付成功数、动销率等核心指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS商品维度动销分析表，用于商品销售趋势分析和业务决策支持',
  'lifecycle' = '30'
);
```