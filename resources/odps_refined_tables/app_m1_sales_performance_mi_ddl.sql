```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_m1_sales_performance_mi` (
  `months` STRING COMMENT '月份，格式：yyyyMM，示例：202509',
  `m1` STRING COMMENT 'M1管理者姓名，取值范围：肖时煌、骆婷婷、罗慧、陶京龙、陈俊生、林献、韦贵丰、冯朝皇、李光远、蒋柳选、李钱程、翟远方、唐宽、习燕庆、陈忠良、王业鼎、赵奎、陈露露、张茂权、张浩亮、葛世豪、王金浩、姜浪、汪林俊、刘大鹏',
  `m2` STRING COMMENT 'M2管理者姓名，取值范围：陈欲豪、林金秋、姜浪、桂少达、孙日达、翟远方、彭琨、孙军杰、赵奎',
  `m3` STRING COMMENT 'M3管理者姓名，取值范围：孙日达、吕建杰',
  `zone_name` STRING COMMENT '销售区域名称，取值范围：东莞、佛山、厦门、四川、大粤西、广州、广西、徽京、无锡、昆明、杭州、杭州湾、武汉、江西、济南、浙南、浦东、浦西、深圳、福泉、苏北、苏州、贵阳、重庆、长沙、青岛',
  `region` STRING COMMENT '销售大区名称，取值范围：华南大区、闽桂大区、西南大区、苏皖大区、昆明区域、浙江大区、华中大区、山东大区、上海大区',
  `category` STRING COMMENT '销售类目，取值范围：安佳铁塔、鲜果、乳制品(不含AT)和其他',
  `gmv_target` DECIMAL(38,18) COMMENT '实付GMV目标金额，单位：元',
  `real_total_amt` DECIMAL(38,18) COMMENT '累计实付GMV金额，单位：元',
  `real_total_amt_last_1d` DECIMAL(38,18) COMMENT '当天实付GMV金额，单位：元',
  `real_total_amt_last_2d` DECIMAL(38,18) COMMENT '前一天实付GMV金额，单位：元',
  `time_coming_rate` DECIMAL(38,18) COMMENT '时间进度完成率，小数形式表示百分比，示例：0.733333表示73.33%'
) 
COMMENT '平台销售M1维度达成表现表，记录各M1管理者的销售业绩达成情况，包括GMV目标和实际完成情况'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区日期，格式：yyyyMMdd，示例：20250922'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '平台销售M1维度达成表现表',
  'lifecycle' = '30'
);
```