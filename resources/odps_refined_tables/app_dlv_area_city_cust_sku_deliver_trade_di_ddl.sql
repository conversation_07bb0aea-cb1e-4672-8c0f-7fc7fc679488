```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_area_city_cust_sku_deliver_trade_di`(
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
  `large_area_id` BIGINT COMMENT '运营服务大区ID，唯一标识一个大区',
  `large_area_name` STRING COMMENT '运营服务大区名称，如：苏南大区、杭州大区等',
  `city_id` BIGINT COMMENT '运营服务区ID，唯一标识一个城市服务区',
  `city_name` STRING COMMENT '运营服务区名称，如：苏州、湖州、长沙普冷等',
  `cust_id` BIGINT COMMENT '客户ID，唯一标识一个客户',
  `cust_name` STRING COMMENT '客户名称，如：辣三多宽窄巷子成都串串香木渎店、光的书局广德店等',
  `cust_type` STRING COMMENT '客户类型，枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、西餐披萨、其他',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位的唯一标识',
  `spu_id` BIGINT COMMENT 'SPU ID，标准产品单位的唯一标识',
  `spu_name` STRING COMMENT '商品名称，如：青凯特芒、优诺高品质冷藏牛乳等',
  `sku_disc` STRING COMMENT '商品描述，包含规格、等级、重量等信息',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送交易应付GMV，单位为元',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送交易实付GMV，单位为元',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV，单位为元',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV，单位为元',
  `timing_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV，单位为元',
  `timing_dlv_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付GMV，单位为元',
  `timing_dlv_cost_amt` DECIMAL(38,18) COMMENT '省心送成本，单位为元',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，单位为元',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，单位为元',
  `timing_no_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '省心送未履约应付GMV，单位为元',
  `timing_no_dlv_real_total_amt` DECIMAL(38,18) COMMENT '省心送未履约实付GMV，单位为元',
  `timing_no_dlv_sku_cnt` BIGINT COMMENT '省心送未履约商品数量',
  `timing_dlv_7_day_sku_cnt` BIGINT COMMENT '省心送未来7天预约配送商品数量',
  `timing_dlv_14_day_sku_cnt` BIGINT COMMENT '省心送未来14天预约配送商品数量'
) 
COMMENT '运营服务区+客户+库存仓 SKU 省心送交易明细表，记录省心送业务的交易和履约明细数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
   'comment'='省心送业务交易明细事实表，包含运营服务区、客户、商品维度的交易和履约指标') 
LIFECYCLE 30;
```