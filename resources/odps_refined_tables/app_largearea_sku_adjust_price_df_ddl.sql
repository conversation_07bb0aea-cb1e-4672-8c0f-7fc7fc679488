CREATE TABLE IF NOT EXISTS app_largearea_sku_adjust_price_df(
	large_area_name STRING COMMENT '大区名称，枚举值包括：广州大区、杭州大区、上海大区等',
	sku_id STRING COMMENT 'SKU ID，商品唯一标识',
	spu_name STRING COMMENT '商品名称',
	adjust_price_time DATETIME COMMENT '调价时间，格式为年月日时分秒',
	original_price DECIMAL(38,18) COMMENT '调价前价格',
	price DECIMAL(38,18) COMMENT '调价后价格',
	max_sale_price DECIMAL(38,18) COMMENT '近30天最高价格',
	min_sale_price DECIMAL(38,18) COMMENT '近30天最低价格'
) 
COMMENT '商品调价记录表，记录各大区SKU的价格调整历史数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商品调价记录表，包含各大区SKU的价格调整记录、历史最高最低价格等信息') 
LIFECYCLE 30;