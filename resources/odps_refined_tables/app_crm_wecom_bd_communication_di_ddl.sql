```sql
CREATE TABLE IF NOT EXISTS app_crm_wecom_bd_communication_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
    `bd_id` BIGINT COMMENT '销售ID，唯一标识一个销售人员',
    `bd_name` STRING COMMENT '销售姓名',
    `m1_name` STRING COMMENT '城市负责人名称（M1管理者），即销售的直接上级',
    `m2_name` STRING COMMENT '区域负责人名称（M2管理者），即M1的直接上级',
    `m3_name` STRING COMMENT '部门负责人名称（M3管理者），即M2的直接上级',
    `region` STRING COMMENT '大区名称，取值范围：上海大区、苏皖大区、华南一区、华中大区、浙江大区、山东大区、云贵桂大区、福泉、华南二区、四川、虚拟区域',
    `conversation_count` BIGINT COMMENT '聊天消息数，统计销售与客户的对话次数',
    `message_count` BIGINT COMMENT '消息总数，包括发送和接收的所有消息数量',
    `answer_proportion` STRING COMMENT '回复消息占比，格式为百分比字符串（如"0.00%"），表示销售回复客户消息的比例',
    `answer_duration` BIGINT COMMENT '回复时长，单位：分钟，表示销售回复客户消息的平均时间'
)
COMMENT '销售与客户沟通互动看板表，记录销售人员与客户的沟通互动数据，包括消息数量、回复比例和时长等指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='销售与客户沟通互动看板，用于分析销售团队的沟通效率和客户服务质量')
LIFECYCLE 30;
```