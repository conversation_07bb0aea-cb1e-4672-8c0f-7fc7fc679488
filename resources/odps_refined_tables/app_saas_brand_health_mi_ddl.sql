```sql
CREATE TABLE IF NOT EXISTS app_saas_brand_health_mi(
    `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
    `brand_alias` STRING COMMENT '品牌名称',
    `order_store_cnt` BIGINT COMMENT '交易门店数，统计周期内发生交易的门店数量',
    `all_store_cnt` BIGINT COMMENT '注册门店数，统计周期内累计注册的门店总数',
    `total_gmv` DECIMAL(38,18) COMMENT '总交易GMV，统计周期内所有门店的交易总额',
    `store_gmv` DECIMAL(38,18) COMMENT '店均交易GMV，统计周期内平均每个门店的交易金额',
    `order_days` BIGINT COMMENT '下单天数，统计周期内门店有下单行为的天数',
    `home_login_days` BIGINT COMMENT '后台登录天数，统计周期内门店登录后台系统的天数',
    `is_supplier` STRING COMMENT '是否完善供应商创建：是-已完善供应商创建，否-未完善供应商创建',
    `is_supplier_sku` STRING COMMENT '是否关联供应商：是-已关联供应商，否-未关联供应商',
    `is_purchases` STRING COMMENT '当月是否下采购单：是-当月有采购单，否-当月无采购单',
    `is_after_sale` STRING COMMENT '当月是否有售后：是-当月有售后记录，否-当月无售后记录',
    `is_store_log` STRING COMMENT '当月是否使用仓库模块：是-当月使用仓库模块，否-当月未使用仓库模块',
    `is_finance_log` STRING COMMENT '当月是否使用财务模块：是-当月使用财务模块，否-当月未使用财务模块',
    `tenant_id` BIGINT COMMENT '租户id，唯一标识一个租户'
)
COMMENT 'SaaS品牌健康度表，用于分析品牌在SaaS平台上的运营健康度指标，包括交易情况、系统使用情况等'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，如20250922表示2025年9月22日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='SaaS品牌健康度分析表，包含品牌交易数据、系统使用情况等核心指标',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```