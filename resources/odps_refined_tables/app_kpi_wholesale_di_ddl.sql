CREATE TABLE IF NOT EXISTS app_kpi_wholesale_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`sku_type` STRING COMMENT '商品类型；取值范围：自营/代仓',
	`order_gmv_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
	`deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
	`deliver_cust_cnt` BIGINT COMMENT '履约客户数',
	`deliver_cust_arpu` DECIMAL(38,18) COMMENT '履约ARPU(应付总金额/客户数)',
	`deliver_order_cnt` BIGINT COMMENT '履约订单数',
	`deliver_order_avg` DECIMAL(38,18) COMMENT '履约订单均价(应付总金额/订单数)',
	`deliver_cost_amt` DECIMAL(38,18) COMMENT '履约总成本',
	`deliver_gross_profit` DECIMAL(38,18) COMMENT '履约毛利润(应付总金额-总成本)',
	`deliver_gross_profit_rate` DECIMAL(38,18) COMMENT '履约毛利率'
) 
COMMENT '交易口径KPI指标日汇总表，包含交易和履约相关的关键绩效指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='交易口径KPI指标日汇总表，用于存储每日的交易和履约业务指标数据') 
LIFECYCLE 30;