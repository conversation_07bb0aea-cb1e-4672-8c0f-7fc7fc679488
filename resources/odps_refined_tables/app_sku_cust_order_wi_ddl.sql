CREATE TABLE IF NOT EXISTS app_sku_cust_order_wi(
	year STRING COMMENT '年份，格式：yyyy',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式：yyyyMMdd（年月日）',
	sunday STRING COMMENT '周日日期，格式：yyyyMMdd（年月日）',
	sku_id STRING COMMENT 'SKU编号，商品唯一标识',
	spu_name STRING COMMENT '商品名称',
	sku_disc STRING COMMENT '商品描述，包含规格、重量、等级等信息',
	cust_type STRING COMMENT '客户业态，枚举值：其他、咖啡、水果/果切/榨汁店、甜品冰淇淋、糖水/水果捞、茶饮、西餐、面包蛋糕',
	cust_cnt BIGINT COMMENT '交易客户数，统计周期内购买该SKU的客户数量',
	group_cust_cnt BIGINT COMMENT '业态总客户数，统计周期内该业态的总客户数量'
)
COMMENT '区域渗透数据表，记录各SKU在不同客户业态中的渗透情况，用于分析商品的市场覆盖度'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）')
STORED AS ALIORC
TBLPROPERTIES ('comment'='区域渗透数据分析表，包含商品销售数据和客户业态分布信息')
LIFECYCLE 30;