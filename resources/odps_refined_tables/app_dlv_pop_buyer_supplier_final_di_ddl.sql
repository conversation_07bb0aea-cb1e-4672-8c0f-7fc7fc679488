```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_pop_buyer_supplier_final_di`(
  `date` STRING COMMENT '业务日期，格式：yyyyMMdd，表示数据统计的业务日期',
  `supplier` STRING COMMENT '供应商名称，枚举值包括：李强、龙飞果业、杨志闯、汪高洋、刘东等',
  `buyer` STRING COMMENT '买手名称，枚举值包括：南乔、龙飞、大表哥、村花、山峰等',
  `sku_id` STRING COMMENT '商品SKU编号，唯一标识商品的编码',
  `order_sku_cnt` BIGINT COMMENT '下单商品数量，统计周期内该供应商-买手-SKU组合的下单商品件数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '下单商品总毛重（斤），统计周期内该供应商-买手-SKU组合的下单商品总重量',
  `order_total_amt` DECIMAL(38,18) COMMENT '供货总金额（元），统计周期内该供应商-买手-SKU组合的供货总金额',
  `take_total_amt` DECIMAL(38,18) COMMENT '提货费用（元），统计周期内该供应商-买手-SKU组合的提货相关费用',
  `after_sale_short_sku_cnt` BIGINT COMMENT '售后缺货商品数，统计周期内该供应商-买手-SKU组合的售后缺货商品数量',
  `after_sale_short_sku_weight` DECIMAL(38,18) COMMENT '售后缺货商品总毛重（斤），统计周期内该供应商-买手-SKU组合的售后缺货商品总重量',
  `after_sale_short_total_amt` DECIMAL(38,18) COMMENT '售后缺货总金额（元），统计周期内该供应商-买手-SKU组合的售后缺货涉及金额',
  `after_sale_quality_sku_cnt` BIGINT COMMENT '售后质量商品数，统计周期内该供应商-买手-SKU组合的因质量问题产生的售后商品数量',
  `after_sale_quality_sku_weight` DECIMAL(38,18) COMMENT '售后质量商品总毛重（斤），统计周期内该供应商-买手-SKU组合的因质量问题产生的售后商品总重量',
  `after_sale_quality_total_amt` DECIMAL(38,18) COMMENT '售后质量总金额（元），统计周期内该供应商-买手-SKU组合的因质量问题产生的售后涉及金额',
  `commission_after_sale_amt` DECIMAL(38,18) COMMENT '售后缺货金额/(1+佣金比例)，售后缺货金额扣除佣金后的净额',
  `commission_after_sale_quality_amt` DECIMAL(38,18) COMMENT '售后质量金额/(1+佣金比例)，售后质量金额扣除佣金后的净额'
) 
COMMENT '供应商买手对账汇总表，按供应商-买手-SKU维度统计下单、售后等对账相关数据，用于供应商和买手之间的对账结算'
PARTITIONED BY (
  `ds` STRING COMMENT '数据分区字段，格式：yyyyMMdd，表示数据加载的日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '供应商买手对账汇总表，提供供应商和买手之间的对账结算数据',
  'columnar.nested.type' = 'true'
) 
LIFECYCLE 30;
```