CREATE TABLE IF NOT EXISTS app_saas_order_statement_summary_di(
  `tenant_id` BIGINT COMMENT '租户ID，取值范围：7-123',
  `time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd，表示年月日',
  `total_price` DECIMAL(38,18) COMMENT '总金额（元）',
  `wechat_pay_total_price` DECIMAL(38,18) COMMENT '总金额（微信支付）（元）',
  `bill_balance_pay_total_price` DECIMAL(38,18) COMMENT '总金额（账期+余额支付）（元）',
  `supply_total_price` DECIMAL(38,18) COMMENT '直供货品总金额（元）',
  `supply_delivery_fee` DECIMAL(38,18) COMMENT '供应商运费（元）',
  `refund_price_deducted_supply` DECIMAL(38,18) COMMENT '等比换算后采购售后金额合计（元）',
  `total_difference` DECIMAL(38,18) COMMENT '商品售价与采购价倒挂差额（元）',
  `goods_agent_fee` DECIMAL(38,18) COMMENT '代仓费（元）',
  `goods_agent_refund_fee_supply_responsibility` DECIMAL(38,18) COMMENT '售后明细表_已到货售后_鲜沐责任_代仓费用合计（元）',
  `sales_and_supply_difference` DECIMAL(38,18) COMMENT '鲜沐直供货品_售价与采购价差额总计（未剔售后）（元）',
  `refund_sales_and_supply_difference` DECIMAL(38,18) COMMENT '有退款的_鲜沐直供货品的售价与采购差额总计（元）',
  `gross_profit` DECIMAL(38,18) COMMENT '鲜沐直供货品售价与采购价差额总计（毛利润）（元）',
  `wechat_pay_sales_and_supply_difference_deducted_refund` DECIMAL(38,18) COMMENT '销售与采购差额（剔除售后）（微信支付）（元）',
  `bill_balance_pay_sales_and_supply_difference_deducted_refund` DECIMAL(38,18) COMMENT '销售与采购差额（剔除售后）（账期支付+余额支付）（元）',
  `order_count` BIGINT COMMENT '订单数，取值范围：1-122',
  `order_item_count` BIGINT COMMENT 'SKU订单件数合计，取值范围：3-413',
  `wechat_pay_total_price_deducted_delivery_fee` DECIMAL(38,18) COMMENT '采购应付总计（不含配送费）（微信支付）（元）',
  `bill_balance_pay_total_price_deducted_delivery_fee` DECIMAL(38,18) COMMENT '采购应付总计（不含配送费）（账期和余额支付）（元）',
  `wechat_pay_supply_delivery_fee` DECIMAL(38,18) COMMENT '供应商配送费（微信支付）（元）',
  `bill_balance_pay_supply_delivery_fee` DECIMAL(38,18) COMMENT '供应商配送费（账期和余额支付）（元）'
) 
COMMENT 'SAAS对账单-对账单概要表（近15天数据），包含租户维度的订单金额、支付方式、采购成本、利润等汇总统计信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='SAAS对账单概要统计表，按租户和时间维度汇总订单财务数据') 
LIFECYCLE 30;