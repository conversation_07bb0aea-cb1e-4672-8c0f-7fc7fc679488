CREATE TABLE IF NOT EXISTS app_warehouse_estimated_consumption_df(
    `pd_id` BIGINT COMMENT '产品ID，唯一标识产品',
    `sku_id` STRING COMMENT 'SKU编码，唯一标识库存单位，取值范围包括：1000063425545、1000432818285、1000781016035、100102、1001052874538等',
    `warehouse_no` BIGINT COMMENT '仓库编号，唯一标识仓库，取值范围：2-169',
    `view_date` DATETIME COMMENT '日期，格式为年月日时分秒，表示数据查看日期',
    `estimated_sales` BIGINT COMMENT '预估销量，预测的销售数量',
    `estimated_transfer_out` BIGINT COMMENT '预估调拨量，预测的调拨出库数量',
    `consumption` BIGINT COMMENT '预估消耗量，预测的总消耗数量',
    `sale_cnt` DECIMAL(38,18) COMMENT '预销出库量(不取整)，精确到18位小数的销售出库预测',
    `allocation_cnt` DECIMAL(38,18) COMMENT '预调出库量(不取整)，精确到18位小数的调拨出库预测',
    `consumption_a` DECIMAL(38,18) COMMENT '预估消耗量(不取整)，精确到18位小数的总消耗预测',
    `transfer_order_plan_out_quantity` DECIMAL(38,18) COMMENT '调拨计划出数量，精确到18位小数的调拨计划出库数量'
)
COMMENT '库存仓预估消耗表，包含仓库库存的各类预估消耗数据，包括销量、调拨量和总消耗量等预测指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='库存仓预估消耗分析表，用于仓库库存管理和消耗预测')
LIFECYCLE 30;