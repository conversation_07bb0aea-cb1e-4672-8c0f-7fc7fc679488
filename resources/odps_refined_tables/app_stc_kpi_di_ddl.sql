CREATE TABLE IF NOT EXISTS app_stc_kpi_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `check_sku_cnt` BIGINT COMMENT '抽检数量，统计抽检的商品件数',
    `in_bound_sku_cnt` BIGINT COMMENT '入库数量，统计入库的商品件数',
    `check_rate` DECIMAL(38,18) COMMENT '抽检比例，抽检数量/入库数量的比例',
    `back_order_cnt` BIGINT COMMENT '退货总单数，统计退货订单总数',
    `finish_order_cnt` BIGINT COMMENT '已完成单数，统计已完成的订单数量',
    `back_finish_rate` DECIMAL(38,18) COMMENT '退货完结率，已完成退货单数/退货总单数的比例',
    `damage_amt` DECIMAL(38,18) COMMENT '货损金额，统计货损的总金额',
    `damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额——仓配责，仓配责任导致的货损金额',
    `sale_amt` DECIMAL(38,18) COMMENT '销售金额，统计销售的总金额',
    `error_sku_cnt` BIGINT COMMENT '错误件数，统计错误的商品件数',
    `error_sku_cnt_wah` BIGINT COMMENT '错误件数_仓配责，仓配责任导致的错误件数',
    `error_cust_cnt` BIGINT COMMENT '错误客户数，统计出现错误的客户数量',
    `error_cust_cnt_wah` BIGINT COMMENT '错误客户数_仓配责，仓配责任导致的错误客户数',
    `cust_cnt` BIGINT COMMENT '活跃客户数，统计活跃的客户数量',
    `sku_cnt` BIGINT COMMENT '配送件数，统计配送的商品件数',
    `total_point_cnt` BIGINT COMMENT '总点位数，统计总的配送点位数量',
    `point_cnt` BIGINT COMMENT '点位数（不含喜茶），统计除喜茶外的配送点位数量',
    `no_in_time_point_cnt` BIGINT COMMENT '不及时点位数（不含喜茶），统计除喜茶外不及时的配送点位数量',
    `delay_time_point_cnt_2` BIGINT COMMENT '前置2小时不及时点位数（不含喜茶），统计除喜茶外前置2小时不及时的配送点位数量',
    `out_time` DECIMAL(38,18) COMMENT '配送超时时间（不含喜茶），统计除喜茶外配送超时的时间',
    `delay_time_2` DECIMAL(38,18) COMMENT '前置2小时配送超时时间（不含喜茶），统计除喜茶外前置2小时配送超时的时间',
    `path_cnt` BIGINT COMMENT '线路数（不含喜茶），统计除喜茶外的配送线路数量',
    `delay_path_cnt` BIGINT COMMENT '出库不及时线路数（不含喜茶），统计除喜茶外出库不及时的线路数量',
    `out_distance_point_cnt` BIGINT COMMENT '超距离点位数（含喜茶），统计超距离的配送点位数量（包含喜茶）',
    `after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额，统计售后处理的总金额',
    `after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责，仓配责任导致的售后金额',
    `after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责，采购责任导致的售后金额',
    `after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责，品控责任导致的售后金额',
    `after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责，采购和品控共同责任导致的售后金额',
    `after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责，其他责任导致的售后金额',
    `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额，统计配送相关的销售总金额',
    `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额，统计优惠券使用的总金额',
    `origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV，统计应付的商品交易总额',
    `real_total_amt` DECIMAL(38,18) COMMENT '实付GMV，统计实付的商品交易总额',
    `storage_amt` DECIMAL(38,18) COMMENT '仓储成本，统计仓储相关的成本金额',
    `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，统计干线运输相关的成本金额',
    `deliver_amt` DECIMAL(38,18) COMMENT '配送成本，统计配送相关的成本金额',
    `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本，统计采购自提相关的成本金额',
    `other_amt` DECIMAL(38,18) COMMENT '其他成本，统计其他相关的成本金额',
    `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，统计调拨相关的成本金额',
    `cost_amt` DECIMAL(38,18) COMMENT '商品成本，统计商品相关的成本金额'
) 
COMMENT '仓配KPI汇总表，包含仓储配送相关的关键绩效指标数据，用于监控和分析仓配业务表现'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='仓配KPI汇总表，包含仓储配送相关的关键绩效指标数据') 
LIFECYCLE 30;