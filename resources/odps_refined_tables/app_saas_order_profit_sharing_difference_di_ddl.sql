```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_order_profit_sharing_difference_di` (
  `tenant_id` BIGINT COMMENT '租户ID，标识业务租户',
  `time_tag` STRING COMMENT '时间标签，格式为yyyyMMdd，表示业务日期',
  `order_id` BIGINT COMMENT '订单ID，唯一标识订单',
  `order_no` STRING COMMENT '订单编号，业务系统生成的订单号',
  `store_name` STRING COMMENT '门店名称，下单的门店名称',
  `order_address` STRING COMMENT '收货地址，订单的配送地址',
  `order_phone` STRING COMMENT '收货人联系电话，订单收货人的手机号码',
  `order_time` DATETIME COMMENT '下单时间，格式为yyyy-MM-dd HH:mm:ss，订单创建时间',
  `pay_time` DATETIME COMMENT '支付时间，格式为yyyy-MM-dd HH:mm:ss，订单支付完成时间',
  `pay_type` BIGINT COMMENT '支付方式：1-线上支付，2-账期支付，3-余额支付',
  `profit_sharing_success_time` DATETIME COMMENT '分账成功时间，格式为yyyy-MM-dd HH:mm:ss，分账操作完成时间',
  `total_item_price` DECIMAL(38,18) COMMENT '商品销售总价（含运费），单位：元，保留18位小数',
  `total_goods_price` DECIMAL(38,18) COMMENT '货品采购总价（含运费），单位：元，保留18位小数',
  `difference` DECIMAL(38,18) COMMENT '差额，销售总价与采购总价的差值，单位：元，保留18位小数',
  `supplier_id` BIGINT COMMENT '供应商ID，标识商品供应商'
)
COMMENT 'SAAS对账单-订单分账差额表，记录订单分账相关的差额计算信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SAAS对账单-订单分账差额明细表，用于记录订单分账相关的差额计算和核对',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```