CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_history_cust_category_performance_mi` (
  `bd_id` BIGINT COMMENT 'BD ID',
  `bd_name` STRING COMMENT '最新归属BD姓名',
  `is_test_bd` BIGINT COMMENT '是否测试BD：1-是，0-否',
  `bd_region` STRING COMMENT '大区：浙江大区、上海大区、苏皖大区、华中大区、华南大区、山东大区、闽桂大区',
  `bd_work_zone` STRING COMMENT '区域：杭州、浙南、杭州湾、浦东、徽京、苏州、浦西、武汉、江西、长沙、广州、深圳、青岛、无锡、福泉、苏北',
  `cust_id` BIGINT COMMENT '客户ID',
  `cust_name` STRING COMMENT '客户名称',
  `cust_type` STRING COMMENT '客户类型：存量-存量客户，新增-新增客户',
  `cust_type_detail` STRING COMMENT '客户类型详情：老客-老客户',
  `order_source` STRING COMMENT '订单来源：鲜沐-鲜沐平台，SAAS-SAAS平台',
  `category1` STRING COMMENT '一级类目：鲜果-鲜果类，乳制品-乳制品类，其他-其他类目',
  `spu_group` STRING COMMENT '推广品类：柠檬、酷盖纯牛奶、Protag纯牛奶、芒果、草莓、蓝莓、Protag常温生椰乳、西瓜、C味调制芋泥（荔浦）、C味原味波波晶球（轻盈版）、象牌苏打水、澄善速冻百香果浆、C味原味波波晶球、澄善HPP冷冻芒果浆、C味三色大芋圆、C味椰果果粒、C味马蹄爆爆珠、小台农芒果冻肉、C味圆形小芋圆、C味荔浦芋头块、牛油果、火龙果',
  `is_dlv_payment` BIGINT COMMENT '是否履约结算：1-履约结算，0-交易结算',
  `max_months` STRING COMMENT '最近履约(交易)月份，格式：yyyyMM',
  `last_months` STRING COMMENT '非本月的最近履约(交易)月份，格式：yyyyMM',
  `mtd_dlv_ori_amt` DECIMAL(38,18) COMMENT '本月履约(交易)应付GMV',
  `mtd_dlv_real_amt` DECIMAL(38,18) COMMENT '本月履约(交易)实付GMV',
  `mtd_dlv_sku_cnt` BIGINT COMMENT '本月履约(交易)件数',
  `mtd_big_sku_cnt` DECIMAL(38,18) COMMENT '本月大规格履约(交易)件数',
  `is_completed` BIGINT COMMENT '是否完成该品类任务：1-完成，0-未完成',
  `last_dlv_ori_amt` DECIMAL(38,18) COMMENT '非本月履约应付GMV_最近月份',
  `last_dlv_real_amt` DECIMAL(38,18) COMMENT '非本月履约实付GMV_最近月份',
  `last_dlv_sku_cnt` BIGINT COMMENT '非本月履约件数_最近月份',
  `last_big_sku_cnt` DECIMAL(38,18) COMMENT '非本月大规格履约件数_最近月份'
)
COMMENT '拉新客户推广品类本月表现表，记录拉新客户在不同推广品类上的月度业绩表现数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，数据日期，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '拉新客户推广品类月度表现分析表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 365;