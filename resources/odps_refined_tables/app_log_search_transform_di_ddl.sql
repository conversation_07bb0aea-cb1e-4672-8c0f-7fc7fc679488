CREATE TABLE IF NOT EXISTS app_log_search_transform_di(
    `date` STRING COMMENT '日期，格式：yyyyMMdd，表示年月日',
    `query` STRING COMMENT '搜索词:整体/草莓等，枚举值包括：整体、越南椰青、安佳、0脂牛奶等商品搜索词',
    `cust_uv` BIGINT COMMENT '用户UV：独立访客数',
    `cust_pv` BIGINT COMMENT '用户PV：页面浏览量',
    `new_cust_uv` BIGINT COMMENT '新用户UV：新独立访客数',
    `sku_impression_uv` BIGINT COMMENT '商品曝光UV：看到商品的独立用户数',
    `sku_impression_pv` BIGINT COMMENT '商品曝光PV：商品被展示的次数',
    `sku_click_uv` BIGINT COMMENT '商品点击UV：点击商品的独立用户数',
    `sku_click_pv` BIGINT COMMENT '商品点击PV：商品被点击的次数',
    `sku_cart_uv` BIGINT COMMENT '商品加购UV：将商品加入购物车的独立用户数',
    `sku_cart_pv` BIGINT COMMENT '商品加购PV：商品被加入购物车的次数',
    `order_cnt` BIGINT COMMENT '下单数：订单数量',
    `order_amt` DECIMAL(38,18) COMMENT '下单金额：订单总金额',
    `order_uv` BIGINT COMMENT '下单用户UV：下单的独立用户数',
    `order_paid_cnt` BIGINT COMMENT '支付订单数：已支付的订单数量',
    `order_paid_amt` DECIMAL(38,18) COMMENT '支付金额：已支付的订单总金额',
    `order_paid_uv` BIGINT COMMENT '支付订单用户UV：支付订单的独立用户数',
    `order_paid_amt_avg` DECIMAL(38,18) COMMENT '平均支付金额：支付金额/支付订单数',
    `sku_click_uv_rate` DECIMAL(38,18) COMMENT '商品UV点击率：商品点击UV/商品曝光UV',
    `sku_click_pv_rate` DECIMAL(38,18) COMMENT '商品PV点击率：商品点击PV/商品曝光PV',
    `sku_cart_uv_rate` DECIMAL(38,18) COMMENT '商品UV加购率：商品加购UV/商品曝光UV',
    `sku_cart_pv_rate` DECIMAL(38,18) COMMENT '商品PV加购率：商品加购PV/商品曝光PV',
    `order_paid_uv_rate` DECIMAL(38,18) COMMENT '订单支付UV转化率：支付订单用户UV/用户UV'
)
COMMENT '商城搜索词转化汇总表，统计各搜索词对应的用户行为转化指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，yyyyMMdd格式，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='商城搜索词转化分析表，包含搜索词维度的用户行为转化漏斗数据')
LIFECYCLE 30;