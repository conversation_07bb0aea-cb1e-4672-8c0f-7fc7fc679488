```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_xianmu_card_statistics_df` (
  `stat_date` STRING COMMENT '统计日期，格式为yyyyMMdd，表示年月日',
  `total_balance` DECIMAL(38,18) COMMENT '鲜沐卡总余额，单位为元',
  `total_balance_weekly_ratio` DECIMAL(38,18) COMMENT '鲜沐卡总余额周环比，百分比值（%）',
  `store_count_with_balance` BIGINT COMMENT '有余额的门店数量，整数',
  `store_count_weekly_ratio` DECIMAL(38,18) COMMENT '有余额的门店数量周环比，百分比值（%）',
  `avg_store_balance` DECIMAL(38,18) COMMENT '店均余额，单位为元',
  `avg_store_balance_weekly_ratio` DECIMAL(38,18) COMMENT '店均余额周环比，百分比值（%）'
) 
COMMENT '鲜沐卡余额统计表，包含鲜沐卡余额相关的统计指标和周环比数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC  
TBLPROPERTIES ('comment'='鲜沐卡余额统计表，用于监控鲜沐卡余额变化趋势和门店分布情况') 
LIFECYCLE 30;
```