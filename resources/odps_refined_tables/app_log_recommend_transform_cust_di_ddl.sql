CREATE TABLE IF NOT EXISTS app_log_recommend_transform_cust_di(
	`date` STRING COMMENT '日期，格式：yyyyMMdd，表示年月日',
	`cust_id` BIGINT COMMENT '客户ID，唯一标识一个客户',
	`scene` STRING COMMENT '推荐场景：首页、分类页、活动页、购物车页、商品详情页、特价专区',
	`abexperiments_experiment_id` STRING COMMENT 'AB实验解析字段-experiment_id，取值范围：无',
	`abexperiments_experiment_place` STRING COMMENT 'AB实验解析字段-experiment_place，取值范围：无',
	`abexperiments_variant_id` STRING COMMENT 'AB实验解析字段-variant_id，取值范围：无',
	`is_new` STRING COMMENT '是否当日注册：是/否/None（未知）',
	`sku_impression_uv` BIGINT COMMENT '商品曝光独立访客数',
	`sku_impression_pv` BIGINT COMMENT '商品曝光页面浏览量',
	`sku_click_uv` BIGINT COMMENT '商品点击独立访客数',
	`sku_click_pv` BIGINT COMMENT '商品点击页面浏览量',
	`sku_cart_uv` BIGINT COMMENT '商品加购独立访客数',
	`sku_cart_pv` BIGINT COMMENT '商品加购页面浏览量',
	`order_cnt` BIGINT COMMENT '下单数（订单数量）',
	`order_amt` DECIMAL(38,18) COMMENT '下单金额',
	`order_paid_cnt` BIGINT COMMENT '支付订单数',
	`order_paid_amt` DECIMAL(38,18) COMMENT '支付金额',
	`order_paid_amt_avg` DECIMAL(38,18) COMMENT '平均支付金额'
) 
COMMENT '商城推荐转化汇总表，记录各推荐场景下的用户行为转化数据，包括曝光、点击、加购、下单、支付等关键指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商城推荐转化汇总表，用于分析推荐系统的效果和用户转化路径') 
LIFECYCLE 30;