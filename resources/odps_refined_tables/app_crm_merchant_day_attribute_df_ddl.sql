CREATE TABLE IF NOT EXISTS app_crm_merchant_day_attribute_df(
    `cust_id` BIGINT COMMENT '客户ID，唯一标识商户',
    `not_visited` BIGINT COMMENT '未拜访天数，从最后一次拜访至今的天数',
    `days_without_order` BIGINT COMMENT '未下单天数，从最后一次下单至今的天数',
    `merchant_lifecycle` BIGINT COMMENT '商户生命周期阶段：0-新注册，1-首单，2-非稳定期，3-稳定期',
    `order_frequency` BIGINT COMMENT '下单频率，统计周期内的下单次数',
    `timing_follow_type` BIGINT COMMENT '未完结的省心送标识：0-无省心送，1-有省心送',
    `order_cycle` BIGINT COMMENT '近三个月内平均下单周期（天数）',
    `days_without_order_follow` BIGINT COMMENT '未下单天数（与跟进相关的计算）',
    `life_cycle` STRING COMMENT '新生命周期标签：N0-新注册，N1-新客，L1-L3-流失风险等级，A1-A3-活跃等级，S1-S2-稳定等级，W-预警，B1-B2-回流等级',
    `r_value` STRING COMMENT 'R价值标签（最近一次消费）：L-低价值，R1-R4-价值等级（R4最高）',
    `f_value` STRING COMMENT 'F价值标签（消费频率）：L-低频率，F1-F4-频率等级（F4最高）',
    `m_value` STRING COMMENT 'M价值标签（消费金额）：L-低金额，M1-M4-金额等级（M4最高）',
    `days_not_logged_in` BIGINT COMMENT '未登录天数，从最后一次登录至今的天数',
    `frequency` BIGINT COMMENT '近1年平均下单周期（天数），超过60天取60天',
    `visit_count` BIGINT COMMENT '本月被拜访次数'
)
COMMENT '商户每日属性表，包含商户的基本属性、生命周期状态、价值标签和业务行为指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='商户属性分析表，用于商户分层和运营策略制定')
LIFECYCLE 30;