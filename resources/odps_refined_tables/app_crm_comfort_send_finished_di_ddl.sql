```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_comfort_send_finished_di` (
  `m_id` BIGINT COMMENT '商户ID，唯一标识一个商户',
  `order_no` STRING COMMENT '订单编号，唯一标识一个订单',
  `order_time` DATETIME COMMENT '订单生成时间，格式为年月日时分秒',
  `sku_id` STRING COMMENT '商品SKU编码，唯一标识一个商品规格',
  `pd_name` STRING COMMENT '商品名称',
  `pd_weight` STRING COMMENT '商品规格描述，如"1KG*10袋"',
  `pd_amount` BIGINT COMMENT '订单内商品数量，取值范围：2-500',
  `pay_amount` DECIMAL(38,18) COMMENT '实付总额，精确到小数点后18位',
  `area_no` BIGINT COMMENT '商户所在运营区域编号',
  `bd_id` BIGINT COMMENT '归属BD ID，公海为0，取值范围：0-1181430',
  `day_tag` STRING COMMENT '数据所在日标记，格式为yyyyMMdd',
  `m_name` STRING COMMENT '商户名称',
  `province` STRING COMMENT '省份名称，枚举值包括：上海、江苏、广东、浙江、广西壮族自治区、四川、重庆、湖南、湖北、江西、安徽、云南、山东、福建、贵州',
  `city` STRING COMMENT '城市名称',
  `area` STRING COMMENT '区县名称'
)
COMMENT 'CRM近30天配送完成的省心送订单数据，包含订单基本信息、商品信息、商户信息和地域信息'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '省心送订单完成配送记录表，用于分析配送完成情况和相关业务指标',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```