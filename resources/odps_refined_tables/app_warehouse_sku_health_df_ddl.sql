```sql
CREATE TABLE IF NOT EXISTS app_warehouse_sku_health_df(
    `date` STRING COMMENT '日期，格式为yyyyMMdd',
    `warehouse_no` BIGINT COMMENT '库存仓号',
    `warehouse_name` STRING COMMENT '库存仓名称',
    `sku_id` STRING COMMENT 'SKU编号',
    `category1` STRING COMMENT '一级类目',
    `category2` STRING COMMENT '二级类目',
    `category3` STRING COMMENT '三级类目',
    `category4` STRING COMMENT '四级类目',
    `spu_name` STRING COMMENT '商品名称',
    `sku_disc` STRING COMMENT '商品规格描述',
    `sku_propriety` STRING COMMENT 'SKU性质：常规，活动，临保，拆包，不卖，破袋',
    `sku_type` STRING COMMENT 'SKU类型：自营,代仓，代售',
    `sku_life` STRING COMMENT '生命周期状态：使用中，已删除',
    `on_sale` STRING COMMENT '上架状态：上架，下架，异常',
    `is_big_cust` STRING COMMENT '是否大客户专享：是，否',
    `origin_place` STRING COMMENT '产地',
    `is_origin` STRING COMMENT '是否国产：是，否',
    `is_new` STRING COMMENT '是否新品（SKU创建时间60天内算新品）：是，否',
    `is_180d_order` STRING COMMENT '近半年是否有动销（近180天是否有销售）：是，否',
    `abc_label` STRING COMMENT '货品ABC分类：A,B,C,无',
    `season_sign` STRING COMMENT '季节性标识：无,夏季,秋季,冬季',
    `life_cycle` STRING COMMENT '生命周期阶段：衰退低,衰退中,衰退高,稳定,成长低,成长中,成长高,长尾,新品,无',
    `grow_coe` DECIMAL(38,18) COMMENT '成长系数',
    `grow_type` STRING COMMENT '成长曲线类型：高成长型,波动型,高衰退型',
    `cust_preference` STRING COMMENT '业态偏好',
    `exposure_type` STRING COMMENT '曝光类别：高曝光,中曝光,低曝光',
    `click_type` STRING COMMENT '点击类别：高点击,中点击,低点击',
    `elastic_coefficient` DECIMAL(38,18) COMMENT '弹性系数',
    `elastic_coefficient_type` STRING COMMENT '弹性类别：弹性商品,中度弹性商品,不弹性商品',
    `gross_type` STRING COMMENT '毛利类别：高毛利,中毛利,低毛利',
    `order_sku_cnt_type` STRING COMMENT '销量类别：高销量,中销量,低销量',
    `order_amt_type` STRING COMMENT 'GMV类别：高GMV,中GMV,低GMV',
    `turnover_type` STRING COMMENT '周转类别：高周转,中周转,低周转',
    `store_cost_type` STRING COMMENT '库存金额类别：高库存,中库存,低库存',
    `store_order_type` STRING COMMENT '库存应用类标签',
    `order_gross_type` STRING COMMENT '销售应用类标签',
    `click_order_type` STRING COMMENT '流量应用类标签',
    `store_quantity` BIGINT COMMENT '仓库库存数量',
    `lock_quantit` BIGINT COMMENT '锁定库存数量',
    `safe_quantit` BIGINT COMMENT '安全库存数量',
    `freeze_quantity` BIGINT COMMENT '冻结库存数量',
    `ues_store_quantity` BIGINT COMMENT '可用库存数量',
    `road_quantity` BIGINT COMMENT '在途库存数量',
    `pcs_on_raod_quantity` BIGINT COMMENT '采购在途库存数量',
    `alc_on_raod_quantity` BIGINT COMMENT '调拨在途库存数量',
    `store_amt` DECIMAL(38,18) COMMENT '仓库库存成本金额',
    `ues_store_amt` DECIMAL(38,18) COMMENT '可用库存成本金额',
    `lock_amt` DECIMAL(38,18) COMMENT '锁定库存成本金额',
    `safe_amt` DECIMAL(38,18) COMMENT '安全库存成本金额',
    `freeze_amt` DECIMAL(38,18) COMMENT '冻结库存成本金额',
    `on_road_amt` DECIMAL(38,18) COMMENT '在途库存成本金额',
    `purchase_road_amt` DECIMAL(38,18) COMMENT '采购在途库存成本金额',
    `allocate_road_amt` DECIMAL(38,18) COMMENT '调拨在途库存成本金额',
    `nearest_doc_3` DECIMAL(38,18) COMMENT '近3天DOC（库存周转天数）',
    `nearest_doc_7` DECIMAL(38,18) COMMENT '近7天DOC（库存周转天数）',
    `nearest_doc_14` DECIMAL(38,18) COMMENT '近14天DOC（库存周转天数）',
    `future_doc` DECIMAL(38,18) COMMENT '未来预估DOC（库存周转天数）',
    `duration_on_shelf` DECIMAL(38,18) COMMENT '上架时长（天）',
    `duration_sale_out` DECIMAL(38,18) COMMENT '下架时长（天）',
    `sale_out_rate` DECIMAL(38,18) COMMENT '售罄率',
    `unit_cost` DECIMAL(38,18) COMMENT '单价',
    `init_store_quantity` BIGINT COMMENT '在库库存数量（期初）',
    `init_store_amt` DECIMAL(38,18) COMMENT '在库金额（期初）',
    `period_store_cnt_3_1` BIGINT COMMENT '1/3效期内库存数量',
    `period_store_cnt_3_2` BIGINT COMMENT '2/3效期内库存数量',
    `period_store_amt_3_1` DECIMAL(38,18) COMMENT '1/3效期内库存成本',
    `period_store_amt_3_2` DECIMAL(38,18) COMMENT '2/3效期内库存成本',
    `period_store_cnt_2_1` BIGINT COMMENT '1/2效期内库存数量',
    `period_store_amt_2_1` DECIMAL(38,18) COMMENT '1/2效期内库存成本',
    `period_store_cnt_4_3` BIGINT COMMENT '4/3效期内库存数量',
    `period_store_amt_4_3` DECIMAL(38,18) COMMENT '4/3效期内库存成本',
    `in_bound_store_cnt` BIGINT COMMENT '总入库量',
    `in_purchases_cnt` BIGINT COMMENT '采购入库量',
    `in_allocate_cnt` BIGINT COMMENT '调拨入库量',
    `in_transfer_cnt` BIGINT COMMENT '转换入库量',
    `in_back_cnt` BIGINT COMMENT '退货入库量',
    `in_other_cnt` BIGINT COMMENT '其他入库量',
    `in_bound_store_amt` DECIMAL(38,18) COMMENT '总入库成本',
    `in_purchases_amt` DECIMAL(38,18) COMMENT '采购入库成本',
    `in_allocate_amt` DECIMAL(38,18) COMMENT '调拨入库成本',
    `in_transfer_amt` DECIMAL(38,18) COMMENT '转换入库成本',
    `in_back_amt` DECIMAL(38,18) COMMENT '退货入库成本',
    `in_other_amt` DECIMAL(38,18) COMMENT '其他入库成本',
    `out_bound_store_cnt` BIGINT COMMENT '总出库量',
    `out_sale_cnt` BIGINT COMMENT '销售出库量（含自提）',
    `out_allocate_cnt` BIGINT COMMENT '调拨出库量',
    `out_transfer_cnt` BIGINT COMMENT '转换出库量',
    `out_damage_cnt` BIGINT COMMENT '货损出库量',
    `out_other_cnt` BIGINT COMMENT '其他出库量',
    `out_bound_store_amt` DECIMAL(38,18) COMMENT '总出库成本',
    `out_sale_amt` DECIMAL(38,18) COMMENT '销售出库成本（含自提）',
    `out_allocate_amt` DECIMAL(38,18) COMMENT '调拨出库成本',
    `out_transfer_amt` DECIMAL(38,18) COMMENT '转换出库成本',
    `out_damage_amt` DECIMAL(38,18) COMMENT '货损出库成本',
    `out_other_amt` DECIMAL(38,18) COMMENT '其他出库成本',
    `nearest_7_days_sale_quantity` BIGINT COMMENT '近7天销量数量（含自提）',
    `nearest_7_days_total_sale_cost` DECIMAL(38,18) COMMENT '近7天总销量成本',
    `nearest_7_days_inventory_quantity` BIGINT COMMENT '近7天库存数量（含自提）',
    `nearest_7_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近7天总库存成本',
    `nearest_30_days_sale_quantity` BIGINT COMMENT '近30天销量数量（含自提）',
    `nearest_30_days_total_sale_cost` DECIMAL(38,18) COMMENT '近30天总销量成本',
    `nearest_30_days_inventory_quantity` BIGINT COMMENT '近30天库存数量（含自提）',
    `nearest_30_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近30天总库存成本',
    `month_sale_quality` BIGINT COMMENT '当月销量（含自提）',
    `month_total_sale_cost` DECIMAL(38,18) COMMENT '当月总销量成本（含自提）',
    `month_inventory_quantity` BIGINT COMMENT '当月库存数量',
    `month_inventory_total_cost` DECIMAL(38,18) COMMENT '当月库存成本',
    `nearest_3_days_sale_avg` DECIMAL(38,18) COMMENT '近3天日均出库量（含自提）',
    `nearest_7_days_sale_avg` DECIMAL(38,18) COMMENT '近7天日均出库量（含自提）',
    `nearest_14_days_sale_avg` DECIMAL(38,18) COMMENT '近14天日均出库量（含自提）',
    `future_sale_out_cnt` DECIMAL(38,18) COMMENT '预估未来日均销量',
    `future_allocate_cnt` DECIMAL(38,18) COMMENT '预估未来日均调拨量',
    `imminent_store_cnt` BIGINT COMMENT '临期品库存量',
    `imminent_store_amt` DECIMAL(38,18) COMMENT '临期品成本',
    `unsalable_decided` STRING COMMENT '动销判定：两周低动销，两周无动销，-（表示正常动销）',
    `is_sale_out_rask` STRING COMMENT '售罄风险判定：是，否'
) 
COMMENT '仓+SKU库存健康汇总表，包含各仓库SKU的库存状态、动销情况、库存周转、成本分析等综合健康指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='仓+SKU库存健康度分析表，用于监控和管理各仓库SKU的库存健康状况',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```