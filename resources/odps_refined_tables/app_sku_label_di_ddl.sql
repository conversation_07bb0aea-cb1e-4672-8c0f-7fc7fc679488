```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_label_di` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd，表示数据统计日期',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位的唯一标识',
  `sku_label` STRING COMMENT 'ABC标签，取值范围：A-核心商品，B-重要商品，C-一般商品',
  `dlv_30_origin_amt` DECIMAL(38,18) COMMENT '近30天履约应付GMV，单位：元',
  `dlv_30_cust_cnt` BIGINT COMMENT '近30天履约客户数，单位：个',
  `dlv_30_sku_cnt` BIGINT COMMENT '近30天履约数量，单位：件',
  `dlv_30_gross_profit_amt` DECIMAL(38,18) COMMENT '近30天履约实付毛利润，单位：元',
  `dlv_30_gross_rate` DECIMAL(38,18) COMMENT '近30天履约实付毛利率，单位：百分比',
  `dlv_31_60_origin_amt` DECIMAL(38,18) COMMENT '近31-60天履约应付GMV，单位：元',
  `dlv_31_60_cust_cnt` BIGINT COMMENT '近31-60天履约客户数，单位：个',
  `dlv_31_60_sku_cnt` BIGINT COMMENT '近31-60天履约数量，单位：件',
  `dlv_31_60_gross_profit_amt` DECIMAL(38,18) COMMENT '近31-60天履约实付毛利润，单位：元',
  `dlv_31_60_gross_rate` DECIMAL(38,18) COMMENT '近31-60天履约实付毛利率，单位：百分比',
  `dlv_origin_growth_amount` DECIMAL(38,18) COMMENT '履约应付增长额，近30天与31-60天履约应付GMV的差值，单位：元',
  `dlv_origin_growth_rate` DECIMAL(38,18) COMMENT '履约应付增长率，近30天与31-60天履约应付GMV的增长比率',
  `dlv_cust_growth_cnt` BIGINT COMMENT '履约客户增长数，近30天与31-60天履约客户数的差值，单位：个',
  `dlv_cust_growth_rate` DECIMAL(38,18) COMMENT '履约客户增长率，近30天与31-60天履约客户数的增长比率',
  `dlv_origin_profit_growth_amount` DECIMAL(38,18) COMMENT '履约毛利润增长额，近30天与31-60天履约实付毛利润的差值，单位：元',
  `dlv_real_profit_growth_rate` DECIMAL(38,18) COMMENT '履约毛利润增长率，近30天与31-60天履约实付毛利润的增长比率',
  `dlv_origin_growth_label` STRING COMMENT 'GMC增速标签，取值范围：高增长-显著增长，锐减-显著下降，其他-无明显变化',
  `dlv_origin_rate_label` STRING COMMENT 'GMC增率标签，取值范围：高增长-显著增长，锐减-显著下降，其他-无明显变化',
  `dlv_cust_growth_label` STRING COMMENT '客户增速标签，取值范围：高增长-显著增长，锐减-显著下降，其他-无明显变化',
  `dlv_cust_rate_label` STRING COMMENT '客户增率标签，取值范围：高增长-显著增长，锐减-显著下降，其他-无明显变化',
  `dlv_profit_growth_label` STRING COMMENT '毛利润增速标签，取值范围：高增长-显著增长，锐减-显著下降，其他-无明显变化',
  `dlv_profit_rate_label` STRING COMMENT '毛利润增率标签，取值范围：高增长-显著增长，锐减-显著下降，其他-无明显变化',
  `spu_name_list` STRING COMMENT '购物车关联TOP3商品列表，格式为商品名称1_商品名称2_商品名称3'
)
COMMENT '商品标签表，包含商品的ABC分类标签、履约相关指标和增长趋势分析'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='商品标签分析表，用于商品ABC分类和履约表现监控',
  'lifecycle'='30'
);
```