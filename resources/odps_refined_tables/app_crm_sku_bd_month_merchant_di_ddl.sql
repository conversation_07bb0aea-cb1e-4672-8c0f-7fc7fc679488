CREATE TABLE IF NOT EXISTS app_crm_sku_bd_month_merchant_di(
    `month_tag` STRING COMMENT '月份标记，格式为yyyyMM，表示年月，如202509表示2025年9月',
    `sku_id` STRING COMMENT '商品SKU编号，唯一标识商品规格',
    `bd_id` BIGINT COMMENT '销售ID，取值范围：最小值13，最大值1181430，平均值389597，标准差515383',
    `merchant_id_text` STRING COMMENT '商户ID列表，使用英文逗号分隔多个商户ID'
)
COMMENT '每个SKU每月下单商户ID关联表，记录商品与销售、商户的月度关联关系'
PARTITIONED BY (
    `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示年月日，如20250922表示2025年9月22日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '商品SKU月度销售商户关联表，用于分析商品销售与商户的月度关联情况',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;