CREATE TABLE IF NOT EXISTS app_area_honour_kpi_di(
	service_area STRING COMMENT '区域名称，枚举值：华北、华中、华西、广西、福建、贵阳、昆明、华东、华南',
	point_in_cnt BIGINT COMMENT '内区点位数，统计区域内服务点的数量',
	point_out_cnt BIGINT COMMENT '外区点位数，统计区域外服务点的数量',
	out_quality BIGINT COMMENT '出库件数，统计当日出库的商品数量',
	trunk_amt DECIMAL(38,18) COMMENT '干线费用，物流运输中的干线运输成本',
	delivery_in_amt DECIMAL(38,18) COMMENT '内区配送成本，区域内配送产生的费用',
	delivery_out_amt DECIMAL(38,18) COMMENT '外区配送成本，区域外配送产生的费用',
	storage_amt DECIMAL(38,18) COMMENT '仓储成本，商品存储相关的费用',
	delivery_total_amt DECIMAL(38,18) COMMENT '总配送费，内区配送成本与外区配送成本之和'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='区域维度履约成本KPI日表，统计各区域的履约相关成本和关键绩效指标') 
LIFECYCLE 30;