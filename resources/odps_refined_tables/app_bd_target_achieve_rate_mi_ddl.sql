CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_bd_target_achieve_rate_mi` (
  `months` STRING COMMENT '月份，格式为yyyyMM',
  `bd_id` BIGINT COMMENT '业绩归属的BD_ID',
  `bd_name` STRING COMMENT '业绩归属的销售姓名',
  `bd_m1` STRING COMMENT 'BD所属M1管理者姓名',
  `bd_m2` STRING COMMENT 'BD所属M2管理者姓名',
  `bd_m3` STRING COMMENT 'BD所属M3管理者姓名',
  `bd_work_zone` STRING COMMENT 'BD所在销售区域，枚举值：杭州湾、徽京、广州、东莞、浙南、大粤西、深圳、无锡、福泉、苏北、江西、贵阳,重庆、四川、广西、佛山、杭州、武汉、长沙、济南,青岛、苏州、浦西、昆明、浦东、厦门',
  `bd_work_city` STRING COMMENT 'BD所在城市，枚举值：湖州市、南京市、广州市、东莞市、台州市、江门市、深圳市、无锡市、泉州市、泰州市、南昌市、宁波市、珠海市、重庆市、嘉兴市、清远市、绵阳市、扬州市、盐城市、新余市、湛江市、茂名市、福州市、惠州市、萍乡市、南宁市、佛山市、杭州市、上饶市、武汉市、绍兴市、中山市、合肥市、长沙市、株洲市、金华市、贵阳市、成都市、桂林市、汕头市、青岛市、苏州市、上海市、淮安市、温州市、常德市、莆田市、昆明市、芜湖市、厦门市等',
  `team_tag` STRING COMMENT 'BD所属团队，枚举值：平台销售',
  `is_disabled` STRING COMMENT '账号是否禁用，枚举值：未禁用、已禁用',
  `create_time` STRING COMMENT '账号创建时间，格式为yyyy-MM-dd HH:mm:ss',
  `bd_creat_months` STRING COMMENT '账号创建月份，格式为yyyyMM',
  `no_at_gmv_target` DECIMAL(38,18) COMMENT '非ATGMV目标金额',
  `no_at_gmv_amt` DECIMAL(38,18) COMMENT '非ATGMV达成金额',
  `no_at_gmv_achieve_rate` DECIMAL(38,18) COMMENT '非ATGMV达成率',
  `fruit_gmv_target` DECIMAL(38,18) COMMENT '鲜果GMV目标金额',
  `fruit_gmv_amt` DECIMAL(38,18) COMMENT '鲜果GMV达成金额',
  `fruit_gmv_achieve_rate` DECIMAL(38,18) COMMENT '鲜果GMV达成率',
  `total_gmv_achieve_rate` DECIMAL(38,18) COMMENT '综合达成率',
  `p_rate` DECIMAL(38,18) COMMENT 'P系数',
  `fruit_rate` DECIMAL(38,18) COMMENT '鲜果达成系数'
) 
COMMENT '销售目标达成系数表，记录BD的销售目标达成情况和相关指标'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，数据日期，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='销售目标达成系数表，包含BD的销售目标达成率、GMV指标和层级关系信息')
LIFECYCLE 30;