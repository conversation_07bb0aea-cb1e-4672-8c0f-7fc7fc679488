CREATE TABLE IF NOT EXISTS app_saas_tenant_metrics_summary_di(
	tenant_id BIGINT COMMENT '租户ID，唯一标识一个SaaS租户',
	time_tag STRING COMMENT '时间标签，格式为yyyyMMdd，表示年月日',
	no_sale_item_rate_7d DECIMAL(38,18) COMMENT '7日滞销率（连续7天未发生付款的商品数与所有上架中的商品数的占比），取值范围0-100',
	no_sale_item_rate_30d DECIMAL(38,18) COMMENT '30日滞销率（连续30天未发生付款的商品数与所有上架中的商品数的占比），取值范围0-100',
	store_purchase_rate_7d DECIMAL(38,18) COMMENT '7日采购活跃率（近7天有过下单的门店数/经营中的总门店数），取值范围0-100',
	store_purchase_rate_30d DECIMAL(38,18) COMMENT '30日采购活跃率（近30天有过下单的门店数/经营中的总门店数），取值范围0-100',
	refund_and_returns_rate_1d DECIMAL(38,18) COMMENT '昨日已到货退款率（昨日已到货退款&退货退款金额占比），取值范围0-100',
	refund_and_returns_rate_7d DECIMAL(38,18) COMMENT '7日已到货退款率（近7天已到货退款&退货退款金额占比），取值范围0-100',
	refund_and_returns_rate_30d DECIMAL(38,18) COMMENT '30日已到货退款率（近30天已到货退款&退货退款金额占比），取值范围0-100',
	order_fulfillment_rate_1d DECIMAL(38,18) COMMENT '次日履约率（近30天订单次日发货数/30天总订单数），取值范围0-100',
	order_fulfillment_rate_3d DECIMAL(38,18) COMMENT '3日履约率（近30天订单3日内发货数/30天总订单数），取值范围0-100',
	order_fulfillment_rate_7d DECIMAL(38,18) COMMENT '7日履约率（近30天订单7日内发货数/30天总订单数），取值范围0-100',
	stock_turnover_days_30d DECIMAL(38,18) COMMENT '近30天库存周转天数，表示商品从入库到出库的平均天数',
	no_sale_goods_num_15d BIGINT COMMENT '近15天滞销货品数（连续15天无销售的商品数量）',
	near_deadline_goods_num_15d BIGINT COMMENT '近15天临期货品数（距离保质期截止日期15天内的商品数量）',
	expired_goods_num_30d BIGINT COMMENT '近30天过期货品数（已过保质期的商品数量）',
	supplier_on_time_delivery_rate_30d DECIMAL(38,18) COMMENT '近30天供应商到仓准时率（按时交付的订单占比），取值范围0-100',
	supplier_to_warehouse_accuracy_30d DECIMAL(38,18) COMMENT '近30天供应商到仓准确率（交付商品与订单一致的占比），取值范围0-100',
	damaged_goods_out_of_warehouse_num_7d BIGINT COMMENT '近7天货损出库的商品件数（出库时发现损坏的商品数量）',
	stock_accuracy_7d DECIMAL(38,18) COMMENT '近7天库存准确率（系统库存与实际库存一致的占比），取值范围0-100',
	outbound_accuracy_7d DECIMAL(38,18) COMMENT '近7天出库准确率（出库商品与订单一致的占比），取值范围0-100',
	outbound_timeliness_rate_7d DECIMAL(38,18) COMMENT '近7天出库及时率（按时出库的订单占比），取值范围0-100',
	inbound_timeliness_rate_7d DECIMAL(38,18) COMMENT '近7天入库及时率（按时入库的订单占比），取值范围0-100'
) 
COMMENT 'SaaS租户指标汇总表，包含租户的各项业务指标数据，用于分析和监控租户运营状况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS租户运营指标汇总表，包含销售、库存、履约、供应商等多个维度的业务指标') 
LIFECYCLE 30;