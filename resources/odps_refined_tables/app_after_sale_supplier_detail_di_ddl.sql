```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_after_sale_supplier_detail_di` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd',
  `after_sale_order_id` STRING COMMENT '售后单号，即after_sale_order_no',
  `apply_time` DATETIME COMMENT '售后申请时间，格式：yyyy-MM-dd HH:mm:ss',
  `reason` STRING COMMENT '售后原因，枚举值：商品品质问题、其他、包装问题、商品数量不符、保质期不符',
  `handle_type` STRING COMMENT '处理方式，枚举值：返券、退货退款、退款、录入账单、补发、拒收退款、换货',
  `review_remark` STRING COMMENT '审核备注',
  `after_sale_sku_cnt` BIGINT COMMENT '售后商品数量',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后金额',
  `sku_id` STRING COMMENT '商品SKU',
  `spu_id` BIGINT COMMENT '商品SPU；即pd_id',
  `spu_no` STRING COMMENT 'spu编号',
  `spu_name` STRING COMMENT '商品名',
  `sku_disc` STRING COMMENT 'sku描述；即weight',
  `sku_type` STRING COMMENT '商品类型；枚举值：自营、代售',
  `cust_id` BIGINT COMMENT '售后用户，即客户ID',
  `cust_name` STRING COMMENT '客户名',
  `cust_type` STRING COMMENT '客户类型；枚举值：普通、大客户',
  `cust_team` STRING COMMENT '客户团队类型；枚举值：平台客户、集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `brand_id` BIGINT COMMENT '品牌ID（原大客户）',
  `brand_name` STRING COMMENT '品牌的企业名称',
  `brand_alias` STRING COMMENT '品牌的品牌名称',
  `city_id` BIGINT COMMENT '运营服务ID',
  `city_name` STRING COMMENT '运营服务名称',
  `large_area_id` BIGINT COMMENT '运营服务大区id',
  `large_area_name` STRING COMMENT '运营服务大区name；枚举值：青岛大区、杭州大区、成都大区、武汉大区、重庆大区、广州大区、苏州大区、上海大区、苏南大区、长沙大区、贵阳大区、福州大区、南宁大区',
  `province` STRING COMMENT '注册时省；枚举值：山东、江苏、四川、湖北、重庆、广东、浙江、安徽、上海、江西、湖南、贵州、福建、广西壮族自治区',
  `purchase_no` STRING COMMENT '采购批次',
  `supplier` STRING COMMENT '批次供应商',
  `store_no` BIGINT COMMENT '配送仓编号',
  `store_name` STRING COMMENT '配送仓名称',
  `warehouse_no` BIGINT COMMENT '库存仓编号',
  `warehouse_name` STRING COMMENT '库存仓名称；枚举值：青岛总仓、嘉兴总仓、华西总仓、武汉总仓、东莞总仓、南京总仓、长沙总仓、济南总仓、贵阳总仓、福州总仓、南宁总仓',
  `after_sale_type` STRING COMMENT '售后分类；枚举值：质量,腐烂/发霉/变质/黑斑、其他、质量,过熟、质量,花皮、质量,过黄/过老/不新鲜、仓配物流,少称、仓配物流,开裂/压伤、仓配物流,包装破损、质量,过生、仓配物流,缺货、质量,有籽、仓配物流,漏发、质量,异物/有虫、质量,单果重量不足、特殊处理(客户体验)、错拍',
  `receipt_method` STRING COMMENT '收货方式'
)
COMMENT '已到货售后有码数据表，记录售后订单的详细信息，包括商品信息、客户信息、供应商信息等'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '已到货售后有码数据表，用于分析售后订单的详细情况',
  'lifecycle' = '30'
);
```