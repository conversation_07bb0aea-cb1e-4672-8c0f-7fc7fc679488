CREATE TABLE IF NOT EXISTS app_finance_saas_cost_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`service_area` STRING COMMENT '大区，取值范围：华中、华东、西南、广西、福建、未知',
	`warehouse_no` BIGINT COMMENT '库存仓ID，唯一标识仓库的数字编号',
	`warehouse_name` STRING COMMENT '库存仓名称，具体仓库的完整名称',
	`category1` STRING COMMENT '商品一级类目，取值范围：鲜果、乳制品、其他',
	`sell_cost_amt` DECIMAL(38,18) COMMENT '含税销售成本，包含税费的销售成本金额',
	`damage_cost_amt` DECIMAL(38,18) COMMENT '含税货损成本，包含税费的货物损失成本金额',
	`sample_cost_amt` DECIMAL(38,18) COMMENT '含税出样成本，包含税费的样品成本金额',
	`stocktake_cost_amt` DECIMAL(38,18) COMMENT '含税盘盈盘亏成本，包含税费的库存盘点差异成本金额',
	`sell_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税销售成本，不包含税费的销售成本金额',
	`damage_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税货损成本，不包含税费的货物损失成本金额',
	`sample_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税出样成本，不包含税费的样品成本金额',
	`stocktake_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税盘盈盘亏成本，不包含税费的库存盘点差异成本金额'
) 
COMMENT '财务口径收入数据表，记录各仓库不同商品类目的成本数据，包含含税和不含税的成本金额'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='财务口径成本数据表，用于财务分析和成本核算') 
LIFECYCLE 30;