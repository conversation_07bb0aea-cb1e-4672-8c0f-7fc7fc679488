CREATE TABLE IF NOT EXISTS app_kpi_whole_delivery_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`cust_group` STRING COMMENT '客户类型，取值范围：大客户、平台客户、批发客户、ALL（全部客户）',
	`sku_type` STRING COMMENT '商品类型，取值范围：自营、代仓、全品类、SAAS客户自营、ALL（全部商品类型）',
	`category` STRING COMMENT '商品类目，取值范围：鲜果、乳制品、其他、ALL（全部类目）',
	`dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位：元',
	`dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位：元',
	`dlv_cost_amt` DECIMAL(38,18) COMMENT '履约总成本，单位：元',
	`dlv_origin_gross_profit` DECIMAL(38,18) COMMENT '履约应付毛利润，单位：元',
	`dlv_real_gross_profit` DECIMAL(38,18) COMMENT '履约实付毛利润，单位：元',
	`dlv_cust_cnt` BIGINT COMMENT '履约客户数，单位：个',
	`dlv_point_cnt` BIGINT COMMENT '履约点位数，单位：个',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费（含精准送，超时加单费，去除优惠券），单位：元',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后金额，单位：元',
	`damage_amt` DECIMAL(38,18) COMMENT '货损金额，单位：元',
	`offine_delivey_amt` DECIMAL(38,18) COMMENT '履约费用，单位：元',
	`offine_no_delivey_amt` DECIMAL(38,18) COMMENT '非履约费用，单位：元',
	`timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付总金额，单位：元',
	`timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付总金额，单位：元',
	`timing_cost_amt` DECIMAL(38,18) COMMENT '省心送履约总成本，单位：元',
	`special_amt` DECIMAL(38,18) COMMENT '特价活动营销费用，单位：元',
	`temporary_amt` DECIMAL(38,18) COMMENT '临保活动营销费用，单位：元',
	`step_amt` DECIMAL(38,18) COMMENT '阶梯价营销费用，单位：元',
	`cream_amt` DECIMAL(38,18) COMMENT '奶油卡营销费用，单位：元',
	`fresh_milk_amt` DECIMAL(38,18) COMMENT '鲜奶卡营销费用，单位：元',
	`after_sale_compensate_amt` DECIMAL(38,18) COMMENT '售后补偿营销费用，单位：元',
	`industry_activities_amt` DECIMAL(38,18) COMMENT '行业活动营销费用，单位：元',
	`sale_store_amt` DECIMAL(38,18) COMMENT '销售囤货券营销费用，单位：元',
	`sale_spots_amt` DECIMAL(38,18) COMMENT '销售现货券营销费用，单位：元',
	`sales_customer_amt` DECIMAL(38,18) COMMENT '销售客情券营销费用，单位：元',
	`sales_activity_amt` DECIMAL(38,18) COMMENT '销售月活券营销费用，单位：元',
	`sales_category_amt` DECIMAL(38,18) COMMENT '销售品类券营销费用，单位：元',
	`area_new_amt` DECIMAL(38,18) COMMENT '区域拉新券营销费用，单位：元',
	`area_recall_amt` DECIMAL(38,18) COMMENT '区域召回券营销费用，单位：元',
	`full_reduction_amt` DECIMAL(38,18) COMMENT '满减活动营销费用，单位：元',
	`platform_activity_amt` DECIMAL(38,18) COMMENT '平台活动券营销费用，单位：元',
	`qst_amount` DECIMAL(38,18) COMMENT '全生态用车费用，单位：元'
) 
COMMENT '履约KPI汇总表，包含履约相关的各项关键绩效指标数据，如履约金额、成本、利润、客户数、点位数以及各类营销费用等'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='履约KPI汇总表，用于分析和监控履约业务的各项关键指标') 
LIFECYCLE 30;