```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_largeg_area_sku_order_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位的唯一标识',
  `spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格、等级、重量等详细信息',
  `large_area_id` BIGINT COMMENT '运营大区ID，取值范围：1-上海大区, 2-华中大区, 3-华南大区, 4-山东大区, 5-昆明大区, 6-浙江大区, 7-电销, 8-苏皖大区, 9-西南大区, 10-闽桂大区',
  `large_area_name` STRING COMMENT '运营大区名称，取值范围：上海大区、华中大区、华南大区、山东大区、昆明大区、浙江大区、电销、苏皖大区、西南大区、闽桂大区',
  `cust_cnt` BIGINT COMMENT '交易客户数，统计周期内购买该SKU的客户数量',
  `large_area_cust_cnt` BIGINT COMMENT '运营大区总客户数，统计周期内该大区的总客户数量'
)
COMMENT '区域渗透数据表，统计各运营大区内SKU的客户购买情况，用于分析商品在各区域的渗透率'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，如20250922表示2025年9月22日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='区域SKU订单渗透数据表',
  'lifecycle'='30'
);
```