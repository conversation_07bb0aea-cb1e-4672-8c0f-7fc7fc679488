```sql
CREATE TABLE IF NOT EXISTS app_bms_proxy_stock_change_statistics_di(
    date_tag STRING COMMENT '业务日期，格式：yyyyMMdd',
    doc_type BIGINT COMMENT '业务类型枚举：0-销售出库，1-退货入库，2-盘盈入库，3-盘亏出库，4-货损出库，5-出样出库',
    doc_stock_type BIGINT COMMENT '单据类型，取值范围：15-58',
    doc_no BIGINT COMMENT '业务ID，唯一标识每笔业务',
    doc_stock_no STRING COMMENT '库存流水ID，唯一标识每条库存流水记录',
    warehouse_no BIGINT COMMENT '库存仓ID，标识仓库编号',
    warehouse_name STRING COMMENT '库存仓名称，枚举值包括：武汉总仓、东莞总仓、东莞冷冻总仓、嘉兴总仓、嘉兴海盐总仓、南京总仓、华西总仓、重庆总仓、长沙总仓、青岛总仓、福州总仓、昆明总仓、南宁总仓',
    doc_stock_change_date DATETIME COMMENT '库存变动时间，格式：yyyy-MM-dd HH:mm:ss',
    supplier_id STRING COMMENT '供应商ID，标识供应商编号',
    supplier_name STRING COMMENT '供应商名称',
    purchaser STRING COMMENT '采购员姓名，枚举值包括：吴仔祥、郭倩倩、毛文俊、吴雅雯、谢雨澄、石力、韩易航、黄可、张明恩、沈佳凤、崔庆云',
    batch STRING COMMENT '批次号',
    sku STRING COMMENT 'SKU编码，商品唯一标识',
    title STRING COMMENT '商品名称',
    specification STRING COMMENT '商品规格描述',
    stock_change_quantity BIGINT COMMENT '发生数量，正数表示入库，负数表示出库，取值范围：-400到400',
    doc_unit_price DECIMAL(38,18) COMMENT '应付单价，单位：元',
    doc_actual_unit_price DECIMAL(38,18) COMMENT '实付单价，单位：元',
    doc_price DECIMAL(38,18) COMMENT '应付总价，单位：元',
    doc_actual_price DECIMAL(38,18) COMMENT '实付总价，单位：元',
    order_no STRING COMMENT '订单编号',
    order_source STRING COMMENT '订单来源枚举：xianmu-项目，saas-SaaS系统',
    cust_name STRING COMMENT '客户名称',
    kickback_rate DECIMAL(38,18) COMMENT '佣金比例',
    remark STRING COMMENT '备注信息',
    image STRING COMMENT '图片URL地址',
    responsibility_source STRING COMMENT '责任归属枚举：供应商-供应商责任，非供应商-非供应商责任',
    area_price DECIMAL(38,18) COMMENT '区域售价，单位：元',
    date_flag DATETIME COMMENT '业务日期标志，格式：yyyy-MM-dd HH:mm:ss，通常为当天零点',
    doc_stock_change_finish_date DATETIME COMMENT '业务变动完成日期，格式：yyyy-MM-dd HH:mm:ss',
    marketing_amount DECIMAL(38,18) COMMENT '营销费用，单位：元'
)
COMMENT '代销对账信息表，记录代销业务中的库存变动、价格、订单等对账相关信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='代销业务对账统计表，包含库存变动、价格信息、订单详情等对账核心数据',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```