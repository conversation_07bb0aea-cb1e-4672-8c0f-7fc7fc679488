CREATE TABLE IF NOT EXISTS app_pcs_supplier_rebate_sku_cumulative_df(
	year BIGINT COMMENT '年度，如：2023、2024、2025',
	period STRING COMMENT '统计周期，取值范围：年、H1(上半年)、H2(下半年)、Q1(第一季度)、Q2(第二季度)、Q3(第三季度)、Q4(第四季度)、1月、2月、3月、4月、5月、6月、7月、8月、9月、10月、11月、12月',
	sku_id STRING COMMENT 'SKU编码，商品唯一标识',
	commodity_name_and_specification STRING COMMENT '商品名称&规格，包含商品详细描述信息',
	supplier_id BIGINT COMMENT '供货商ID，供应商唯一标识',
	supplier_name STRING COMMENT '供货商名称，供应商全称',
	warehouse_name STRING COMMENT '仓库名称，存储商品的仓库信息',
	commodity_temperature_zone STRING COMMENT '商品温区，取值范围：冷藏、冷冻、常温',
	purchase_order_amount_in_period DECIMAL(38,18) COMMENT '周期内采购下单金额（含税）',
	purchase_order_amount_excluding_tax DECIMAL(38,18) COMMENT '采购下单金额（不含税）',
	purchase_order_quantity_in_period BIGINT COMMENT '周期内采购下单件数',
	purchase_order_weight_in_period DECIMAL(38,18) COMMENT '周期内采购下单重量（单位：kg）',
	purchase_order_volume_in_period DECIMAL(38,18) COMMENT '周期内采购下单体积（单位：m³）',
	purchase_inbound_amount_in_period DECIMAL(38,18) COMMENT '周期内采购入库金额（含税）',
	purchase_inbound_amount_excluding_tax DECIMAL(38,18) COMMENT '采购入库金额（不含税）',
	purchase_inbound_quantity_in_period BIGINT COMMENT '周期内采购入库件数',
	purchase_inbound_weight_in_period DECIMAL(38,18) COMMENT '周期内采购入库重量（单位：kg）',
	purchase_inbound_volume_in_period DECIMAL(38,18) COMMENT '周期内采购入库体积（单位：m³）',
	purchase_reservation_amount_in_period DECIMAL(38,18) COMMENT '周期内采购预约金额（含税）',
	purchase_reservation_amount_excluding_tax DECIMAL(38,18) COMMENT '采购预约金额（不含税）',
	purchase_reservation_quantity_in_period BIGINT COMMENT '周期内采购预约件数',
	purchase_reservation_weight_in_period DECIMAL(38,18) COMMENT '周期内采购预约重量（单位：kg）',
	purchase_reservation_volume_in_period DECIMAL(38,18) COMMENT '周期内采购预约体积（单位：m³）'
)
COMMENT '供应商返利目标累计月维度表，记录供应商SKU级别的采购相关数据，用于返利计算和分析'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='供应商返利目标累计月维度表，包含采购下单、入库、预约等关键业务指标')
LIFECYCLE 30;