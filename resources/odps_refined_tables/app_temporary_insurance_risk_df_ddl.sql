CREATE TABLE IF NOT EXISTS app_temporary_insurance_risk_df(
	sku STRING COMMENT 'SKU编码，商品唯一标识',
	warehouse_no BIGINT COMMENT '库存仓编号，取值范围：2-10',
	area_no BIGINT COMMENT '城市编号，取值范围：1001-44271',
	large_area_no BIGINT COMMENT '运营大区编号，取值范围：1-89',
	date_flag STRING COMMENT '日期标识，格式：yyyyMMdd（年月日）'
) 
COMMENT '临保风险品表，记录临时保险风险商品的相关信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='临保风险品数据表，包含SKU编码、仓库信息、区域信息和日期标识等字段') 
LIFECYCLE 30;