```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_team_brand_performance_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的业务日期',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户',
  `brand_id` BIGINT COMMENT '公司ID，唯一标识品牌公司',
  `brand_name` STRING COMMENT '公司名称，品牌公司的全称',
  `brand_alias` STRING COMMENT '品牌别名，品牌常用的简称或别名',
  `register_province` STRING COMMENT '注册省份，品牌公司注册所在地的省份',
  `register_city` STRING COMMENT '注册城市，品牌公司注册所在地的城市',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额，交易订单的原始应付金额总和',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额，交易订单的实际支付金额总和',
  `order_cust_cnt` BIGINT COMMENT '交易门店数，发生交易的门店数量',
  `order_order_cnt` BIGINT COMMENT '交易订单数，交易订单的总数量',
  `new_cust_cnt` BIGINT COMMENT '活跃门店数中新增门店数，新增加的活跃门店数量',
  `new_cust_gmv` DECIMAL(38,18) COMMENT '新增活跃门店GMV，新增活跃门店产生的交易总额',
  `close_cust_cnt` BIGINT COMMENT '活跃门店数中倒闭门店数，已关闭的活跃门店数量',
  `close_cust_gmv` DECIMAL(38,18) COMMENT '倒闭门店GMV，倒闭门店产生的交易总额',
  `old_cust_cnt` BIGINT COMMENT '老活跃门店数，持续活跃的老门店数量',
  `old_cust_gmv` DECIMAL(38,18) COMMENT '老活跃门店GMV，老活跃门店产生的交易总额',
  `new_noactive_cust_cnt` BIGINT COMMENT '拉新门店数（仅注册未下单），仅注册但未下单的新门店数量',
  `new_active_cust_cnt` BIGINT COMMENT '拉新门店数（注册且下单），注册并下单的新门店数量',
  `new_active_gmv` DECIMAL(38,18) COMMENT '拉新门店GMV，拉新门店产生的交易总额',
  `order_replace_cust_cnt` BIGINT COMMENT '代下单门店数，代为下单的门店数量',
  `order_replace_gmv` DECIMAL(38,18) COMMENT '代下单实付金额，代下单的实际支付金额总和',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，履约订单的原始应付金额总和',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，履约订单的实际支付金额总和',
  `cost_amt` DECIMAL(38,18) COMMENT '履约成本，履约订单的成本金额总和',
  `order_cnt` BIGINT COMMENT '履约订单数，履约订单的总数量',
  `sku_cnt` BIGINT COMMENT '总配送件数，配送的商品件数总和',
  `point_cnt` BIGINT COMMENT '总点位数，配送点位的总数',
  `cust_cnt` BIGINT COMMENT '履约门店数，发生履约的门店数量',
  `self_real_total_amt` DECIMAL(38,18) COMMENT '自营实付总金额，自营业务的实际支付金额总和',
  `self_cost_amt` DECIMAL(38,18) COMMENT '自营成本，自营业务的成本金额总和',
  `self_cust_cnt` BIGINT COMMENT '自营品牌门店数，自营品牌的门店数量',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实付总金额，省心送业务的实际支付金额总和',
  `timing_cost_amt` DECIMAL(38,18) COMMENT '省心送成本，省心送业务的成本金额总和',
  `timing_cust_cnt` BIGINT COMMENT '省心送门店数，使用省心送业务的门店数量',
  `fruit_real_total_amt` DECIMAL(38,18) COMMENT '鲜果实付总金额，鲜果业务的实际支付金额总和',
  `fruit_cost_amt` DECIMAL(38,18) COMMENT '鲜果成本，鲜果业务的成本金额总和',
  `fruit_cash_real_total_amt` DECIMAL(38,18) COMMENT '鲜果账期实付金额，鲜果账期业务的实际支付金额',
  `fruit_cust_cnt` BIGINT COMMENT '鲜果门店数，使用鲜果业务的门店数量',
  `dairy_real_total_amt` DECIMAL(38,18) COMMENT '乳制品实付总金额，乳制品业务的实际支付金额总和',
  `dairy_cost_amt` DECIMAL(38,18) COMMENT '乳制品成本，乳制品业务的成本金额总和',
  `dairy_cash_real_cost_amt` DECIMAL(38,18) COMMENT '乳制品账期实付金额，乳制品账期业务的实际支付金额',
  `dairy_cust_cnt` BIGINT COMMENT '乳制品门店数，使用乳制品业务的门店数量',
  `nodairy_real_total_amt` DECIMAL(38,18) COMMENT '非乳制品实付总金额，非乳制品业务的实际支付金额总和',
  `nodairy_cost_amt` DECIMAL(38,18) COMMENT '非乳制品成本，非乳制品业务的成本金额总和',
  `nodairy_cash_cost_amt` DECIMAL(38,18) COMMENT '非乳制品账期实付金额，非乳制品账期业务的实际支付金额',
  `nodairy_cust_cnt` BIGINT COMMENT '非乳制品门店数，使用非乳制品业务的门店数量',
  `cash_real_total_amt` DECIMAL(38,18) COMMENT '账期实付金额，账期业务的实际支付金额总和',
  `nocash_real_total_amt` DECIMAL(38,18) COMMENT '非账期实付金额，非账期业务的实际支付金额总和',
  `replace_origin_total_amt` DECIMAL(38,18) COMMENT '代仓应付总金额，代仓业务的原始应付金额总和',
  `replace_real_total_amt` DECIMAL(38,18) COMMENT '代仓实付总金额，代仓业务的实际支付金额总和',
  `replace_cust_cnt` BIGINT COMMENT '代仓门店数，使用代仓业务的门店数量',
  `self_after_sale_amt` DECIMAL(38,18) COMMENT '自营售后金额，自营业务的售后金额总和',
  `replace_after_sale_amt` DECIMAL(38,18) COMMENT '代仓售后金额，代仓业务的售后金额总和'
) 
COMMENT '大客户团队汇总表，统计各客户团队下品牌公司的交易和履约相关指标数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的技术日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='大客户团队业务绩效汇总表，包含交易、履约、自营、省心送、鲜果、乳制品、非乳制品、账期、代仓等各业务维度的统计指标',
  'lifecycle'='30'
)
```