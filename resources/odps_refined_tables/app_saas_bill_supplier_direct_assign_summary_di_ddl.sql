CREATE TABLE IF NOT EXISTS app_saas_bill_supplier_direct_assign_summary_di(
  `tenant_id` BIGINT COMMENT '租户ID，取值范围：8-123',
  `supplier_id` BIGINT COMMENT '供应商ID，取值范围：0-3405，0表示无供应商',
  `time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd，表示年月日',
  `total_purchase_amount` DECIMAL(38,18) COMMENT '采购金额总计（元）',
  `total_purchase_amount_wechat_pay` DECIMAL(38,18) COMMENT '采购金额总计-微信支付（元）',
  `total_purchase_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '采购金额总计-账期加余额支付（元）',
  `total_amount_of_purchase_and_after_sales_wechat_pay` DECIMAL(38,18) COMMENT '等比换算后采购售后金额合计-微信支付（元）',
  `total_amount_of_purchase_and_after_sales_bill_balance_pay` DECIMAL(38,18) COMMENT '等比换算后采购售后金额合计-账期和余额支付（元）',
  `total_purchase_amount_remove_refund_wechat_pay` DECIMAL(38,18) COMMENT '扣除售后的采购金额-微信支付（元）',
  `total_purchase_amount_remove_refund_bill_balance_pay` DECIMAL(38,18) COMMENT '扣除售后的采购金额-账期和余额支付（元）',
  `sales_and_purchase_difference_wechat_pay` DECIMAL(38,18) COMMENT '销售与采购差额(剔除售后,不含运费)-微信支付（元）',
  `sales_and_purchase_difference_bill_balance_pay` DECIMAL(38,18) COMMENT '销售与采购差额(剔除售后,不含运费)-账期加余额支付（元）',
  `total_sales_amount` DECIMAL(38,18) COMMENT '销售金额总计（元）',
  `total_sales_amount_wechat_pay` DECIMAL(38,18) COMMENT '销售金额总计-微信支付（元）',
  `total_sales_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '销售金额总计-账期加余额支付（元）',
  `after_sale_amount_wechat_pay` DECIMAL(38,18) COMMENT '销售售后金额-微信支付（元）',
  `after_sale_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '销售售后金额-账期加余额支付（元）',
  `deduct_after_sales_amount_wechat_pay` DECIMAL(38,18) COMMENT '扣除售后销售金额-微信支付（元）',
  `deduct_after_sales_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '扣除售后销售金额-账期加余额支付（元）',
  `goods_delivery_fee_remove_refund` DECIMAL(38,18) COMMENT '供应商配送费扣除售后（元）',
  `delivery_fee_deduct_after_sales_amount` DECIMAL(38,18) COMMENT '剔除售后的订单配送费（元）'
) 
COMMENT 'SAAS对账单-供应商直配账单概要表，包含供应商直配业务的采购、销售、售后等金额统计信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
   'comment'='SAAS对账单-供应商直配账单概要表，按天分区存储供应商直配业务的财务统计信息') 
LIFECYCLE 30;