CREATE TABLE IF NOT EXISTS app_crm_sku_performance_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
    `sku_id` STRING COMMENT '商品SKU ID，唯一标识一个商品规格',
    `spu_name` STRING COMMENT '商品名称，即标准产品单元名称',
    `sku_spec` STRING COMMENT '商品规格描述，如包装规格、重量等',
    `sku_category` STRING COMMENT '商品类目：枚举值包括自营品牌、鲜果、乳制品、非乳制品',
    `category4` STRING COMMENT '四级分类，商品更细粒度的分类',
    `administrative_city` STRING COMMENT '商家对应的行政城市名称',
    `is_simple` STRING COMMENT '是否单店：枚举值包括是、否',
    `cust_type` STRING COMMENT '客户类型：枚举值包括面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、糖水/水果捞、西餐披萨、其他',
    `sku_cnt` BIGINT COMMENT '商品销售数量',
    `real_gmv_amt` DECIMAL(38,18) COMMENT '订单实付金额，实际支付金额',
    `origin_gmv_amt` DECIMAL(38,18) COMMENT '订单应付金额，原始订单金额',
    `deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约实付金额，实际履约支付金额',
    `deliver_cost` DECIMAL(38,18) COMMENT '履约成本，履约过程中产生的成本',
    `deliver_cust_cnt` BIGINT COMMENT '履约客户数，完成履约的客户数量',
    `order_cnt` BIGINT COMMENT '订单数，销售订单数量',
    `cust_cnt` BIGINT COMMENT '订单客户数，下单的客户数量',
    `m1` STRING COMMENT '城市负责人（M1），城市级别的管理人员',
    `m2` STRING COMMENT '区域负责人（M2），区域级别的管理人员',
    `m3` STRING COMMENT '部门负责人（M3），部门级别的管理人员'
)
COMMENT 'SKU粒度平台销售业绩报表周汇总表，按SKU维度统计的销售业绩数据，每周汇总一次'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SKU粒度平台销售业绩报表周汇总表',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;