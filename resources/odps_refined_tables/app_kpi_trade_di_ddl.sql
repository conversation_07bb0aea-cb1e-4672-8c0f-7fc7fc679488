```sql
CREATE TABLE IF NOT EXISTS app_kpi_trade_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
    `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额：订单原始总金额，包含各种费用',
    `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额：客户实际支付金额，扣除优惠等',
    `cust_cnt` BIGINT COMMENT '客户数：去重后的客户数量',
    `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(平均每用户收入)：应付总金额/客户数，衡量客户价值',
    `order_cnt` BIGINT COMMENT '订单数：总订单数量',
    `order_avg` DECIMAL(38,18) COMMENT '订单均价：应付总金额/订单数，平均每个订单金额',
    `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额：商品未送达情况下的售后金额',
    `after_sale_rate` DECIMAL(38,18) COMMENT '退货率：未到货售后总金额/应付总金额，衡量售后情况',
    `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额：直发模式的采购金额',
    `delivery_amt` DECIMAL(38,18) COMMENT '运费：运费+超时加单费的总和',
    `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额：定时配送服务的订单金额'
)
COMMENT '交易口径KPI指标日汇总表，包含交易相关的核心业务指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，用于按天分区管理数据'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '交易核心指标日粒度汇总表，用于业务分析和监控',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```