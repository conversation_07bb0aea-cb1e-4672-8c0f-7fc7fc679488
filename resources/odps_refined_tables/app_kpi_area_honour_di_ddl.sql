CREATE TABLE IF NOT EXISTS app_kpi_area_honour_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
	`area_no` BIGINT COMMENT '城配仓编号，取值范围：1-152',
	`area_name` STRING COMMENT '城配仓名称，枚举值包括：杭州仓、上海仓、宁波仓、苏州仓、嘉兴仓、广州仓、南京仓、湖州仓、无锡仓、金丽衢仓、成都仓、温州仓、东莞仓、深圳仓、佛山仓、重庆仓、台州仓、合肥仓、扬州仓、淮安仓、中山仓、惠州仓、福州仓、泉州仓、厦门仓、长沙仓、南昌仓、武汉仓、莆田仓、荆门仓、常德仓、宜春仓、南宁仓、昆明仓、南通仓、贵阳仓、潮汕仓、青岛仓、襄阳仓、衡阳仓、杭州三仓、上海六仓、盐城仓、慈溪仓、徐州仓、株洲仓、烟台仓、秀洲仓、东莞快递履约仓、益禾堂快递虚拟仓等',
	`sku_type` STRING COMMENT '商品类型，枚举值：自营-自营商品，代仓-代仓商品，代售-代售商品',
	`origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额，单位：元',
	`real_total_amt` DECIMAL(38,18) COMMENT '实际总金额，单位：元',
	`cost_amt` DECIMAL(38,18) COMMENT '商品成本价，单位：元',
	`preferential_amt` DECIMAL(38,18) COMMENT '营销金额，单位：元',
	`origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率，计算公式：(原始总金额-商品成本价)/原始总金额',
	`real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率，计算公式：(实际总金额-商品成本价)/实际总金额',
	`refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额，单位：元',
	`cust_cnt` BIGINT COMMENT '客户数，取值范围：1-391',
	`cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价，单位：元/客户，计算公式：实际总金额/客户数',
	`order_cnt` BIGINT COMMENT '订单数，取值范围：1-476',
	`point_cnt` BIGINT COMMENT '点位数，取值范围：1-407',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本，单位：元',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，单位：元',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本，单位：元',
	`total_deliver_amt` DECIMAL(38,18) COMMENT '履约总费用，单位：元，计算公式：仓储成本+干线成本+配送成本'
) 
COMMENT '履约KPI城配仓维度日表，按城配仓维度统计每日的履约相关KPI指标，包括销售额、成本、毛利率、客户数、订单数、履约费用等'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='履约KPI城配仓维度日表，用于城配仓维度的履约绩效分析和监控') 
LIFECYCLE 30;