CREATE TABLE IF NOT EXISTS app_log_mall_sku_di(
	`date` STRING COMMENT '日期，格式：yyyyMMdd',
	`module_name` STRING COMMENT '模块名称，枚举值：总量,个人中心,优惠券,支付,个人抽奖,中转页,搜索,注册信息,活动页,特价专区,选择地址,采购助手,首页商品,再来一单,首页,首页弹窗,分类,常用推荐,换购,省心送,meetings,优惠券商品,其他,购物车',
	`page_name_chs` STRING COMMENT '页面中文名称，枚举值：总量,homeBanner,售后详情,帐号详情,我的订单,订单详情,优惠券可用商品,优惠券可用商品goods,优惠券可用商品唤起购买,其他,其他goods,其他加购,其他唤起购买,其他立即购买,底部分类,底部分类唤起购买,首页分类,首页分类唤起购买',
	`sku_id` STRING COMMENT 'SKU唯一标识',
	`spu_id` BIGINT COMMENT 'SPU ID，商品ID',
	`spu_name` STRING COMMENT 'SPU名称，商品名称',
	`sku_spec` STRING COMMENT '商品规格描述',
	`sku_type` STRING COMMENT '商品类型，枚举值：自营,代仓,代售',
	`category1` STRING COMMENT '商品一级分类，枚举值：其他,乳制品,鲜果',
	`category2` STRING COMMENT '商品二级分类，枚举值：成品原料,谷物制品,乳制品,饮料,饼干丨糖果丨可可豆制品,调味品,坚果制品,肉丨肉制品,新鲜水果,水果制品,茶制品,包材,饮品原料,其他,POP—鲜果,食用油丨油脂及制品,酒,糖丨糖制品,蛋丨蛋制品,糕点丨面包,食品添加剂,蔬菜制品,新鲜蔬菜,海鲜｜水产品｜制品',
	`category3` STRING COMMENT '商品三级分类，枚举值：方便速食,谷物加工品,黄油,植物蛋白饮料,饼干,半固体(酱),烘炒类,混合肉类,柑果类,奶酪丨芝士,糖果,烘焙类原料,面粉丨小麦粉,冻干水果,茶粉,粉圆类配料,五谷杂粮,其他液体调味料,配料（小料）类,预拌粉,封口膜｜盖｜夹,其他耗材,果汁原料,礼品,葡萄,枣,水果风味制品,其他鲜果,包装袋丨盒,装饰,浆果类,瓠果类,稀奶油,仁果类,液体乳,食用油脂制品,碳酸饮料,蒸馏酒,咖啡豆及其制品,可可豆制品,谷物罐头,砂糖,糖浆,果冻类配料,核果类,冷冻饮品,果蔬汁,炼乳,饮料冲调粉,配制酒等',
	`category4` STRING COMMENT '商品四级分类，枚举值：速食面,意面,乳酸黄油,其他植物蛋白饮料,饼干碎,混合黄油,其他半固体(酱),坚果,冷冻冷藏预制调理生混合肉,热加工熟混合肉制品,腌腊熏生混合肉制品,柠檬,奶油奶酪,布拉塔奶酪,硬质糖果,蛋挞液,其他面粉(法式日式),冻干粉,红茶粉,麻薯,红豆,食用醋,切达再制干酪,配料（小料）,糯米预拌粉,塑料制品封口盖,标签贴纸,果汁/糖浆/调味酱类,周边产品,抹茶粉,阳光玫瑰,冬枣,马苏里拉,果泥丨果茸,西梅,纸制包装袋,生日蜡烛,火龙果,无盐黄油,有盐黄油,柚,西瓜,蜜瓜,搅打型稀奶油,梨,鲜牛奶,凤梨丨菠萝,植脂奶油,浓缩果汁,果汁原浆等',
	`sku_exposure_pv` BIGINT COMMENT '商品曝光页面浏览量',
	`sku_exposure_uv` BIGINT COMMENT '商品曝光独立访客数',
	`sku_click_pv` BIGINT COMMENT '商品点击页面浏览量',
	`sku_click_uv` BIGINT COMMENT '商品点击独立访客数',
	`sku_detail_exposure_pv` BIGINT COMMENT '商品详情页曝光页面浏览量',
	`sku_detail_exposure_uv` BIGINT COMMENT '商品详情页曝光独立访客数',
	`sku_detail_cart_buy_pv` BIGINT COMMENT '商品详情页加购点击页面浏览量',
	`sku_detail_cart_buy_uv` BIGINT COMMENT '商品详情页加购点击独立访客数',
	`sku_detail_instant_buy_pv` BIGINT COMMENT '商品详情页立即购买点击页面浏览量',
	`sku_detail_instant_buy_uv` BIGINT COMMENT '商品详情页立即购买点击独立访客数',
	`sku_arouse_click_pv` BIGINT COMMENT '商品唤起点击页面浏览量',
	`sku_arouse_click_uv` BIGINT COMMENT '商品唤起点击独立访客数',
	`sku_arouse_cart_buy_pv` BIGINT COMMENT '商品唤起加购点击页面浏览量',
	`sku_arouse_cart_buy_uv` BIGINT COMMENT '商品唤起加购点击独立访客数',
	`sku_arouse_instant_buy_pv` BIGINT COMMENT '商品唤起立即购买点击页面浏览量',
	`sku_arouse_instant_buy_uv` BIGINT COMMENT '商品唤起立即购买点击独立访客数',
	`sku_cart_buy_pv` BIGINT COMMENT '商品加购页面浏览量',
	`sku_cart_buy_uv` BIGINT COMMENT '商品加购独立访客数',
	`sku_instant_buy_pv` BIGINT COMMENT '商品立即购买页面浏览量',
	`sku_instant_buy_uv` BIGINT COMMENT '商品立即购买独立访客数',
	`sku_cart_instant_buy_pv` BIGINT COMMENT '商品加购+立即购买页面浏览量',
	`sku_cart_instant_buy_uv` BIGINT COMMENT '商品加购+立即购买独立访客数',
	`sku_expand_pay_pv` BIGINT COMMENT '商品拓展购买支付页面浏览量',
	`sku_expand_pay_uv` BIGINT COMMENT '商品拓展购买支付独立访客数',
	`sku_timing_pay_pv` BIGINT COMMENT '商品省心送支付页面浏览量',
	`sku_timing_pay_uv` BIGINT COMMENT '商品省心送支付独立访客数',
	`sku_pay_pv` BIGINT COMMENT '商品确认支付页面浏览量',
	`sku_pay_uv` BIGINT COMMENT '商品确认支付独立访客数',
	`sku_remind_pv` BIGINT COMMENT '商品提醒页面浏览量',
	`sku_remind_uv` BIGINT COMMENT '商品提醒独立访客数',
	`sku_arrival_remind_pv` BIGINT COMMENT '商品到货提醒页面浏览量',
	`sku_arrival_remind_uv` BIGINT COMMENT '商品到货提醒独立访客数',
	`sku_arouse_remind_pv` BIGINT COMMENT '商品唤起提醒页面浏览量',
	`sku_arouse_remind_uv` BIGINT COMMENT '商品唤起提醒独立访客数',
	`sku_order_order_cnt` BIGINT COMMENT '购买次数',
	`sku_order_cust_cnt` BIGINT COMMENT '购买人数',
	`login_uv` BIGINT COMMENT '登陆独立访客数',
	`login_pv` BIGINT COMMENT '登陆页面浏览量'
) 
COMMENT '商城SKU流量分析表，包含商品维度的流量指标统计（新逻辑从20230523开始）'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商城SKU流量分析表，用于分析商品维度的用户行为数据，包括曝光、点击、加购、购买等关键指标')
LIFECYCLE 30;