```sql
CREATE TABLE IF NOT EXISTS app_cust_mtd_performance_mi(
    `last_bd_name` STRING COMMENT '最新归属BD姓名',
    `last_bd_id` STRING COMMENT 'BD_ID，销售人员的唯一标识',
    `bd_region` STRING COMMENT '大区名称，枚举值：浙江大区、电销、上海大区、苏皖大区、华南大区、西南大区、闽桂大区、华中大区、山东大区、昆明大区',
    `bd_work_zone` STRING COMMENT '区域名称，枚举值：杭州、浙南、电销、浦西、杭州湾、苏州、浦东、无锡、深圳、徽京、四川、苏北、佛山、东莞、重庆、广州、福泉、大粤西、厦门、江西、武汉、长沙、青岛、广西、昆明、贵阳、济南',
    `cust_id` STRING COMMENT '客户ID，客户的唯一标识',
    `last_cust_name` STRING COMMENT '客户名称',
    `cust_dlv_type` STRING COMMENT '客户类型，枚举值：A-高价值客户，非A-普通客户',
    `total_score_num` DOUBLE COMMENT 'BD利润积分累计，数值型指标',
    `bd_performance_rate` DOUBLE COMMENT '利润积分系数，绩效计算系数',
    `dlv_cust_cnt` BIGINT COMMENT '履约客户数，已履约的客户数量',
    `cust_comm_amt` DOUBLE COMMENT '客户数佣金，基于客户数量的佣金金额',
    `dlv_ori_amt` DOUBLE COMMENT '履约应付GMV，履约订单的原始应付金额',
    `dlv_real_amt` DOUBLE COMMENT '履约实付GMV，履约订单的实际支付金额',
    `item_profit_amt` DOUBLE COMMENT '自营商品毛利润，自营商品的毛利润金额',
    `dlv_real_amt_at` DOUBLE COMMENT 'AT_履约实付金额，AT品类履约实付金额',
    `dlv_real_amt_expo` DOUBLE COMMENT '流量品_履约实付金额，流量品类履约实付金额',
    `dlv_real_amt_profit` DOUBLE COMMENT '利润品_履约实付金额，利润品类履约实付金额',
    `dlv_real_amt_normal` DOUBLE COMMENT '常规品_履约实付金额，常规品类履约实付金额',
    `dlv_real_amt_fruit` DOUBLE COMMENT '鲜果_履约实付金额，鲜果品类履约实付金额',
    `cate_group_score_num` DOUBLE COMMENT '利润得分，基于品类分组的利润得分',
    `dlv_spu_cnt` BIGINT COMMENT '履约SPU数，已履约的SPU数量',
    `more_than_spu_cnt` BIGINT COMMENT '超额SPU数，超过标准的SPU数量',
    `more_than_spu_comm` DOUBLE COMMENT '超额SPU数佣金，超额SPU的佣金金额',
    `more_than_spu_cust` BIGINT COMMENT '超额SPU客户数，拥有超额SPU的客户数量',
    `total_comm_amt` DOUBLE COMMENT '高价值客户佣金汇总，高价值客户的总佣金金额',
    `is_test_bd` STRING COMMENT '是否测试BD，枚举值：1-测试BD，0-正式BD',
    `dlv_planned_spu_cnt` BIGINT COMMENT '出库任务新增SPU数，出库任务中新增的SPU数量',
    `dlv_planned_real_amt` DOUBLE COMMENT '出库任务中新增履约金额，出库任务中新增的履约金额',
    `cust_value_lable` STRING COMMENT '高价值客户标签，枚举值：普通客户、高价值客户、潜力高价值客户、准高价值客户',
    `arpu_target` DOUBLE COMMENT '高价值ARPU标准，高价值客户的ARPU目标值',
    `spu_target` DOUBLE COMMENT '高价值SPU标准，高价值客户的SPU目标值',
    `arpu_comm_amt` DOUBLE COMMENT '超ARPU激励金额，超过ARPU标准的激励金额'
)
COMMENT '单客户MTD维度绩效表现表，记录每个客户在月度至今(MTD)时间维度下的绩效表现数据，包括BD利润积分、履约情况、佣金计算等核心业务指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，数据日期，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '单客户MTD维度绩效表现表',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 720
```