CREATE TABLE IF NOT EXISTS app_warehouse_in_out_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`warehouse_no` BIGINT COMMENT '仓库编号',
	`warehouse_name` STRING COMMENT '仓库名称，枚举值包括：东莞总仓、南京总仓、长沙总仓、重庆总仓、南宁总仓等100+个仓库名称',
	`category1` STRING COMMENT '商品一级分类，枚举值：乳制品、鲜果、其他',
	`allocation_out_cnt` BIGINT COMMENT '调拨出库发货量',
	`allocation_out_cost` DECIMAL(38,18) COMMENT '调拨出库发货总成本',
	`sale_out_cnt` BIGINT COMMENT '销售出库发货量',
	`sale_out_cost` DECIMAL(38,18) COMMENT '销售出库发货总成本',
	`selef_sale_out_cnt` BIGINT COMMENT '销售自提出库发货量',
	`selef_sale_out_cost` DECIMAL(38,18) COMMENT '销售自提出库发货总成本',
	`sample_out_cnt` BIGINT COMMENT '出样出库发货量',
	`sample_out_cost` DECIMAL(38,18) COMMENT '出样出库发货总成本',
	`damage_out_cnt` BIGINT COMMENT '货损出库发货量',
	`damage_out_cost` DECIMAL(38,18) COMMENT '货损出库发货总成本',
	`transfer_out_cnt` BIGINT COMMENT '转换出库发货量',
	`transfer_out_cost` DECIMAL(38,18) COMMENT '转换出库发货总成本',
	`purchase_back_cnt` BIGINT COMMENT '采购退货出库发货量',
	`purchase_back_cost` DECIMAL(38,18) COMMENT '采购退货出库发货总成本',
	`replenish_out_cnt` BIGINT COMMENT '补货出库发货量',
	`replenish_out_cost` DECIMAL(38,18) COMMENT '补货出库发货总成本',
	`allocation_in_cnt` BIGINT COMMENT '调拨入库收货量',
	`allocation_in_cost` DECIMAL(38,18) COMMENT '调拨入库收货总成本',
	`purchase_in_cnt` BIGINT COMMENT '采购入库收货量',
	`purchase_in_cost` DECIMAL(38,18) COMMENT '采购入库收货总成本',
	`return_in_cnt` BIGINT COMMENT '退货入库收货量',
	`return_in_cost` DECIMAL(38,18) COMMENT '退货入库收货总成本',
	`transfer_in_cnt` BIGINT COMMENT '转换入库收货量',
	`transfer_in_cost` DECIMAL(38,18) COMMENT '转换入库收货总成本'
)
COMMENT '出入库仓库维度按天汇总表，记录各仓库每日各类出入库业务的数量和成本统计'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = '出入库仓库维度按天汇总表，包含调拨、销售、采购、退货等各类出入库业务的量和成本统计',
	'columnar.nested.type' = 'true'
)
LIFECYCLE 30;