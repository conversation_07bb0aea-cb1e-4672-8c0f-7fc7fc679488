CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_big_cust_delivery_after_sale_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
  `big_cust` STRING COMMENT '大客户名称，枚举值包括：喜茶、乐蔻、茶大将、苏阁、海底捞、到点咖啡',
  `sku_type` STRING COMMENT '商品类型，枚举值包括：自营、代仓',
  `point_cnt` BIGINT COMMENT '配送点位数，统计配送服务的网点数量',
  `delay_point_cnt` BIGINT COMMENT '延迟配送点位数，统计配送延迟的网点数量',
  `order_cnt` BIGINT COMMENT '订单数量，统计配送订单总数',
  `origin_total_amt` DECIMAL(38,18) COMMENT '配送应付GMV，配送服务应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '配送实付GMV，配送服务实际支付总金额',
  `delivary_amt` DECIMAL(38,18) COMMENT '配送费用，配送服务产生的费用',
  `out_sku_cnt` BIGINT COMMENT '出库商品件数，统计出库的商品数量',
  `short_sku_cnt` BIGINT COMMENT '缺货商品件数，统计缺货的商品数量',
  `after_order_cnt` BIGINT COMMENT '售后订单数量，统计售后服务的订单数量',
  `after_total_amt` DECIMAL(38,18) COMMENT '售后金额，售后服务产生的总金额'
) 
COMMENT '大客户配送信息汇总表，包含大客户配送业务的各项指标统计，如点位数、订单数、GMV、配送费、出库缺货情况以及售后数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，用于数据管理和查询优化'
)
STORED AS ALIORC  
TBLPROPERTIES ('comment'='大客户配送信息汇总表，用于分析大客户配送业务的运营情况和售后服务质量')
LIFECYCLE 30;