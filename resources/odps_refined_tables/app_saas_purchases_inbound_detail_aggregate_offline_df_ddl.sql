```sql
CREATE TABLE IF NOT EXISTS app_saas_purchases_inbound_detail_aggregate_offline_df(
    `inbound_time` DATETIME COMMENT '入库时间，格式：年月日时分秒',
    `batch_no` STRING COMMENT '采购批次编号，枚举值示例：20230112178790149、20230112178712145等',
    `inbound_stock` BIGINT COMMENT '实际入库数量',
    `inbound_price` DECIMAL(38,18) COMMENT '入库总金额，保留18位小数精度',
    `purchases_stock` BIGINT COMMENT '采购计划数量',
    `purchases_price` DECIMAL(38,18) COMMENT '采购计划金额，保留18位小数精度',
    `sku_no` STRING COMMENT '商品SKU编号，枚举值示例：1038882864152、1038281116414等',
    `sku_name` STRING COMMENT '商品名称，枚举值示例：测试商品、香辣腿肉-川町太郎、速冻草莓（人在茶在）等',
    `specification` STRING COMMENT '商品规格描述，枚举值示例：0_1箱*2斤包、0_2G*2KG包、20片*6包等',
    `packaging` STRING COMMENT '包装单位，枚举值：包、箱、袋、组、桶、罐、盒、瓶',
    `saas_sku_no` STRING COMMENT 'SaaS系统SKU编号，枚举值示例：100922、100924、101168等',
    `saas_sku_name` STRING COMMENT 'SaaS系统商品名称，枚举值示例：测试商品、香辣腿肉-川町太郎、速冻草莓（人在茶在）等',
    `saas_specification` STRING COMMENT 'SaaS系统商品规格描述，枚举值示例：1箱*2斤、2G*2KG、20片*6包等',
    `saas_packaging` STRING COMMENT 'SaaS系统包装单位，枚举值：包、箱、组、桶、罐、袋、盒、瓶',
    `inbound_create_user_id` BIGINT COMMENT '采购创建人用户ID',
    `inbound_create_user_name` STRING COMMENT '采购创建人姓名，枚举值：1、IMG艾米高管理员、人在茶在管理员、艾炒酸奶的订货商城管理员、悟空测试、每口咖管理员',
    `inbound_create_user_phone` STRING COMMENT '采购创建人联系电话',
    `warehouse_id` BIGINT COMMENT '仓库ID',
    `warehouse_name` STRING COMMENT '仓库名称，枚举值：重构测试仓、测试总仓、广州总仓、上海总仓、嘉兴总仓、杭州总仓、昆明总仓、苏州总仓',
    `tenant_id` BIGINT COMMENT '租户ID',
    `tenant_name` STRING COMMENT '租户名称，枚举值：鲜沐商城、IMG艾米高、人在茶在、艾炒酸奶的订货商城、每口咖',
    `supplier_id` BIGINT COMMENT '供应商ID',
    `supplier_name` STRING COMMENT '供应商名称，枚举值示例：大鹏测试个人、大鹏测试、龙岩市森汇食品有限公司等',
    `supplier_type` BIGINT COMMENT '供应商类型，枚举值：0-普通供应商、1-测试供应商、2-其他类型供应商'
)
COMMENT '采购入库单离线聚合表，包含采购入库的详细信息，包括商品信息、仓库信息、租户信息和供应商信息等'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '采购入库单离线聚合明细表',
    'lifecycle' = '30'
)
```