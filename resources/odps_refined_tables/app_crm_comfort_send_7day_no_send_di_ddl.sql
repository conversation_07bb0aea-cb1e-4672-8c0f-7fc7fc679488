```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_comfort_send_7day_no_send_di`(
  `m_id` BIGINT COMMENT '商户ID，唯一标识商户',
  `order_no` STRING COMMENT '订单编号，唯一标识订单',
  `order_time` DATETIME COMMENT '订单生成时间，格式为年月日时分秒',
  `sku_id` STRING COMMENT '商品SKU编码，唯一标识商品规格',
  `pd_name` STRING COMMENT '商品名称',
  `pd_weight` STRING COMMENT '商品规格重量描述',
  `pd_amount` BIGINT COMMENT '订单内商品数量，取值范围：1-700',
  `pay_amount` DECIMAL(38,18) COMMENT '实付总额，精确到小数点后18位',
  `area_no` BIGINT COMMENT '商户所在运营区域编码，取值范围：1001-44264',
  `bd_id` BIGINT COMMENT '归属BD ID，公海为0，取值范围：0-1180780',
  `day_tag` STRING COMMENT '数据所在日标记，格式为yyyyMMdd',
  `m_name` STRING COMMENT '商户名称',
  `province` STRING COMMENT '省份名称，枚举值包括：湖南、江西、上海、浙江、广东、江苏、安徽、湖北、重庆、广西壮族自治区、福建、四川、山东、贵州、云南等',
  `city` STRING COMMENT '城市名称',
  `area` STRING COMMENT '区县名称'
)
COMMENT 'CRM近7天未配送省心送订单表，记录近7天内未完成配送的省心送订单信息'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'CRM近7天未配送省心送订单明细表，用于跟踪和分析未及时配送的省心送订单情况',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```