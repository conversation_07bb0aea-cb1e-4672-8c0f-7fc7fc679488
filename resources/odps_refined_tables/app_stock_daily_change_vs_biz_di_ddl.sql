CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_stock_daily_change_vs_biz_di` (
  `pt` STRING COMMENT '数据批次标识，格式为yyyyMMdd',
  `change_date` DATETIME COMMENT '库存变更日期，格式为年月日时分秒',
  `warehouse_no` BIGINT COMMENT '仓库编码，唯一标识仓库',
  `warehouse_name` STRING COMMENT '仓库名称，如：嘉兴总仓、华西总仓等',
  `sku` STRING COMMENT '商品SKU编码，唯一标识商品',
  `pd_name` STRING COMMENT '商品名称，描述商品的具体名称',
  `specifications` STRING COMMENT '商品规格描述，包含包装规格等信息',
  `change_type` STRING COMMENT '库存变更类型，取值范围：出样出库、销售出库、货损出库、退货入库、盘亏出库、盘盈入库',
  `stock_change_quantity` BIGINT COMMENT '库存流水变更数量，记录实际库存变动',
  `biz_change_quantity` BIGINT COMMENT '业务变更数量，记录业务单据对应的数量'
)
COMMENT '库存每日变更对比业务单，用于对比库存流水变更与业务单据变更的数量差异'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '库存每日变更对比业务单分析表',
  'lifecycle' = '30'
);