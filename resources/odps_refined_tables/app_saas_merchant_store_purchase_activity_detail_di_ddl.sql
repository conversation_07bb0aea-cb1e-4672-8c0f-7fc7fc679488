CREATE TABLE IF NOT EXISTS app_saas_merchant_store_purchase_activity_detail_di(
	tenant_id BIGINT COMMENT '租户ID，取值范围：2-99',
	time_tag STRING COMMENT '时间标签，格式：yyyyMMdd（年月日）',
	store_id BIGINT COMMENT '门店ID，取值范围：1-544759',
	purchased_amount_7d BIGINT COMMENT '前7日采购订单数，取值范围：0-18，均值：0.39，标准差：1.26',
	purchased_amount_30d BIGINT COMMENT '前30日采购订单数，取值范围：0-68，均值：1.65，标准差：4.88'
) 
COMMENT 'SaaS门店采购活跃明细表，记录各门店在不同时间段的采购订单活跃情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS门店采购活跃明细表，用于分析门店采购行为活跃度') 
LIFECYCLE 30;