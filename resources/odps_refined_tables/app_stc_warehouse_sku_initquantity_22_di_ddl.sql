```sql
CREATE TABLE IF NOT EXISTS app_stc_warehouse_sku_initquantity_22_di(
    `date` STRING COMMENT '日期，格式：yyyyMMdd',
    `warehouse_no` BIGINT COMMENT '库存仓ID',
    `warehouse_name` STRING COMMENT '库存仓名称，枚举值：上海总仓、山东总仓、嘉兴总仓、华西总仓、重庆总仓、福州总仓、长沙总仓、南宁总仓、昆明总仓、苏州总仓、贵阳总仓、青岛总仓、东莞总仓',
    `batch_no` STRING COMMENT '批次编号',
    `sku_id` STRING COMMENT 'SKU编号',
    `sku_property` STRING COMMENT 'SKU属性，枚举值：常规、活动、临保、拆包、不卖、破袋',
    `spu_id` BIGINT COMMENT 'SPU ID（产品ID）',
    `spu_no` STRING COMMENT 'SPU编号',
    `spu_name` STRING COMMENT 'SPU名称',
    `sku_disc` STRING COMMENT 'SKU描述（规格描述）',
    `category` STRING COMMENT '一级类目，枚举值：乳制品、其他、鲜果',
    `storage_way` STRING COMMENT '存储方式，枚举值：冷冻、冷藏、常温',
    `pack_unit` STRING COMMENT '包装单位，枚举值：箱、袋、筐、包、瓶、盒、桶、罐、份、卷、个、件、块、组、条、台、套',
    `production_date` DATETIME COMMENT '生产日期，格式：yyyy-MM-dd HH:mm:ss',
    `quality_date` DATETIME COMMENT '保质期，格式：yyyy-MM-dd HH:mm:ss',
    `init_quantity` BIGINT COMMENT '期初库存数量',
    `warehouse_sku_quantity` BIGINT COMMENT '仓库库存数量'
)
COMMENT 'SKU每日期初库存量表，记录每日各SKU的期初库存和仓库库存信息'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SKU期初库存明细表，包含产品基本信息、库存信息和存储方式等',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```