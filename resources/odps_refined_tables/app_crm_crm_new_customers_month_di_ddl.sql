CREATE TABLE IF NOT EXISTS app_crm_crm_new_customers_month_di(
    m_id BIGINT COMMENT '商户ID，唯一标识一个商户，取值范围：6453-576035',
    bd_id BIGINT COMMENT '销售ID，唯一标识一个销售人员，取值范围：405-1189140',
    day_tag STRING COMMENT '拉新日期，格式为yyyyMMdd，表示年月日，取值范围：20250901-20250922之间的日期'
)
COMMENT '月度拉新客户表，记录每月新增客户信息，包含商户、销售和拉新日期等维度数据'
PARTITIONED BY (
    ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日，用于按天分区管理数据'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '月度拉新客户事实表，用于分析每月新增客户情况和销售业绩',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;