```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_pop_biaoguo_products_df` (
  `category_name` STRING COMMENT '完整类目名称，如：水果-自动化_火龙果_进口红心',
  `category1` STRING COMMENT '一级类目，枚举值：水果-自动化',
  `category2` STRING COMMENT '二级类目，枚举值：火龙果、菠萝蜜、小众水果、百香果、柠檬、凤梨、香蕉、更多水果、椰子、橙、葡萄、小番茄、水果黄瓜、梨、猕猴桃/奇异果、山竹、荔枝、桃、苹果、蜜瓜、牛油果、木瓜、蔬菜水果、榴莲、橘/桔、西瓜、蓝莓浆果、芭乐、柚、樱桃/车厘子、芒果、今日推荐、柑、香瓜甜瓜、莲雾、人参果、李、提子、枣、杏、杨梅',
  `category3` STRING COMMENT '三级类目，如：进口红心、白心火龙果、红菠萝蜜、水果南瓜、黄金百香果、黄柠檬等',
  `back_category_name` STRING COMMENT '后台类目名称，如：进口红心、白心火龙果、今日推荐等',
  `id` STRING COMMENT '商品唯一标识',
  `competitor` STRING COMMENT '竞争对手，枚举值：标果-杭州',
  `sku_code` STRING COMMENT 'SKU编码',
  `goods_name` STRING COMMENT '商品名称',
  `baby_name` STRING COMMENT '商品详情的描述',
  `standard_price` STRING COMMENT '标准价格（单位：元）',
  `final_standard_price` STRING COMMENT '最终标准价格（单位：元）',
  `last_time_standard_price` STRING COMMENT '上次标准价格（单位：元）',
  `final_unit_price_catty` STRING COMMENT '最终市斤价格（单位：元/斤）',
  `unit_price_catty` STRING COMMENT '单位市斤价格（单位：元/斤）',
  `goods_type` STRING COMMENT '商品类型，枚举值：ORDINARY',
  `specification` STRING COMMENT '规格，如：约31.5斤、约36斤等',
  `unit` STRING COMMENT '单位，枚举值：PIECE（件）、SINGLE（个）、BAG（袋）、BOX（盒）、BOX_CASE（箱）',
  `gross_weight` STRING COMMENT '毛重（单位：斤）',
  `net_weight` STRING COMMENT '净重（单位：斤）',
  `month_sale` STRING COMMENT '月销量',
  `goods_siphon_commission_rate` STRING COMMENT '商品吸客佣金比例（单位：%），枚举值：9.0、13.9997、7.0002、11.9999、3.9999、9.9999、5.0、6.888、6.9748、6.0、11.0',
  `seller_siphon_commission_rate` STRING COMMENT '卖家吸客佣金比例（单位：%），枚举值：5.5',
  `seller_name` STRING COMMENT '卖家名称，枚举值：公孙策、小敏果品、鸿宸果业、小二、旺旺、小毅、十二楼果业、大司马、小霸王、大华、小强、熊掌柜、天涯、一鸣、王子、木木、不凡',
  `goods_prop_detail_list` STRING COMMENT '商品属性详情列表，JSON格式字符串',
  `url` STRING COMMENT '商品链接URL',
  `seven_day_after_sale` STRING COMMENT '七天售后服务数量',
  `spider_fetch_time` STRING COMMENT '爬虫抓取时间，格式：yyyy-MM-dd HH:mm:ss',
  `create_time` STRING COMMENT '商品创建时间（来自标果），格式：yyyy-MM-dd HH:mm:ss'
)
COMMENT 'POP标果商品详细信息明细表，包含商品类目、价格、规格、销量、佣金比例等详细信息'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '数据日期分区，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
  'comment'='POP标果商品详细信息明细表，包含商品类目、价格、规格、销量、佣金比例等详细信息')
LIFECYCLE 180;
```