CREATE TABLE IF NOT EXISTS app_finance_store_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`service_area` STRING COMMENT '大区，取值范围：云南、华东、华中、山东、广西、未知、福建、西南、贵州',
	`warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识',
	`warehouse_name` STRING COMMENT '库存仓名称，取值范围：昆明总仓、上海总仓、嘉兴总仓、苏州总仓、长沙总仓、青岛总仓、南宁总仓、东莞总仓、美团优选杭州一仓、美团优选杭州二仓、美团优选上海仓、美团虚拟代下单总仓、自营奶虚拟仓、美团代加工虚拟仓、东莞冷冻总仓、嘉兴海盐总仓、南京总仓、济南总仓、武汉总仓、福州总仓、华西总仓、重庆总仓、贵阳总仓',
	`category1` STRING COMMENT '商品一级类目，取值范围：鲜果、乳制品、其他',
	`store_amt` DECIMAL(38,18) COMMENT '含税在库金额，单位：元',
	`store_amt_notax` DECIMAL(38,18) COMMENT '不含税在库金额，单位：元',
	`road_amt` DECIMAL(38,18) COMMENT '含税在途金额，单位：元',
	`road_amt_notax` DECIMAL(38,18) COMMENT '不含税在途金额，单位：元'
) 
COMMENT '财务口径收入数据表，包含各仓位的库存和在途金额数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='财务口径收入数据表，按日期分区存储各仓位的财务数据') 
LIFECYCLE 30;