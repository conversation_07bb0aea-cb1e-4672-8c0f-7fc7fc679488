CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_puchase_transfer_di`(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`type` STRING COMMENT '实验分组类型，取值范围：V3、V4、对照组、空字符串（表示未知分组）',
	`category` STRING COMMENT '功能模块分类，取值范围：搜索模块、分类模块、采购助手模块、搜索页面、分类页面、采购助手',
	`cust_cnt` BIGINT COMMENT '曝光UV，统计在对应模块的曝光用户数量',
	`click_uv` BIGINT COMMENT '商品点击UV，统计在对应模块点击商品的用户数量',
	`order_cnt` BIGINT COMMENT '订单数量（宽口径统计），如下单前15分钟内在对应模块有点击行为，则纳入统计'
)
COMMENT '采购助手转化漏斗分析表，用于追踪用户在采购助手各模块的转化行为路径和效果'
PARTITIONED BY (
	`ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的分区日期'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = '采购助手转化漏斗分析表，记录各实验分组在不同功能模块的用户曝光、点击和转化数据',
	'columnar.nested.type' = 'true'
)
LIFECYCLE 30;