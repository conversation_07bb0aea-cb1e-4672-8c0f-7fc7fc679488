CREATE TABLE IF NOT EXISTS app_dlv_entrust_sku_amount_di(
  `batch_id` BIGINT COMMENT '调度单号，唯一标识一个运输调度任务',
  `use_type` STRING COMMENT '用车类型：0-干线用车、1-调拨用车、2-采购用车、3-大客户用车',
  `amount` DECIMAL(38,18) COMMENT '总费用金额，单位为元',
  `calculate_cost_name` STRING COMMENT '费用名称，如"干线运费"',
  `plan_total_distance` DECIMAL(38,18) COMMENT '调度单计划里程，单位为公里',
  `dist_order_id` BIGINT COMMENT '委托单号，唯一标识一个运输委托单',
  `entrust_weight` DECIMAL(38,18) COMMENT '委托总重量，单位为千克(kg)',
  `entrust_weight_fee` DECIMAL(38,18) COMMENT '委托单按重量分摊到的金额，单位为元',
  `weigt_per_fee` DECIMAL(38,18) COMMENT '单公斤费用，单位为元/公斤',
  `dist_order_fee` DECIMAL(38,18) COMMENT '委托单分摊总金额，单位为元',
  `entrust_quantity` BIGINT COMMENT '委托总数量，单位为件',
  `dist_quantity_fee` DECIMAL(38,18) COMMENT '单件分摊费用，单位为元/件',
  `weigt_distance_fee` DECIMAL(38,18) COMMENT '委托单吨公里分摊费用，单位为元',
  `weight_distance` DECIMAL(38,18) COMMENT '委托单吨公里数，单位为吨公里',
  `weigt_kilometer_fee` DECIMAL(38,18) COMMENT '吨公里分摊费率，单位为元/吨公里',
  `entrust_distance` DECIMAL(38,18) COMMENT '委托实际公里数，单位为公里',
  `begin_site_id` BIGINT COMMENT '起始点位ID',
  `begin_site_name` STRING COMMENT '起始点位名称，如"嘉兴总仓"',
  `end_site_id` BIGINT COMMENT '目的点位ID',
  `end_site_name` STRING COMMENT '目的点位名称，如"苏州仓"',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位标识',
  `sku_disc` STRING COMMENT '商品描述，包含规格、重量等信息',
  `spu_name` STRING COMMENT '商品名称，标准产品单元名称',
  `sku_type` BIGINT COMMENT '商品类型：0-自营、1-代仓、2-代售',
  `sub_type` BIGINT COMMENT '商品二级性质：1-自营-代销不入仓、2-自营-代销入仓、3-自营-经销、4-代仓-代仓',
  `sku_cnt` BIGINT COMMENT 'SKU合计件数，单位为件',
  `sku_weight` DECIMAL(38,18) COMMENT 'SKU合计重量，单位为千克(kg)',
  `sku_volume` DECIMAL(38,18) COMMENT 'SKU合计体积，单位为立方米',
  `sku_weight_distance` DECIMAL(38,18) COMMENT 'SKU合计吨公里数，单位为吨公里',
  `sku_fee_by_unit` DECIMAL(38,18) COMMENT 'SKU费用-按件数分摊，单位为元',
  `sku_fee_by_weiht_km` DECIMAL(38,18) COMMENT 'SKU费用-按吨公里分摊，单位为元',
  `sku_fee_by_weiht_per` DECIMAL(38,18) COMMENT 'SKU费用-按重量分摊，单位为元'
) 
COMMENT '品类干线分摊表，记录各品类商品在干线运输中的费用分摊明细'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '干线运输费用分摊明细表，包含调度单、委托单、SKU级别的费用分摊计算',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;