CREATE TABLE IF NOT EXISTS app_saas_purchases_outbound_detail_aggregate_offline_df(
    outbound_time DATETIME COMMENT '出库时间，格式：年月日时分秒',
    batch_no STRING COMMENT '采购批次编号，唯一标识一次采购批次',
    refund_batch_no STRING COMMENT '退款单编号，唯一标识一次退款操作',
    outbound_stock BIGINT COMMENT '出库数量，单位：件',
    outbound_price DECIMAL(38,18) COMMENT '出库金额，单位：元，保留18位小数',
    purchases_stock BIGINT COMMENT '采购数量，单位：件',
    purchases_price DECIMAL(38,18) COMMENT '采购金额，单位：元，保留18位小数',
    sku_no STRING COMMENT 'SKU编号，商品唯一标识',
    sku_name STRING COMMENT 'SKU名称，商品名称',
    specification STRING COMMENT '商品规格描述',
    packaging STRING COMMENT '包装单位，取值范围：包、箱、件、盒、瓶、袋、条、卷',
    saas_sku_no STRING COMMENT 'SaaS系统SKU编号，SaaS系统中的商品唯一标识',
    saas_sku_name STRING COMMENT 'SaaS系统SKU名称，SaaS系统中的商品名称',
    saas_specification STRING COMMENT 'SaaS系统商品规格描述',
    saas_packaging STRING COMMENT 'SaaS系统包装单位，取值范围：包、箱、件、盒、瓶、袋、条、卷',
    outbound_create_user_id BIGINT COMMENT '出库发起人ID，系统用户唯一标识',
    outbound_create_user_name STRING COMMENT '出库发起人姓名',
    outbound_create_user_phone STRING COMMENT '出库发起人电话号码',
    inbound_create_user_id BIGINT COMMENT '采购人ID，系统用户唯一标识',
    inbound_create_user_name STRING COMMENT '采购人姓名',
    inbound_create_user_phone STRING COMMENT '采购人电话号码',
    warehouse_id BIGINT COMMENT '仓库ID，库存仓唯一标识',
    warehouse_name STRING COMMENT '仓库名称',
    tenant_id BIGINT COMMENT '租户ID，系统租户唯一标识',
    tenant_name STRING COMMENT '租户名称',
    supplier_id BIGINT COMMENT '供应商ID，供应商唯一标识',
    supplier_name STRING COMMENT '供应商名称',
    supplier_type BIGINT COMMENT '供应商类型，取值范围：0-总仓供应商，1-个人供应商，2-其他类型供应商'
)
COMMENT '采购退货出库单离线聚合表，包含采购退货出库的详细信息，包括商品信息、数量金额、操作人员、仓库和供应商等维度数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='采购退货出库明细聚合离线表，用于存储采购退货出库业务的详细数据')
LIFECYCLE 30;