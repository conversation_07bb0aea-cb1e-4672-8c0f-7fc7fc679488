CREATE TABLE IF NOT EXISTS app_saas_product_movement_day_di(
	tenant_id BIGINT COMMENT '租户ID',
	time_tag STRING COMMENT '时间标签，格式为年月日yyyyMMdd',
	on_sale_num BIGINT COMMENT '在售商品数量',
	pay_success_num BIGINT COMMENT '支付成功商品数量',
	sale_rate DECIMAL(38,18) COMMENT '动销率，计算公式：支付成功商品数/在售商品数',
	warehouse_type BIGINT COMMENT '归属类型：0-自营品，1-三方品',
	delivery_type BIGINT COMMENT '配送方式：0-品牌方配送，1-三方配送',
	goods_type BIGINT COMMENT '商品类型：0-无货商品，1-报价货品，2-自营货品，3-其他类型（根据数据发现存在值3）'
) 
COMMENT 'SaaS商品维度动销表(日维度)，统计每日商品销售情况，包括在售商品数、支付成功商品数和动销率等指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为年月日yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS商品维度动销日统计表') 
LIFECYCLE 30;