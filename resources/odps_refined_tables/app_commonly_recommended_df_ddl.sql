CREATE TABLE IF NOT EXISTS app_commonly_recommended_df(
    cust_id BIGINT COMMENT '客户ID，唯一标识一个客户，取值范围：3-20158',
    sku_id STRING COMMENT '商品SKU，商品唯一标识，包含字母数字组合，如N001S01R005、607448530773等',
    score DECIMAL(38,18) COMMENT '商品排序分值，用于推荐排序，取值范围：0.9-8.1',
    purchases BIGINT COMMENT '一年内购买次数，统计客户对该商品的购买频次，取值范围：0-286'
)
COMMENT '常用推荐离线商品表，存储基于用户行为计算的商品推荐分数和购买频次数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
     'comment'='常用推荐离线商品表，用于存储商品推荐相关的离线计算数据') 
LIFECYCLE 100;