```sql
CREATE TABLE IF NOT EXISTS app_stock_dashboard_history_df(
    view_date DATETIME COMMENT '视图日期，格式为年月日时分秒，表示数据统计的具体时间点',
    sku_id STRING COMMENT 'SKU编号，商品库存单位的唯一标识，取值范围包括1000432818285、1001450537647等数千个唯一值',
    pd_id BIGINT COMMENT '商品编号，数值型标识，取值范围3-18973',
    warehouse_no BIGINT COMMENT '仓库编号，数值型标识，取值范围2-176',
    sales_quantity BIGINT COMMENT '销量出库数量，当日实际销售出库的商品数量，取值范围0-1002',
    transfer_out_quantity BIGINT COMMENT '调拨出库量，当日因调拨而出库的商品数量，取值范围0-68',
    consumption BIGINT COMMENT '消耗量，当日商品的实际消耗数量，取值范围0-1002',
    enabled_quantity BIGINT COMMENT '可用库存，当日可用的商品库存数量，取值范围0-1090130',
    on_way_quantity BIGINT COMMENT '采购在途库存，当日采购中但尚未到货的商品数量，当前数据中均为0',
    allocate_in_quantity BIGINT COMMENT '调拨在途库存，当日调拨中但尚未到货的商品数量，当前数据中均为0',
    init_quantity BIGINT COMMENT '期初库存，当日开始时的商品库存数量，取值范围0-1090130',
    terminal_enabled_quantity BIGINT COMMENT '期末可用库存，当日结束时的可用商品库存数量，取值范围0-1090130',
    terminal_way_quantity BIGINT COMMENT '期末采购在途库存，当日结束时的采购在途商品数量，取值范围0-2730',
    terminal_allocate_in_quantity BIGINT COMMENT '期末调拨在途库存，当日结束时的调拨在途商品数量，取值范围0-60',
    on_way_order_quantity BIGINT COMMENT '采购订单在途数量，采购订单中但尚未完成的商品数量，当前数据中均为0',
    terminal_order_quantity BIGINT COMMENT '期末采购订单在途数量，当日结束时的采购订单在途商品数量，取值范围0-200',
    order_sale_cnt BIGINT COMMENT '订单销量，基于订单统计的销售数量，取值范围0-408',
    timing_delivery_out_quantity BIGINT COMMENT '省心送计划出库量，省心送服务的计划出库数量，取值范围0-75'
)
COMMENT '罗盘历史数据表，存储商品库存、销售、调拨等历史统计信息，用于库存管理和销售分析'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='商品库存仪表盘历史数据表，包含详细的库存变动、销售出库、调拨等业务指标')
LIFECYCLE 30;
```