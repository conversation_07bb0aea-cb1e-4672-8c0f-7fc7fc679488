CREATE TABLE IF NOT EXISTS app_merchant_pool_data_df(
	pool_info_id BIGINT COMMENT '圈人2.0主键ID，唯一标识每条记录',
	m_id BIGINT COMMENT '商家ID，唯一标识商家',
	area_no BIGINT COMMENT '门店运营服务区域编号',
	size STRING COMMENT '店铺类型，取值范围：单店、大客户',
	day_tag STRING COMMENT '日期标签，格式为YYYYMMDD，表示年月日'
)
COMMENT '圈人2.0商家数据表，存储商家基础信息和标签数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为YYYYMMDD，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='圈人2.0商家数据表，包含商家基础信息、区域信息和店铺类型等数据')
LIFECYCLE 30;