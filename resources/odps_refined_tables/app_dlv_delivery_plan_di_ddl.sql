```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_delivery_plan_di`(
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `cust_type` STRING COMMENT '客户类型，取值范围：ALL-全部客户，SAAS客户自营-SaaS客户自营，大客户-大客户，平台客户-平台客户，批发客户-批发客户',
  `sku_type` STRING COMMENT '商品类型，取值范围：ALL-全部商品，代仓-代仓商品，全品类-全品类商品，自营-自营商品，SAAS客户自营-SaaS客户自营商品',
  `category1` STRING COMMENT '一级分类，取值范围：ALL-全部分类，乳制品-乳制品分类，其他-其他分类，鲜果-鲜果分类，SAAS客户自营-SaaS客户自营分类',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV，原始订单总金额',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后GMV，售后未收货金额',
  `pre_origin_total_amt` DECIMAL(38,18) COMMENT '计划履约次日订单应付GMV，次日订单原始金额',
  `pre_real_total_amt` DECIMAL(38,18) COMMENT '计划履约次日订单实付GMV，次日订单实付金额',
  `today_origin_total_amt` DECIMAL(38,18) COMMENT '计划履约当日订单应付GMV，当日订单原始金额',
  `history_origin_total_amt` DECIMAL(38,18) COMMENT '计划履约历史订单实付GMV，历史订单实付金额',
  `cust_cnt` BIGINT COMMENT '计划履约客户数，客户数量统计',
  `cost_amt` DECIMAL(38,18) COMMENT '计划履约成本，成本金额',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，履约原始金额',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，履约实付金额',
  `dlv_cost_amt` DECIMAL(38,18) COMMENT '计划履约成本，履约成本金额',
  `dlv_cust_cnt` BIGINT COMMENT '计划履约客户数，履约客户数量统计'
) 
COMMENT '计划履约数据表，记录各类客户和商品的计划履约相关指标数据，包括GMV、成本、客户数等维度统计'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
  'comment'='计划履约数据分析表，用于跟踪和分析履约计划的执行情况') 
LIFECYCLE 30;
```