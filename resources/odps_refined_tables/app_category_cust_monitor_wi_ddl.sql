CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_category_cust_monitor_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，一年中的第几周',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `cust_type` STRING COMMENT '客户类型，取值范围：ALL/平台客户/大客户/批发客户',
  `category1` STRING COMMENT '一级类目',
  `category2` STRING COMMENT '二级类目',
  `category3` STRING COMMENT '三级类目',
  `category4` STRING COMMENT '四级类目',
  `pv` BIGINT COMMENT '曝光PV（页面浏览量）',
  `click_pv` BIGINT COMMENT '点击PV（点击量）',
  `add_pv` BIGINT COMMENT '加购PV（加入购物车量）',
  `buy_pv` BIGINT COMMENT '直接购买PV（直接购买量）',
  `pv_value` DECIMAL(38,18) COMMENT '千次曝光价值',
  `order_cnt` BIGINT COMMENT '订单数',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV（商品交易总额）',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV（实际支付金额）',
  `sku_cnt` BIGINT COMMENT '动销sku数（有销售的商品数）',
  `on_sale_sku_cnt` BIGINT COMMENT '在架sku数（上架商品数）',
  `sale_sku_rate` DECIMAL(38,18) COMMENT 'sku动销率（动销sku数/在架sku数）',
  `spu_cnt` BIGINT COMMENT '动销spu数（有销售的商品品类数）',
  `on_sale_spu_cnt` BIGINT COMMENT '在架spu数（上架商品品类数）',
  `sale_spu_rate` DECIMAL(38,18) COMMENT 'spu动销率（动销spu数/在架spu数）',
  `order_sku_cnt` BIGINT COMMENT '销量（销售商品件数）',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易商品重量（kg）',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV（履约环节商品交易总额）',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV（履约环节实际支付金额）',
  `cost_amt` DECIMAL(38,18) COMMENT '成本费用',
  `dlv_market_amt` DECIMAL(38,18) COMMENT '营销费用',
  `dlv_market_roi` DECIMAL(38,18) COMMENT '营销费用ROI（投资回报率）',
  `gross_profit_amt` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_profit_rate` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_profit_rate` DECIMAL(38,18) COMMENT '实付毛利率',
  `dlv_sku_cnt` DECIMAL(38,18) COMMENT '履约数量（履约商品件数）',
  `dlv_timing_origin_amt` DECIMAL(38,18) COMMENT '履约省心送应付GMV',
  `dlv_pet_origin_amt` DECIMAL(38,18) COMMENT '履约单件应付GMV',
  `dlv_pet_real_amt` DECIMAL(38,18) COMMENT '履约单件实付GMV',
  `dlv_pet_cost_amt` DECIMAL(38,18) COMMENT '履约单件成本',
  `dlv_pet_profit_amt` DECIMAL(38,18) COMMENT '履约单件毛利润',
  `dlv_sku_weight` DECIMAL(38,18) COMMENT '履约重量（kg）',
  `dlv_pet_weight_origin_amt` DECIMAL(38,18) COMMENT '履约单kg应付GMV',
  `dlv_pet_weight_real_amt` DECIMAL(38,18) COMMENT '履约单kg实付GMV',
  `dlv_pet_weight_cost_amt` DECIMAL(38,18) COMMENT '履约单kg成本',
  `dlv_pet_weight_profit_amt` DECIMAL(38,18) COMMENT '履约单kg毛利润',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后金额',
  `after_sale_noreceived_proportion` DECIMAL(38,18) COMMENT '未到货售后金额占比',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后金额',
  `after_sale_received_proportion` DECIMAL(38,18) COMMENT '已到货售后金额占比',
  `store_quantity` BIGINT COMMENT '可用库存数量',
  `quality_amt_4_3` DECIMAL(38,18) COMMENT '3/4效期库存金额',
  `quality_amt_2_1` DECIMAL(38,18) COMMENT '1/2效期库存金额',
  `quality_amt_4_1` DECIMAL(38,18) COMMENT '1/4效期库存金额',
  `quality_amt_under_4_1` DECIMAL(38,18) COMMENT '1/4效期以下库存金额',
  `advent_amt` DECIMAL(38,18) COMMENT '临期库存金额',
  `turnover` DECIMAL(38,18) COMMENT '近三十天周转率',
  `purchase_in_cnt` BIGINT COMMENT '采购入库数量',
  `purchase_in_amt` DECIMAL(38,18) COMMENT '采购入库金额',
  `damage_in_cnt` BIGINT COMMENT '货损出库数量',
  `damage_in_amt` DECIMAL(38,18) COMMENT '货损出库金额',
  `dlv_origin_growth_amount` DECIMAL(38,18) COMMENT '履约应付增长额',
  `dlv_origin_growth_rate` DECIMAL(38,18) COMMENT '履约应付增长率',
  `dlv_origin_profit_growth_amount` DECIMAL(38,18) COMMENT '履约毛利润增长额',
  `dlv_real_profit_growth_rate` DECIMAL(38,18) COMMENT '履约毛利润增长率',
  `dlv_origin_growth_label` STRING COMMENT 'GMC增速标签，取值范围：高降幅/稳定/高增幅',
  `dlv_ofit_growth_label` STRING COMMENT '毛利润增速标签，取值范围：高降幅/稳定/高增幅',
  `dlv_origin_amt_label` BIGINT COMMENT '履约应付GMV类目排名（四级）',
  `dlv_total_last_origin_rate` DECIMAL(38,18) COMMENT '履约应付GMV占比（四级）上级占比'
) 
COMMENT '类目监控周表，按周统计各类目客户维度的业务指标，包括曝光、点击、交易、库存、履约等核心业务数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：YYYYMMDD，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='类目监控周表，用于监控和分析各类目客户维度的业务表现和趋势')
LIFECYCLE 30;