CREATE TABLE IF NOT EXISTS app_expand_activity_products_df(
    cust_id BIGINT COMMENT '客户ID，唯一标识一个客户',
    area_no BIGINT COMMENT '运营服务区编号，标识客户所属的服务区域',
    sku_id STRING COMMENT '商品SKU编码，唯一标识一个商品',
    type BIGINT COMMENT '推荐商品类型：0-流失风险商品，1-召回商品，2-拉新商品',
    effective_time DATETIME COMMENT '数据有效日期，格式为年月日时分秒'
)
COMMENT '拓展购买商品数据表，包含客户推荐商品的拓展购买数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='拓展购买商品数据表，用于存储客户商品推荐和拓展购买相关信息')
LIFECYCLE 100;