CREATE TABLE IF NOT EXISTS app_saas_brand_log_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`brand_alias` STRING COMMENT '品牌名称，枚举值包括：GIGI LUCKY舒芙蕾、Keke可可同学订货商城、VQ、一只酸奶牛、东桃蛋糕（南客甄选）、乳果说茶饮、五二兰柠檬茶、咕鹿流心披萨、娘惹囡、屋里咖啡、山山不夜、川町太郎、新加坡斯味洛鲜奶茶、日尝、曾小白、有堂古、柠季、椿风、榴莲嘟嘟、瑞杰斯、真茶屋、肯豆、艾炒酸奶、菟竹集、蔡小甜、裕蘭茶楼、谷人说订货小站、赵记鲜果、遇见村上订货、邓氏阿嬷等',
	`login_pv` BIGINT COMMENT '总登录页面浏览量，取值范围：3-385',
	`login_uv` BIGINT COMMENT '总登录独立访客数，取值范围：1-9',
	`mall_pv` BIGINT COMMENT '商场模块页面浏览量，取值范围：0-16',
	`mall_uv` BIGINT COMMENT '商场模块独立访客数，取值范围：0-2',
	`customer_pv` BIGINT COMMENT '门店模块页面浏览量，取值范围：0-71',
	`customer_uv` BIGINT COMMENT '门店模块独立访客数，取值范围：0-4',
	`goods_pv` BIGINT COMMENT '商品模块页面浏览量，取值范围：0-127',
	`goods_uv` BIGINT COMMENT '商品模块独立访客数，取值范围：0-4',
	`order_pv` BIGINT COMMENT '订单模块页面浏览量，取值范围：0-94',
	`order_uv` BIGINT COMMENT '订单模块独立访客数，取值范围：0-9',
	`purchasing_pv` BIGINT COMMENT '采购模块页面浏览量，取值范围：0-88',
	`purchasing_uv` BIGINT COMMENT '采购模块独立访客数，取值范围：0-2',
	`store_pv` BIGINT COMMENT '仓库模块页面浏览量，取值范围：0-59',
	`store_uv` BIGINT COMMENT '仓库模块独立访客数，取值范围：0-5',
	`data_pv` BIGINT COMMENT '数据模块页面浏览量，取值范围：0-39',
	`data_uv` BIGINT COMMENT '数据模块独立访客数，取值范围：0-5',
	`finance_pv` BIGINT COMMENT '财务模块页面浏览量，取值范围：0-27',
	`finance_uv` BIGINT COMMENT '财务模块独立访客数，取值范围：0-2'
) 
COMMENT 'SaaS品牌埋点数据表，记录各品牌在各功能模块的页面访问统计指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='SaaS品牌埋点数据表，包含各品牌在各功能模块的页面浏览量和独立访客数统计') 
LIFECYCLE 30;