CREATE TABLE IF NOT EXISTS app_saas_brand_sku_delivery_mi(
	`month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	`brand_alias` STRING COMMENT '品牌名称，取值范围包括：GIGI LUCKY舒芙蕾、Keke可可同学订货商城、VQ、一只酸奶牛、乳果说茶饮、八街手作（口口椰）、咕鹿流心披萨、娘惹囡、屋里咖啡、山山不夜、川町太郎、御贵人、怡满分杭州saas、新加坡斯味洛鲜奶茶、日尝、有堂古、柠季、桃花屋小酒馆SaaS、椿风、榴莲嘟嘟、爆珠公·老红糖珍珠鲜奶茶、益禾堂、肯豆、艾炒酸奶、菟竹集、蔡小甜、裕蘭茶楼、谷人说订货小站、赵记鲜果、遇见村上订货、银座仁志川订货系统等31个品牌',
	`sku_id` STRING COMMENT '商品SKU，商品唯一标识编码',
	`title` STRING COMMENT '商品标题，商品名称描述',
	`specification` STRING COMMENT '商品规格，商品包装规格和等级信息',
	`category1` STRING COMMENT '后台一级类目，取值范围包括：乳制品、水果制品、新鲜水果、食用油丨油脂及制品、糖丨糖制品、新鲜蔬菜、饮料、糕点丨面包、蔬菜制品、饮品原料、调味品、谷物制品、成品原料、茶制品、饼干丨糖果丨可可豆制品、坚果制品、食品添加剂、包材等18个类目',
	`delivery_gmv` DECIMAL(38,18) COMMENT '履约GMV，商品履约交易金额，保留18位小数精度',
	`cost_amt` DECIMAL(38,18) COMMENT '商品成本，商品成本金额，保留18位小数精度'
) 
COMMENT 'saas利润数据表现表（仅为鲜沐自营数据），包含品牌商品维度的履约GMV和成本数据，用于利润分析'
PARTITIONED BY (
	`ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，如20250922表示2025年9月22日'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment'='saas利润数据表现表（仅为鲜沐自营数据），包含品牌商品维度的履约GMV和成本数据，用于利润分析',
	'last_data_modified_time'='2025-09-23 02:46:27'
)
LIFECYCLE 30;