CREATE TABLE IF NOT EXISTS app_after_sale_delivery_sku_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`sku_id` STRING COMMENT '商品SKU ID',
	`spu_id` BIGINT COMMENT '商品SPU ID',
	`spu_name` STRING COMMENT '商品名称',
	`sku_disc` STRING COMMENT '商品规格描述',
	`sku_type` STRING COMMENT '商品类型：自营-自营商品，代仓-代仓商品，代售-代售商品',
	`after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额（元）',
	`delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付金额（元）',
	`delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付金额（元）',
	`coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额（元）'
)
COMMENT 'SKU粒度配送售后GMV统计表，记录每个SKU的配送售后相关金额指标'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = 'SKU粒度配送售后GMV统计表，包含商品基础信息和售后配送相关金额指标',
	'columnar.nested.type' = 'true'
)
LIFECYCLE 30;