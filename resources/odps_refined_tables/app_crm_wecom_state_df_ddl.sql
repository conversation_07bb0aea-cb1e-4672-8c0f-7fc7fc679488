CREATE TABLE IF NOT EXISTS app_crm_wecom_state_df(
	bd_id BIGINT COMMENT '销售ID，唯一标识一个销售人员',
	bd_name STRING COMMENT '销售人员的姓名',
	parent_id BIGINT COMMENT '上级销售人员的ID',
	parent_name STRING COMMENT '上级销售人员的姓名',
	job_state BIGINT COMMENT '在职状态：0-离职，1-在职',
	wecom_state BIGINT COMMENT '企微状态：1-已激活，2-已禁用，4-未激活，5-退出企业'
)
COMMENT '销售在职状态和企微激活状态表，记录销售人员的在职情况和企微账号状态'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='销售在职状态和企微激活状态表，用于跟踪销售团队的人员状态和企微账号使用情况') 
LIFECYCLE 30;