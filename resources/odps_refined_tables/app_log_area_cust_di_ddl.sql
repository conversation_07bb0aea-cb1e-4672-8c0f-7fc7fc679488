CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_area_cust_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd',
  `large_area_no` STRING COMMENT '运营大区编号，取值范围：1,2,5,14,24,29,38,50,70,71,72,74,75,82,84,89,91,93,未知',
  `large_area_name` STRING COMMENT '运营大区名称，取值范围：上海大区、昆明快递大区、重庆大区、长沙大区、南宁大区、苏州大区、贵阳大区、苏南大区、成都大区、福州大区、青岛大区、可可快递服务区、武汉大区、未知、杭州大区、广州大区、昆明大区、柠季快递大区、广东一点点快递区域',
  `cust_type` STRING COMMENT '客户业态类型，取值范围：咖啡、烘焙、茶饮、其他',
  `uv` BIGINT COMMENT '登录用户数(UV)',
  `pv` BIGINT COMMENT '登录页面浏览量(PV)',
  `exposure_sku_cnt` BIGINT COMMENT '曝光商品SKU数量',
  `view_time` DECIMAL(38,18) COMMENT '浏览时长，单位：分钟',
  `order_cust_cnt` BIGINT COMMENT '下单客户数量',
  `translate_rate` DECIMAL(38,18) COMMENT '转化率（下单客户数/登录用户数）',
  `unlogin_uv` BIGINT COMMENT '未登录用户数(UV)',
  `unlogin_pv` BIGINT COMMENT '未登录页面浏览量(PV)',
  `cust_flag` STRING COMMENT '是否老客标识，取值范围：是、否',
  `home_page_pv` BIGINT COMMENT '首页登录页面浏览量(PV)',
  `order_cnt` BIGINT COMMENT '下单次数',
  `total_exposure_pv` BIGINT COMMENT '总曝光页面浏览量(PV)',
  `total_exposure_uv` BIGINT COMMENT '总曝光用户数(UV)',
  `total_click_pv` BIGINT COMMENT '总点击页面浏览量(PV)',
  `total_click_uv` BIGINT COMMENT '总点击用户数(UV)',
  `sku_exposure_pv` BIGINT COMMENT '商品曝光页面浏览量(PV)',
  `sku_exposure_uv` BIGINT COMMENT '商品曝光用户数(UV)',
  `sku_click_pv` BIGINT COMMENT '商品点击页面浏览量(PV)',
  `sku_click_uv` BIGINT COMMENT '商品点击用户数(UV)',
  `sku_cart_buy_pv` BIGINT COMMENT '商品加购页面浏览量(PV)',
  `sku_cart_buy_uv` BIGINT COMMENT '商品加购用户数(UV)'
)
COMMENT '日志整体监控表，按运营大区和客户业态维度统计用户行为指标，包括登录、浏览、曝光、点击、加购、下单等核心数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '运营大区客户行为日志监控表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;