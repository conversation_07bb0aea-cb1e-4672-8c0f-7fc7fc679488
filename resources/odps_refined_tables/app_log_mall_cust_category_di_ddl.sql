CREATE TABLE IF NOT EXISTS app_log_mall_cust_category_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `cust_type` STRING COMMENT '客户业态，取值范围：其他、咖啡、烘焙、茶饮',
    `category_type` STRING COMMENT '品类，取值范围：NB-A&T淡奶油、PB-标品、乳制品、其他、鲜果',
    `sku_exposure_pv` BIGINT COMMENT '商品曝光PV，统计周期内商品被曝光的次数',
    `sku_exposure_uv` BIGINT COMMENT '商品曝光UV，统计周期内曝光商品的独立用户数',
    `sku_click_pv` BIGINT COMMENT '商品点击PV，统计周期内商品被点击的次数',
    `sku_click_uv` BIGINT COMMENT '商品点击UV，统计周期内点击商品的独立用户数',
    `sku_add_to_cart_pv` BIGINT COMMENT '商品加购PV，统计周期内商品被加入购物车的次数',
    `sku_add_to_cart_uv` BIGINT COMMENT '商品加购UV，统计周期内加购商品的独立用户数',
    `buy_cust_cnt` BIGINT COMMENT '购买人数，统计周期内购买该商品的独立用户数'
)
COMMENT '品类转化-商品维度数据表，记录各品类商品在客户业态维度的转化漏斗数据，包括曝光、点击、加购、购买等关键指标'
PARTITIONED BY (
    `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的业务日期'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='品类转化-商品维度数据表，用于分析商品在各客户业态下的转化效果')
LIFECYCLE 30;