```sql
CREATE TABLE IF NOT EXISTS app_cust_order_sku_di(
    `date` STRING COMMENT '业务日期，格式为yyyyMMdd，表示数据统计的日期',
    `register_province` STRING COMMENT '客户注册省份，枚举值包括：江苏、上海、四川、浙江、广东、福建、湖北、江西、湖南、安徽、重庆、山东、贵州、广西壮族自治区、云南等',
    `register_city` STRING COMMENT '客户注册城市，枚举值包括：盐城市、上海市、成都市、宁波市、肇庆市、南通市、厦门市、泰州市、武汉市、杭州市等全国各城市',
    `register_area` STRING COMMENT '客户注册区域/县区，枚举值包括：亭湖区、杨浦区、金牛区、鄞州区、成华区、端州区、海门区、海沧区、高港区、硚口区等全国各区县',
    `cust_class` STRING COMMENT '客户类型，枚举值：普通（非品牌）、大客户（非茶百道）、批发、大客户（茶百道）、普通（品牌）',
    `brand_alias` STRING COMMENT '品牌别名，枚举值：无、七分甜、上上签、淘宝代下单账号、苏米诺手作酸奶、江苏茶百道、爷爷不泡茶等各类品牌',
    `order_type` STRING COMMENT '订单类型，枚举值：其他、省心送',
    `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位唯一标识',
    `spu_name` STRING COMMENT '商品名称（SPU级别）',
    `spu_disc` STRING COMMENT '商品描述，包含规格、重量、等级等信息',
    `category_1` STRING COMMENT '一级类目，枚举值：鲜果、乳制品、其他',
    `category_4` STRING COMMENT '四级类目，枚举值包括：芒果、西瓜、蓝莓、搅打型稀奶油、橙、柠檬、冷冻果肉、桂圆丨龙眼、爆爆珠等140+个商品类目',
    `after_sale_sku_cnt` BIGINT COMMENT '售后SKU数量，统计售后涉及的商品数量',
    `after_sale_cnt` BIGINT COMMENT '售后次数，统计售后发生的次数',
    `after_sale_amt` DECIMAL(38,18) COMMENT '售后金额，精确到18位小数的金额',
    `real_total_amt` DECIMAL(38,18) COMMENT '配送实付金额，精确到18位小数的实际支付金额',
    `conpon_amt` DECIMAL(38,18) COMMENT '优惠券金额，精确到18位小数的优惠券抵扣金额'
)
COMMENT '客户+SKU售后明细表（含未到货），用于分析客户维度的商品售后情况'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='客户商品售后明细分析表，包含客户注册信息、商品信息、售后指标和财务数据',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```