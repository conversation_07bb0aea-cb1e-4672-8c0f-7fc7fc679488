CREATE TABLE IF NOT EXISTS app_pcs_direct_category_warehouse_purchase_kpi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
	`warehouse_no` BIGINT COMMENT '库存仓号，取值范围：2-155',
	`warehouse_name` STRING COMMENT '库存仓名称，枚举值包括：南京总仓、嘉兴水果批发总仓、上海总仓、东莞总仓、武汉总仓、长沙总仓、苏州总仓、南宁总仓、美团优选上海仓、华西总仓、嘉兴总仓、福州总仓、贵阳总仓、青岛总仓、美团优选杭州一仓、美团优选杭州二仓、美团虚拟代下单总仓、济南总仓、昆明总仓等',
	`category4` STRING COMMENT '四级类目，枚举值包括：芭乐、其他蜜瓜、草莓、柠檬、金枕榴莲、柚、牛油果、进口凤梨、葡萄、芒果、根茎类、软籽石榴、山竹、金桔、椰子、混合水果、提子、台农芒果、桃子、其他李子、雪莲果、黄金蜜瓜、石榴、红富士、三红柚、茉莉香葡萄、梨、其他橙、冬枣、千禧番茄、甘蓝、椰青、橘子、25号蜜瓜、国产蓝莓、半边红李、人参果、橙、西瓜、柑、无籽麒麟西瓜、奇异果丨猕猴桃、西州蜜瓜、进口白心、百香果、恐龙蛋李、夏橙、其他芒果、火龙果、水蜜桃等90+个水果品类',
	`direct_purchase_amt` DECIMAL(38,18) COMMENT '直采金额，单位：元',
	`purchases_amt` DECIMAL(38,18) COMMENT '采购金额（非直采），单位：元',
	`cost_flow_amt` DECIMAL(38,18) COMMENT '成本浮动金额，单位：元',
	`direct_delivery_origin_amt` DECIMAL(38,18) COMMENT '直采履约应付金额，单位：元',
	`direct_delivery_real_amt` DECIMAL(38,18) COMMENT '直采履约实付金额，单位：元',
	`direct_delivery_market_amt` DECIMAL(38,18) COMMENT '直采营销费用，单位：元',
	`direct_delivery_cost_amt` DECIMAL(38,18) COMMENT '直采成本费用，单位：元',
	`direct_init_amt` DECIMAL(38,18) COMMENT '直采期末库存金额，单位：元',
	`direct_after_sale_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责售后金额，单位：元',
	`direct_damage_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责货损金额，单位：元'
) 
COMMENT '直采KPI指标表，按日期、仓库、四级类目维度统计直采相关的采购金额、履约金额、成本费用、库存金额等关键绩效指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='直采业务KPI监控表，用于分析直采业务的采购、履约、成本和库存等核心指标') 
LIFECYCLE 30;