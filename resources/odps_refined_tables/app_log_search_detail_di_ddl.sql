CREATE TABLE IF NOT EXISTS app_log_search_detail_di(
  `time` DATETIME COMMENT '事件发生时间，年月日时分秒格式',
  `query` STRING COMMENT '用户搜索的关键词',
  `url` STRING COMMENT '页面URL地址',
  `envent_type` STRING COMMENT '事件类型枚举：click-点击，impression-曝光，add_to_cart-加购，buy_now-立即购买，purchase-提交订单，checkout-发起支付，paid-支付完成',
  `idx` STRING COMMENT '商品在搜索结果列表中的位置下标',
  `sku_id` STRING COMMENT '商品SKU唯一标识',
  `spu_id` BIGINT COMMENT '商品SPU唯一标识（同pd_id）',
  `sku_price` STRING COMMENT '商品SKU价格，单位：元',
  `master_order_no` STRING COMMENT '主订单编号',
  `cid` STRING COMMENT '客户端设备唯一标识',
  `sid` STRING COMMENT '单次打开商城的会话唯一ID',
  `count` BIGINT COMMENT '单次打开页面的埋点事件计数',
  `user_agent` STRING COMMENT '用户设备信息（浏览器/操作系统等）',
  `cust_id` BIGINT COMMENT '客户唯一ID',
  `cust_name` STRING COMMENT '客户名称',
  `cust_type` STRING COMMENT '客户类型枚举：烘焙、茶饮、咖啡、面包蛋糕、甜品冰淇淋、西餐、其他',
  `default_address` STRING COMMENT '店铺默认地址信息（省市区详细地址）',
  `is_new` STRING COMMENT '是否当日注册枚举：是-当日注册，否-非当日注册'
)
COMMENT '商城搜索词流量分析明细表，记录用户搜索行为、商品曝光点击、加购购买等全链路转化事件'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，yyyyMMdd格式日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='商城搜索词流量分析明细表，用于分析搜索关键词的流量分布和转化效果',
  'columnar.nested.type'='true'
)
LIFECYCLE 30;