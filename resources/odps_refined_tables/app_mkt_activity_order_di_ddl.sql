```sql
CREATE TABLE IF NOT EXISTS app_mkt_activity_order_di(
    `date` STRING COMMENT '统计日期，格式为yyyyMMdd',
    `activity_id` BIGINT COMMENT '活动唯一标识ID',
    `activity_name` STRING COMMENT '活动名称，如：舒可曼糖霜特价、武汉仓鲜果阶梯价活动等',
    `start_time` DATETIME COMMENT '活动开始时间，格式为年月日时分秒',
    `end_time` DATETIME COMMENT '活动结束时间，格式为年月日时分秒',
    `activity_type` STRING COMMENT '活动类型：特价活动、临保活动',
    `activity_tag` STRING COMMENT '活动目的标签：滞销促销、临保清仓、潜力品推广、新品推广、用户召回等',
    `city_id` BIGINT COMMENT '运营服务区唯一标识ID',
    `city_name` STRING COMMENT '运营服务区名称，如：南京、武汉普冷、茶武汉外区等',
    `large_area_id` BIGINT COMMENT '运营服务大区唯一标识ID',
    `large_area_name` STRING COMMENT '运营服务大区名称，如：苏州大区、武汉大区、苏南大区等',
    `sku_id` STRING COMMENT '商品SKU编码，商品最小库存单位标识',
    `spu_id` BIGINT COMMENT '商品SPU ID，标准产品单元标识',
    `spu_name` STRING COMMENT '商品名称，如：舒可曼糖霜、红凯特芒、佳沃椰青等',
    `sku_spec` STRING COMMENT '商品规格描述，如：13.62KG*1桶、净重12-12.5斤等',
    `order_cnt` BIGINT COMMENT '订单数量',
    `cust_cnt` BIGINT COMMENT '客户数量',
    `sku_cnt` BIGINT COMMENT 'SKU销售数量',
    `activity_amt` DECIMAL(38,18) COMMENT '营销费用金额',
    `real_total_amt` DECIMAL(38,18) COMMENT '实付金额，客户实际支付金额',
    `origin_total_amt` DECIMAL(38,18) COMMENT '应付金额，订单原始金额'
)
COMMENT '活动订单统计表，记录各营销活动的订单相关统计指标，包括订单数、客户数、销售金额和营销费用等'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，用于按天分区存储数据'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '活动订单统计表，用于分析营销活动效果和订单表现',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```