CREATE TABLE IF NOT EXISTS app_kpi_whole_trade_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
    `cust_group` STRING COMMENT '客户类型，取值范围：大客户、平台客户、批发客户、SAAS客户自营、ALL（全部客户）',
    `sku_type` STRING COMMENT '商品类型，取值范围：自营、代仓、全品类、SAAS客户自营、ALL（全部商品类型）',
    `category` STRING COMMENT '商品类目，取值范围：鲜果、乳制品、其他、SAAS客户自营、ALL（全部类目）',
    `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，交易应付的总金额',
    `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，交易实际支付的总金额',
    `cust_cnt` BIGINT COMMENT '客户数，交易涉及的客户数量',
    `order_cnt` BIGINT COMMENT '订单数，交易产生的订单数量',
    `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额，省心送服务的应付金额',
    `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额，未到货商品的售后金额'
)
COMMENT '交易KPI金额汇总表，包含各类交易金额指标、客户数和订单数的统计信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='交易KPI金额汇总表，用于分析交易相关的关键绩效指标')
LIFECYCLE 30;