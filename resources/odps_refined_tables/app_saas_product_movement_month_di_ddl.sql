CREATE TABLE IF NOT EXISTS app_saas_product_movement_month_di(
	tenant_id BIGINT COMMENT '租户ID，唯一标识一个SaaS租户',
	time_tag STRING COMMENT '时间标签，格式为yyyyMMdd，表示当月1号（如20250901表示2025年9月1号）',
	on_sale_num BIGINT COMMENT '在售商品数量，统计当月该租户在售的商品总数',
	pay_success_num BIGINT COMMENT '支付成功商品数量，统计当月该租户支付成功的商品数量',
	sale_rate DECIMAL(38,18) COMMENT '动销率，计算公式：支付成功商品数/在售商品数，表示商品销售效率',
	warehouse_type BIGINT COMMENT '仓库归属类型：0-自营品，1-三方品',
	delivery_type BIGINT COMMENT '配送方式：0-品牌方配送，1-三方配送',
	goods_type BIGINT COMMENT '商品类型：0-无货商品，1-报价货品，2-自营货品，3-其他类型（根据数据发现存在值3）'
) 
COMMENT 'SaaS商品维度动销表（月维度），统计各租户商品的月度销售情况，包括在售数量、支付成功数量、动销率等指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期（如20250922表示2025年9月22号）') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS商品维度动销表（月维度），用于分析各租户商品的月度销售表现和动销效率') 
LIFECYCLE 30;