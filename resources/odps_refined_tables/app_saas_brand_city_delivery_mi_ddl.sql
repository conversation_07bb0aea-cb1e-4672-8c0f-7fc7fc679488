CREATE TABLE IF NOT EXISTS app_saas_brand_city_delivery_mi(
	`month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	`brand_alias` STRING COMMENT '品牌名称，枚举值包括：GIGI LUCKY舒芙蕾、Keke可可同学订货商城、VQ、一只酸奶牛、东桃蛋糕（南客甄选）、乳果说茶饮、八街手作（口口椰）、咕鹿流心披萨、娘惹囡、屋里咖啡、山山不夜、川町太郎、御贵人、怡满分杭州saas、新加坡斯味洛鲜奶茶、日尝、曾小白、有堂古、柠季、桃花屋小酒馆SaaS、椿风、榴莲嘟嘟、爆珠公·老红糖珍珠鲜奶茶、瑞杰斯、益禾堂、肯豆、艾炒酸奶、菟竹集、蔡小甜、裕蘭茶楼、谷人说订货小站、赵记鲜果、遇见村上订货、银座仁志川订货系统等',
	`province` STRING COMMENT '省份名称，枚举值包括：广东、江西、湖北、湖南、江苏、浙江、四川、重庆、安徽、上海、福建、山东、贵州、甘肃、广西壮族自治区等',
	`city` STRING COMMENT '城市名称，枚举值包括：东莞市、中山市、佛山市、广州市、惠州市、江门市、深圳市、清远市、珠海市、上饶市、荆门市、岳阳市、南京市、镇江市、南昌市、杭州市、湖州市、乐山市、内江市、宜宾市、德阳市、成都市、眉山市、绵阳市、自贡市、苏州市、宁波市、金华市、宜昌市、长沙市、重庆市、嘉兴市、衢州市、南通市、常州市、无锡市、淮安市、蚌埠市、上海市、扬州市、温州市、泉州市、合肥市、汕头市、武汉市、厦门市、福州市、宣城市、湛江市、阳江市等',
	`area` STRING COMMENT '区县名称，枚举值包括：万江街道、南城街道、东区街道、石岐街道、顺德区、天河区、白云区、惠东县、惠城区、惠阳区、江海区、光明区、南山区、坪山区、宝安区、福田区、罗湖区、清城区、清新区、斗门区、金湾区、香洲区、广丰区、京山市、平江县、江宁区、浦口区、秦淮区、京口区、红谷滩区、上城区、桐庐县、吴兴区、市中区、东兴区、叙州区、翠屏区、广汉市、旌阳区、绵竹市、双流区、彭州市、成华区、新都区、武侯区、温江区、蒲江县、邛崃市、郫都区、都江堰市等',
	`point_cnt` BIGINT COMMENT '点位数，统计范围内的点位数量，取值范围：1-298'
) 
COMMENT 'SaaS履约网络可视化表，展示各品牌在不同地区的点位分布情况'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日，如20250922表示2025年9月22日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS履约网络可视化表，用于分析品牌在全国各地区的点位分布和履约网络覆盖情况') 
LIFECYCLE 30;