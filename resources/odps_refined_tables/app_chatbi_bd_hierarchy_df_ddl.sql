CREATE TABLE IF NOT EXISTS app_chatbi_bd_hierarchy_df(
	bd_id STRING COMMENT 'BD ID，员工唯一标识，示例值：11963、17781、600162等',
	bd_name STRING COMMENT 'BD姓名，销售人员姓名，示例值：李文华、陈露露、刘南菁等',
	m1_name STRING COMMENT 'M1管理者（销售主管）姓名，即BD的直接上级，示例值：韦贵丰、陈露露、冯朝皇等',
	m2_name STRING COMMENT 'M2管理者（销售经理）姓名，即M1的直接上级，示例值：林金秋、赵奎、桂少达等',
	m3_name STRING COMMENT 'M3管理者（销售总监）姓名，即M2的直接上级，示例值：孙日达、吕建杰、李茂源等',
	is_valid_bd BIGINT COMMENT 'BD有效性标识：1-有效BD（近30天跟进记录>5次且是普通BD），0-无效BD（可能是离职了，也可能是测试BD，也可能是管理者），取值范围：0-1',
	is_m1_manager BIGINT COMMENT '该BD是否是一个合法的M1管理者（有别于普通BD），1-是M1管理者，0-不是M1管理者，取值范围：0-1',
	is_m2_manager BIGINT COMMENT '该BD是否是一个合法的M2管理者（有别于普通BD），1-是M2管理者，0-不是M2管理者，取值范围：0-1',
	is_m3_manager BIGINT COMMENT '该BD是否是一个合法的M3管理者（有别于普通BD），1-是M3管理者，0-不是M3管理者，取值范围：0-1'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日，示例值：20250922') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='BD层级结果表，包含BD的完整层级关系和基于跟进记录的有效性判断，记录销售团队的组织架构和人员有效性状态') 
LIFECYCLE 30;