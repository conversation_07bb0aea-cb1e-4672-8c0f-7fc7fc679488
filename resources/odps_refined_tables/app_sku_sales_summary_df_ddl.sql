CREATE TABLE IF NOT EXISTS app_sku_sales_summary_df(
	sku STRING COMMENT 'SKU编码，商品唯一标识',
	sales_volume BIGINT COMMENT '最近30天销售数量，统计周期内的商品销售件数',
	total_gmv DECIMAL(20,4) COMMENT '最近30天总GMV金额，统计周期内的商品总交易额，单位：元'
)
COMMENT 'SKU销售数据汇总表(最近30天)，包含各SKU在最近30天内的销售数量和GMV数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，数据日期，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment'='SKU销售数据汇总表，用于分析商品销售表现和业绩统计')
LIFECYCLE 90;