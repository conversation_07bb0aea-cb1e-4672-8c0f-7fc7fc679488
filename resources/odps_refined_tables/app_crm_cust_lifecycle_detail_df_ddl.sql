CREATE TABLE IF NOT EXISTS app_crm_cust_lifecycle_detail_df(
    cust_id BIGINT COMMENT '客户编号，唯一标识客户的ID',
    cust_name STRING COMMENT '客户名称',
    cust_type STRING COMMENT '客户类型；枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
    brand_id BIGINT COMMENT '品牌ID',
    brand_name STRING COMMENT '品牌公司名称',
    brand_alias STRING COMMENT '品牌别名',
    city_id BIGINT COMMENT '城市ID(运营服务区)',
    city_name STRING COMMENT '运营城市名称',
    register_province STRING COMMENT '注册时省份',
    register_city STRING COMMENT '注册时城市',
    register_area STRING COMMENT '注册时区域',
    register_time DATETIME COMMENT '注册时间，格式：年月日时分秒',
    administrative_city STRING COMMENT '行政城市',
    bd_id BIGINT COMMENT '销售ID',
    bd_name STRING COMMENT '销售名称',
    bd_zone STRING COMMENT '销售大区',
    m1 STRING COMMENT '城市负责人',
    m2 STRING COMMENT '区域负责人',
    m3 STRING COMMENT '部门负责人',
    is_disabled_bd BIGINT COMMENT 'BD是否离职；枚举值：0-在职，1-离职',
    frequency DECIMAL(38,18) COMMENT '自身采购周期',
    standard_line STRING COMMENT '近1年标准线标记；枚举值：over、normal、no_deal、first_deal',
    life_cycle_detail STRING COMMENT '生命周期标签（当前）',
    lastmonth_life_cycle_detail STRING COMMENT '生命周期标签（上月末）',
    r_value STRING COMMENT 'R价值标签',
    f_value STRING COMMENT 'F价值标签',
    m_value STRING COMMENT 'M价值标签',
    spu_warn STRING COMMENT 'SPU预警标签',
    cust_value STRING COMMENT '用户价值标签',
    last_delivery_time DATETIME COMMENT '最近一次配送时间，格式：年月日时分秒',
    last_follow_time DATETIME COMMENT '最近一次拜访时间，格式：年月日时分秒',
    last_follow_up_way STRING COMMENT '最近一次跟进方式；枚举值：普通拜访-微信、普通拜访-电话、普通拜访-企微、普通上门拜访、有效拜访、电话、上门、上门拜访、微信、其他',
    last_follow_condition STRING COMMENT '最近一次跟进情况描述',
    last_coupon_order_time DATETIME COMMENT '最近一次使用优惠券时间，格式：年月日时分秒',
    last_login_time DATETIME COMMENT '最近一次登录时间，格式：年月日时分秒',
    last_sku_cl_spuname STRING COMMENT '最近一次点击商品详情页的商品名称',
    last_sku_search_spuname STRING COMMENT '最近一次搜索的商品名称',
    sku_cl_spuname_top5_30d STRING COMMENT '最近30天点击商品详情页排名前5的商品及点击数',
    last_order_time DATETIME COMMENT '最近一次下单时间，格式：年月日时分秒',
    last_order_spuname STRING COMMENT '最近一次购买的商品名称',
    order_fruit_real_amt_7d DECIMAL(38,18) COMMENT '最近7天鲜果下单实付金额',
    order_fruit_real_amt_14d DECIMAL(38,18) COMMENT '最近14天鲜果下单实付金额',
    order_days_month_max BIGINT COMMENT '近1年最高月下单频次',
    order_amt_avg_max DECIMAL(38,18) COMMENT '近1年最高下单次均价',
    sku_order_spuname_top10_1y STRING COMMENT '近1年喜爱商品名称前10',
    order_days BIGINT COMMENT '当月频次',
    order_amt_avg DECIMAL(38,18) COMMENT '当月次均价',
    order_amt DECIMAL(38,18) COMMENT '当月实付',
    fruit_order_days BIGINT COMMENT '当月鲜果频次',
    fruit_order_amt DECIMAL(38,18) COMMENT '当月鲜果实付GMV',
    dairy_order_days BIGINT COMMENT '当月乳制品频次',
    dairy_order_amt DECIMAL(38,18) COMMENT '当月乳制品实付GMV',
    other_order_days BIGINT COMMENT '当月其他频次',
    other_order_amt DECIMAL(38,18) COMMENT '当月其他实付GMV',
    self_order_days BIGINT COMMENT '当月自营品牌频次',
    self_order_amt DECIMAL(38,18) COMMENT '当月自营品牌实付GMV',
    lastmonth_order_days BIGINT COMMENT '上月同期频次',
    lastmonth_order_amt_avg DECIMAL(38,18) COMMENT '上月同期次均价',
    lastmonth_order_amt DECIMAL(38,18) COMMENT '上月同期实付',
    lastmonth_fruit_order_days BIGINT COMMENT '上月同期鲜果频次',
    lastmonth_fruit_order_amt DECIMAL(38,18) COMMENT '上月同期鲜果实付GMV',
    lastmonth_dairy_order_days BIGINT COMMENT '上月同期乳制品频次',
    lastmonth_dairy_order_amt DECIMAL(38,18) COMMENT '上月同期乳制品实付GMV',
    lastmonth_other_order_days BIGINT COMMENT '上月同期其他频次',
    lastmonth_other_order_amt DECIMAL(38,18) COMMENT '上月同期其他实付GMV',
    lastmonth_self_order_days BIGINT COMMENT '上月同期自营品牌频次',
    lastmonth_self_order_amt DECIMAL(38,18) COMMENT '上月同期自营品牌实付GMV',
    last_coupon_name STRING COMMENT '最近一次使用优惠券名称',
    dairy_order_amt_60d DECIMAL(38,18) COMMENT '近60天乳制品下单实付金额',
    dairy_order_days_60d BIGINT COMMENT '近60天乳制品下单频次',
    dairy_order_amt_90d DECIMAL(38,18) COMMENT '近90天乳制品下单实付金额',
    first_order_time DATETIME COMMENT '最早一次下单时间，格式：年月日时分秒',
    first_order_date_category4 STRING COMMENT '首日新客四级类目',
    loss_category4 STRING COMMENT '流失四级类目',
    second_order_date_category4 STRING COMMENT '第二天下单四级类目',
    order_real_amt_his DECIMAL(38,18) COMMENT '历史所有下单实付金额',
    order_days_his BIGINT COMMENT '历史所有下单频次（下单天数）'
)
COMMENT '平台客户销售明细表，包含客户基本信息、销售层级、生命周期标签、价值标签、订单行为、商品偏好等详细信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='平台客户销售明细表，用于客户生命周期管理和销售分析')
LIFECYCLE 90;