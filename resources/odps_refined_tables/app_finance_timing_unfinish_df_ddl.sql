CREATE TABLE IF NOT EXISTS app_finance_timing_unfinish_df(
    order_no STRING COMMENT '订单编号，唯一标识一个订单',
    order_time DATETIME COMMENT '下单时间，格式为年月日时分秒',
    cust_id BIGINT COMMENT '客户ID，唯一标识一个客户',
    cust_name STRING COMMENT '客户名，客户店铺或企业名称',
    cust_team STRING COMMENT '客户团队类型，取值范围：平台客户、大客户',
    sku_id STRING COMMENT 'SKU编号，商品最小库存单位标识',
    spu_name STRING COMMENT '商品名，标准产品单元名称',
    category1 STRING COMMENT '商品一级类目，商品分类的最高层级',
    sku_cnt BIGINT COMMENT '订单内商品件数，订单中包含的商品总数量',
    origin_total_amt DECIMAL(38,18) COMMENT '订单应付金额，订单原始总金额',
    real_total_amt DECIMAL(38,18) COMMENT '订单实付金额，客户实际支付金额',
    unfinish_sku_cnt BIGINT COMMENT '未配送商品件数，尚未配送的商品数量',
    unfinish_amt DECIMAL(38,18) COMMENT '未配送金额，尚未配送的商品金额',
    date_flag STRING COMMENT '日期标识，业务日期标识',
    order_date STRING COMMENT '下单日期，格式为yyyyMMdd'
)
COMMENT '财务口径省心送余额表，记录未完成配送的订单余额信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment'='财务口径省心送余额表，用于跟踪和管理未完成配送订单的财务信息')
LIFECYCLE 30;