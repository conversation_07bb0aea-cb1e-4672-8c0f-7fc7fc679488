CREATE TABLE IF NOT EXISTS app_m2_sales_performance_mi(
	months STRING COMMENT '月份，格式：yyyyMM，如202509表示2025年9月',
	m2 STRING COMMENT 'M2管理者姓名，取值范围：姜浪、孙军杰、孙日达、彭琨、林金秋、桂少达、翟远方、赵奎、陈欲豪',
	m3 STRING COMMENT 'M3管理者姓名，取值范围：孙日达、吕建杰',
	region STRING COMMENT '销售大区，取值范围：西南大区、山东大区、昆明区域、华中大区、闽桂大区、苏皖大区、浙江大区、上海大区、华南大区',
	category STRING COMMENT '类目，取值范围：安佳铁塔、乳制品(不含AT)和其他、鲜果',
	gmv_target DECIMAL(38,18) COMMENT '实付GMV目标',
	real_total_amt DECIMAL(38,18) COMMENT '实付GMV',
	real_total_amt_last_1d DECIMAL(38,18) COMMENT '当天实付GMV',
	real_total_amt_last_2d DECIMAL(38,18) COMMENT '前一天实付GMV',
	time_coming_rate DECIMAL(38,18) COMMENT '时间进度'
) 
COMMENT '销售M2维度达成表现表，记录M2管理者在各销售大区和类目下的GMV目标达成情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='销售M2维度达成表现表，包含M2管理者的GMV目标、实际达成、时间进度等关键指标') 
LIFECYCLE 30;