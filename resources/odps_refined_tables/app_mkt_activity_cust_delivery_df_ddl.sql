```sql
CREATE TABLE IF NOT EXISTS app_mkt_activity_cust_delivery_df(
    activity_id BIGINT COMMENT '活动ID，唯一标识一个营销活动',
    activity_name STRING COMMENT '活动名称，如：新品活动1.5、1.5临保风险（广州）等',
    start_time DATETIME COMMENT '活动开始时间，格式：年月日时分秒',
    end_time DATETIME COMMENT '活动结束时间，格式：年月日时分秒',
    activity_type STRING COMMENT '活动类型，枚举值：特价活动',
    activity_tag STRING COMMENT '活动目的，枚举值：新品推广、滞销促销、临保清仓、潜力品推广、用户召回、6、5',
    city_id BIGINT COMMENT '运营服务区ID，唯一标识一个城市区域',
    city_name STRING COMMENT '运营服务区名称，如：合肥、苏州、广州等（约85个枚举值）',
    large_area_id BIGINT COMMENT '运营服务大区ID，唯一标识一个大区',
    large_area_name STRING COMMENT '运营服务大区名称，枚举值：苏州大区、广州大区、杭州大区、苏南大区、福州大区、南宁大区、昆明大区、上海大区、武汉大区、重庆大区、成都大区、长沙大区、青岛大区、贵阳大区、昆明快递大区',
    cust_id BIGINT COMMENT '客户ID，唯一标识一个客户',
    cust_name STRING COMMENT '客户名称，如：椰子冻小姐姐、米芝莲华侨城店等',
    life_cycle_detail STRING COMMENT '生命周期标签（细）（活动开始时间），枚举值：None、S1、B2、B1、A2、S2、A1、A3、W、N1、L2、N2、L1、L3、N0',
    order_cnt BIGINT COMMENT '活动订单数，统计活动期间产生的订单数量',
    sku_cnt BIGINT COMMENT '活动SKU数量，统计活动涉及的商品品类数量',
    real_total_amt DECIMAL(38,18) COMMENT '实付金额，客户实际支付的金额',
    origin_total_amt DECIMAL(38,18) COMMENT '应付金额，订单原始应付金额',
    delivery_fruit_origin_amt_before_8_14 DECIMAL(38,18) COMMENT '前8-14天鲜果应付GMV，活动开始前8-14天的鲜果类商品原始GMV',
    delivery_fruit_origin_gross_before_8_14 DECIMAL(38,18) COMMENT '前8-14天鲜果应付毛利润，活动开始前8-14天的鲜果类商品毛利润',
    delivery_notfruit_origin_amt_before_8_14 DECIMAL(38,18) COMMENT '前8-14天标品应付GMV，活动开始前8-14天的标品类商品原始GMV',
    delivery_notfruit_origin_gross_before_8_14 DECIMAL(38,18) COMMENT '前8-14天标品应付毛利润，活动开始前8-14天的标品类商品毛利润',
    delivery_fruit_origin_amt_before_1_7 DECIMAL(38,18) COMMENT '前1-7天鲜果应付GMV，活动开始前1-7天的鲜果类商品原始GMV',
    delivery_fruit_origin_gross_before_1_7 DECIMAL(38,18) COMMENT '前1-7天鲜果应付毛利润，活动开始前1-7天的鲜果类商品毛利润',
    delivery_notfruit_origin_amt_before_1_7 DECIMAL(38,18) COMMENT '前1-7天标品应付GMV，活动开始前1-7天的标品类商品原始GMV',
    delivery_notfruit_origin_gross_before_1_7 DECIMAL(38,18) COMMENT '前1-7天标品应付毛利润，活动开始前1-7天的标品类商品毛利润',
    delivery_fruit_origin_amt_after_1_7 DECIMAL(38,18) COMMENT '后1-7天鲜果应付GMV，活动结束后1-7天的鲜果类商品原始GMV',
    delivery_fruit_origin_gross_after_1_7 DECIMAL(38,18) COMMENT '后1-7天鲜果应付毛利润，活动结束后1-7天的鲜果类商品毛利润',
    delivery_notfruit_origin_amt_after_1_7 DECIMAL(38,18) COMMENT '后1-7天标品应付GMV，活动结束后1-7天的标品类商品原始GMV',
    delivery_notfruit_origin_gross_after_1_7 DECIMAL(38,18) COMMENT '后1-7天标品应付毛利润，活动结束后1-7天的标品类商品毛利润',
    delivery_fruit_origin_amt_after_8_14 DECIMAL(38,18) COMMENT '后8-14天鲜果应付GMV，活动结束后8-14天的鲜果类商品原始GMV',
    delivery_fruit_origin_gross_after_8_14 DECIMAL(38,18) COMMENT '后8-14天鲜果应付毛利润，活动结束后8-14天的鲜果类商品毛利润',
    delivery_notfruit_origin_amt_after_8_14 DECIMAL(38,18) COMMENT '后8-14天标品应付GMV，活动结束后8-14天的标品类商品原始GMV',
    delivery_notfruit_origin_gross_after_8_14 DECIMAL(38,18) COMMENT '后8-14天标品应付毛利润，活动结束后8-14天的标品类商品毛利润',
    delivery_fruit_origin_amt_after_15_21 DECIMAL(38,18) COMMENT '后15-21天鲜果应付GMV，活动结束后15-21天的鲜果类商品原始GMV',
    delivery_fruit_origin_gross_after_15_21 DECIMAL(38,18) COMMENT '后15-21天鲜果应付毛利润，活动结束后15-21天的鲜果类商品毛利润',
    delivery_notfruit_origin_amt_after_15_21 DECIMAL(38,18) COMMENT '后15-21天标品应付GMV，活动结束后15-21天的标品类商品原始GMV',
    delivery_notfruit_origin_gross_after_15_21 DECIMAL(38,18) COMMENT '后15-21天标品应付毛利润，活动结束后15-21天的标品类商品毛利润',
    delivery_fruit_origin_amt_after_22_28 DECIMAL(38,18) COMMENT '后22-28天鲜果应付GMV，活动结束后22-28天的鲜果类商品原始GMV',
    delivery_fruit_origin_gross_after_22_28 DECIMAL(38,18) COMMENT '后22-28天鲜果应付毛利润，活动结束后22-28天的鲜果类商品毛利润',
    delivery_notfruit_origin_amt_after_22_28 DECIMAL(38,18) COMMENT '后22-28天标品应付GMV，活动结束后22-28天的标品类商品原始GMV',
    delivery_notfruit_origin_gross_after_22_28 DECIMAL(38,18) COMMENT '后22-28天标品应付毛利润，活动结束后22-28天的标品类商品毛利润'
)
COMMENT '活动效果评估统计表，用于分析营销活动对客户购买行为的影响，包含活动前后不同时间段的GMV和毛利润对比'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='营销活动客户交付效果评估表，统计活动前后不同时间段的商品交付情况和财务指标')
LIFECYCLE 30;
```