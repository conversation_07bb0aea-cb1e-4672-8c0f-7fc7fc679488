```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_delivery_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `sku_id` STRING COMMENT 'SKU ID，商品最小库存单位的唯一标识',
  `spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格包装信息，如"10KG*1箱"',
  `category1` STRING COMMENT '一级类目，取值范围：乳制品、其他',
  `cust_cnt` BIGINT COMMENT '人群数量，统计购买该SKU的客户总数',
  `fruit_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '鲜果履约应付金额，鲜果类商品的原始应付总额',
  `fruit_dlv_cust_cnt` BIGINT COMMENT '鲜果履约客户数，购买鲜果类商品的客户数量',
  `dairy_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '乳制品履约应付金额，乳制品类商品的原始应付总额',
  `dairy_dlv_cust_cnt` BIGINT COMMENT '乳制品履约客户数，购买乳制品类商品的客户数量',
  `other_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '其他履约应付金额，其他类商品的原始应付总额',
  `other_dlv_cust_cnt` BIGINT COMMENT '其他履约客户数，购买其他类商品的客户数量'
)
COMMENT '人群品类偏好分析表，统计不同客户群体对各类商品的购买偏好和消费行为'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '人群品类偏好分析表，用于分析不同客户群体对鲜果、乳制品和其他商品的购买偏好和消费行为',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```