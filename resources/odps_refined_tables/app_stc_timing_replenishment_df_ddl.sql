```sql
CREATE TABLE IF NOT EXISTS app_stc_timing_replenishment_df(
    warehouse_no BIGINT COMMENT '库存仓号，数值型标识',
    warehouse_name STRING COMMENT '库存仓名称，枚举值：嘉兴总仓、华西总仓、长沙总仓、昆明总仓、东莞总仓、武汉总仓',
    sku_id STRING COMMENT 'SKU编码，商品唯一标识',
    sku_disc STRING COMMENT '商品描述，包含规格信息如"2KG*5包/82％"',
    spu_name STRING COMMENT '商品名称，如"寇曼歌文黄油"',
    supplier STRING COMMENT '供货商名称，如"上海阿啰哈贸易有限公司"',
    store_method STRING COMMENT '存储方式；枚举值：冷冻、冷藏、常温',
    timing_dlv_14_day_sku_cnt BIGINT COMMENT '未来14天设置配送量，单位：件',
    timing_dlv_14_30_day_sku_cnt BIGINT COMMENT '未来14天至30天设置配送量，单位：件',
    timing_dlv_30_90_day_sku_cnt BIGINT COMMENT '未来30天至90天设置配送量，单位：件',
    timing_no_plan_sku_cnt BIGINT COMMENT '未设置配送量，单位：件',
    init_quantity BIGINT COMMENT '期末库存数量，单位：件',
    purchase_on_way_quality BIGINT COMMENT '采购在途数量，单位：件',
    no_dlv_sku_cnt BIGINT COMMENT '未履约数量，单位：件',
    supplement_14day BIGINT COMMENT '未来14天需求量，单位：件',
    supplement_30day BIGINT COMMENT '未来30天需求量，单位：件',
    supplement_out_30day BIGINT COMMENT '30天以上需求量，单位：件',
    supplement_cnt DECIMAL(38,18) COMMENT '建议补货量，精确到小数点后18位'
) 
COMMENT '省心送仓储预警数据表，包含库存预警和补货建议信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期')
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='省心送仓储预警数据，用于库存管理和补货决策支持') 
LIFECYCLE 30;
```