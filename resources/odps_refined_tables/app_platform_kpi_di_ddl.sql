CREATE TABLE IF NOT EXISTS app_platform_kpi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位为元',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位为元',
	`cust_cnt` BIGINT COMMENT '履约客户数，去重后的客户数量',
	`preferential_amt` DECIMAL(38,18) COMMENT '营销金额，单位为元，表示营销活动产生的优惠金额',
	`timing_origin_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV，单位为元',
	`timing_cust_cnt` BIGINT COMMENT '省心送履约客户数，去重后的客户数量',
	`timing_ratio` DECIMAL(38,18) COMMENT '省心送合单率，取值范围0-1，表示合单比例',
	`timing_cust_30_not_cnt` BIGINT COMMENT '省心送超过30天未履约客户数',
	`timing_cust_30_90_cnt` BIGINT COMMENT '省心送在30天-90天履约客户数',
	`login_uv` BIGINT COMMENT '登陆UV，独立访客数',
	`order_uv` BIGINT COMMENT '交易客户数，去重后的下单客户数',
	`activity_uv` BIGINT COMMENT '特价点击UV，独立访客数',
	`activity_order_uv` BIGINT COMMENT '特价下单人数，去重后的客户数',
	`exchange_uv` BIGINT COMMENT '换购点击UV，独立访客数',
	`exchange_order_uv` BIGINT COMMENT '换购下单人数，去重后的客户数',
	`expand_uv` BIGINT COMMENT '拓展购买点击UV，独立访客数',
	`expand_order_uv` BIGINT COMMENT '拓展购买下单人数，去重后的客户数',
	`meeting_uv` BIGINT COMMENT '会场活动页点击UV，独立访客数',
	`meeting_order_uv` BIGINT COMMENT '会场活动页下单人数，去重后的客户数',
	`other_uv` BIGINT COMMENT '其他点击UV，独立访客数',
	`other_order_uv` BIGINT COMMENT '其他下单人数，去重后的客户数',
	`timing_all_cust_cnt` BIGINT COMMENT '省心送总客户数，包含所有状态的客户'
) 
COMMENT '平台KPI指标表，包含履约、营销、省心送、用户行为等核心业务指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='平台KPI指标表，用于监控和分析平台核心业务表现') 
LIFECYCLE 30;