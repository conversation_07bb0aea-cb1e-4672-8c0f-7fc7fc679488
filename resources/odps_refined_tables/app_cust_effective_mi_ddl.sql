CREATE TABLE IF NOT EXISTS app_cust_effective_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	m1_name STRING COMMENT 'M1管理者姓名，即销售主管',
	m2_name STRING COMMENT 'M2管理者姓名，即销售经理，M1的直接上级',
	m3_name STRING COMMENT 'M3管理者姓名，即销售总监，M2的直接上级',
	zone_name STRING COMMENT '运营区域名称，枚举值包括：东莞、佛山、厦门、四川、大粤西、广州、广西、徽京、无锡、昆明、杭州、杭州湾、武汉、江西、济南、浙南、浦东、浦西、深圳、福泉、苏北、苏州、贵阳、重庆、长沙、青岛',
	region STRING COMMENT '销售大区名称，枚举值包括：华南大区、闽桂大区、西南大区、苏皖大区、昆明区域、浙江大区、华中大区、山东大区、上海大区',
	effective_cust_target BIGINT COMMENT '有效月活客户数目标值',
	effect_cust_cnt BIGINT COMMENT '实际有效月活客户数',
	time_scheduling DECIMAL(38,18) COMMENT '时间进度百分比，范围0-1之间的小数'
)
COMMENT 'M1/M2/M3管理者层级下的履约有效月活客户数统计表，包含各层级管理者的月活客户目标完成情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='M1/M2/M3 履约有效月活客户数统计表')
LIFECYCLE 30;