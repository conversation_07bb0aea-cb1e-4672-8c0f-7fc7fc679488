CREATE TABLE IF NOT EXISTS app_saas_market_item_on_sale_sold_out_detail_di(
	tenant_id BIGINT COMMENT '租户ID，标识SaaS平台的不同客户租户',
	time_tag STRING COMMENT '时间标签，格式为yyyyMMdd，表示年月日',
	item_id BIGINT COMMENT '商品ID，唯一标识SaaS平台中的商品',
	sale_price DECIMAL(38,18) COMMENT '商品售价，支持高精度小数计算',
	sold_out_time BIGINT COMMENT '日累计售罄时长，单位为秒，0表示未售罄，86400表示全天售罄',
	on_sale_time BIGINT COMMENT '日累计上架时长，单位为秒，取值范围[7348, 86400]，86400表示全天在售'
) 
COMMENT 'SaaS商品上架售罄汇总表，统计每日商品的上架和售罄时长情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS商品销售状态监控表，用于分析商品的上架和售罄情况') 
LIFECYCLE 30;