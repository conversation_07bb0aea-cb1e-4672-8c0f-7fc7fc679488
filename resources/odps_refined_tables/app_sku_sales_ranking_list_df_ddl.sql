```sql
CREATE TABLE IF NOT EXISTS app_sku_sales_ranking_list_df(
    merchant_type STRING COMMENT '客户类型（鲜沐）；枚举类型，取值范围：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、糖水/水果捞、西餐披萨、其他',
    sku_list STRING COMMENT 'SKU销售排行榜（Top20），以逗号分隔的SKU编码列表',
    day_tag STRING COMMENT '日期标签，格式为YYYYMMDD，表示年月日'
)
COMMENT '业态销售榜单，展示各业态下SKU的销售排名情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为YYYYMMDD，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='业态销售榜单表，包含各客户类型下SKU的销售排名数据')
LIFECYCLE 30;
```