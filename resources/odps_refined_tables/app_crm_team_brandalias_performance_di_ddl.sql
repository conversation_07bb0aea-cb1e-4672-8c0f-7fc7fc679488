```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_team_brandalias_performance_di`(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户',
    `brand_alias` STRING COMMENT '品牌别名，如：一鸣、七分甜、乐乐茶、司乎、礼厚茶肆、茉莉奶白、上海茶百道、广东茶百道、江苏茶百道、江西茶百道、浙江茶百道、福建茶百道等',
    `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额，订单原始应付金额汇总',
    `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额，订单实际支付金额汇总',
    `order_brand_cnt` BIGINT COMMENT '交易公司数，发生交易的公司数量',
    `order_cust_cnt` BIGINT COMMENT '交易门店数，发生交易的门店数量',
    `order_order_cnt` BIGINT COMMENT '交易订单数，交易订单总数',
    `new_cust_cnt` BIGINT COMMENT '活跃门店数中新增门店数，新激活的门店数量',
    `new_cust_gmv` DECIMAL(38,18) COMMENT '新增活跃门店GMV，新增门店产生的交易总额',
    `close_cust_cnt` BIGINT COMMENT '活跃门店数中倒闭门店数，关闭或停业的门店数量',
    `close_cust_gmv` DECIMAL(38,18) COMMENT '倒闭门店GMV，倒闭门店产生的交易总额',
    `old_cust_cnt` BIGINT COMMENT '老活跃门店数，持续活跃的现有门店数量',
    `old_cust_gmv` DECIMAL(38,18) COMMENT '老活跃门店GMV，老门店产生的交易总额',
    `new_noactive_cust_cnt` BIGINT COMMENT '拉新门店数（仅注册未下单），仅注册但未下单的新门店数量',
    `new_active_cust_cnt` BIGINT COMMENT '拉新门店数（注册且下单），注册并完成下单的新门店数量',
    `new_active_gmv` DECIMAL(38,18) COMMENT '拉新门店GMV，新激活门店产生的交易总额',
    `order_replace_cust_cnt` BIGINT COMMENT '代下单门店数，代为下单的门店数量',
    `order_replace_gmv` DECIMAL(38,18) COMMENT '代下单实付金额，代下单实际支付金额汇总',
    `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，履约订单原始应付金额汇总',
    `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，履约订单实际支付金额汇总',
    `cost_amt` DECIMAL(38,18) COMMENT '履约成本，履约相关的成本费用',
    `order_cnt` BIGINT COMMENT '履约订单数，履约完成的订单总数',
    `sku_cnt` BIGINT COMMENT '总配送件数，配送的商品SKU总数量',
    `point_cnt` BIGINT COMMENT '总点位数，配送点位总数',
    `cust_cnt` BIGINT COMMENT '履约门店数，完成履约的门店数量',
    `brand_cnt` BIGINT COMMENT '履约公司数，完成履约的公司数量',
    `self_real_total_amt` DECIMAL(38,18) COMMENT '自营实付总金额，自营业务实际支付金额汇总',
    `self_cost_amt` DECIMAL(38,18) COMMENT '自营成本，自营业务相关成本费用',
    `self_cust_cnt` BIGINT COMMENT '自营品牌门店数，自营品牌门店数量',
    `self_brand_cnt` BIGINT COMMENT '自营品牌公司数，自营品牌公司数量',
    `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实付总金额，省心送业务实际支付金额汇总',
    `timing_cost_amt` DECIMAL(38,18) COMMENT '省心送成本，省心送业务相关成本费用',
    `timing_cust_cnt` BIGINT COMMENT '省心送门店数，使用省心送服务的门店数量',
    `timing_brand_cnt` BIGINT COMMENT '省心送公司数，使用省心送服务的公司数量',
    `fruit_real_total_amt` DECIMAL(38,18) COMMENT '鲜果实付总金额，鲜果业务实际支付金额汇总',
    `fruit_cost_amt` DECIMAL(38,18) COMMENT '鲜果成本，鲜果业务相关成本费用',
    `fruit_cash_real_total_amt` DECIMAL(38,18) COMMENT '鲜果账期实付金额，鲜果账期实际支付金额',
    `fruit_cust_cnt` BIGINT COMMENT '鲜果门店数，购买鲜果产品的门店数量',
    `dairy_real_total_amt` DECIMAL(38,18) COMMENT '乳制品实付总金额，乳制品业务实际支付金额汇总',
    `dairy_cost_amt` DECIMAL(38,18) COMMENT '乳制品成本，乳制品业务相关成本费用',
    `dairy_cash_real_cost_amt` DECIMAL(38,18) COMMENT '乳制品账期实付金额，乳制品账期实际支付金额',
    `dairy_cust_cnt` BIGINT COMMENT '乳制品门店数，购买乳制品的门店数量',
    `nodairy_real_total_amt` DECIMAL(38,18) COMMENT '非乳制品实付总金额，非乳制品业务实际支付金额汇总',
    `nodairy_cost_amt` DECIMAL(38,18) COMMENT '非乳制品成本，非乳制品业务相关成本费用',
    `nodairy_cash_cost_amt` DECIMAL(38,18) COMMENT '非乳制品账期实付金额，非乳制品账期实际支付金额',
    `nodairy_cust_cnt` BIGINT COMMENT '非乳制品门店数，购买非乳制品的门店数量',
    `cash_real_total_amt` DECIMAL(38,18) COMMENT '账期实付金额，所有账期业务实际支付金额汇总',
    `nocash_real_total_amt` DECIMAL(38,18) COMMENT '非账期实付金额，非账期业务实际支付金额汇总',
    `replace_origin_total_amt` DECIMAL(38,18) COMMENT '代仓应付总金额，代仓业务原始应付金额汇总',
    `replace_real_total_amt` DECIMAL(38,18) COMMENT '代仓实付总金额，代仓业务实际支付金额汇总',
    `replace_cust_cnt` BIGINT COMMENT '代仓门店数，使用代仓服务的门店数量',
    `self_after_sale_amt` DECIMAL(38,18) COMMENT '自营售后金额，自营业务售后相关金额',
    `replace_after_sale_amt` DECIMAL(38,18) COMMENT '代仓售后金额，代仓业务售后相关金额'
) 
COMMENT '大客户团队汇总表，按客户团队和品牌维度统计的交易、履约、各业务线（自营、省心送、鲜果、乳制品、非乳制品、代仓等）的业绩数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='大客户团队业绩汇总表，包含交易、履约、各业务线的详细业绩指标统计') 
LIFECYCLE 30;
```