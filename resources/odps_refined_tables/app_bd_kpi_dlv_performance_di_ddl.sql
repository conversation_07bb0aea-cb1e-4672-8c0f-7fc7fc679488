```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_bd_kpi_dlv_performance_di` (
  `bd_id` BIGINT COMMENT '业绩归属的BD_ID，销售人员唯一标识',
  `bd_name` STRING COMMENT '业绩归属的销售人员姓名',
  `bd_m1` STRING COMMENT 'BD所属M1管理者姓名，即BD的直接上级',
  `bd_m2` STRING COMMENT 'BD所属M2管理者姓名，即M1的直接上级',
  `bd_m3` STRING COMMENT 'BD所属M3管理者姓名，即M2的直接上级',
  `cust_m1` STRING COMMENT '客户所属M1管理者姓名',
  `zone_name` STRING COMMENT '客户所属销售区域，枚举值：浙南、深圳、杭州湾、大粤西、四川、苏北、广西、武汉、长沙、徽京、浦西、佛山、浦东、东莞、重庆、青岛、杭州、江西、广州、苏州、福泉、厦门、昆明、无锡、贵阳、济南',
  `is_same_city` STRING COMMENT '是否同城，枚举值：是、否',
  `kpi_gmv_achieve_amt` DECIMAL(38,18) COMMENT 'KPI口径GMV达成金额，单位：元',
  `kpi_fruit_gmv_achieve_amt` DECIMAL(38,18) COMMENT 'KPI口径鲜果GMV达成金额，单位：元',
  `dlv_real_amt` DECIMAL(38,18) COMMENT '履约实付GMV金额，单位：元',
  `no_at_dlv_real_amt` DECIMAL(38,18) COMMENT '非AT履约实付GMV金额，单位：元',
  `fruit_dlv_real_amt` DECIMAL(38,18) COMMENT '鲜果履约实付GMV金额，单位：元',
  `dairy_dlv_real_amt` DECIMAL(38,18) COMMENT '乳制品履约实付GMV金额，单位：元',
  `dairy_no_at_dlv_real_amt` DECIMAL(38,18) COMMENT '乳制品非AT履约实付GMV金额，单位：元',
  `other_dlv_real_amt` DECIMAL(38,18) COMMENT '其他品类履约实付GMV金额，单位：元',
  `at_dlv_real_amt` DECIMAL(38,18) COMMENT 'AT履约实付GMV金额，单位：元',
  `dlv_profit_amt` DECIMAL(38,18) COMMENT '履约实付毛利润金额，单位：元',
  `no_at_dlv_profit_amt` DECIMAL(38,18) COMMENT '非AT履约实付毛利润金额，单位：元',
  `fruit_dlv_profit_amt` DECIMAL(38,18) COMMENT '鲜果履约实付毛利润金额，单位：元',
  `dairy_dlv_profit_amt` DECIMAL(38,18) COMMENT '乳制品履约实付毛利润金额，单位：元',
  `dairy_no_at_dlv_profit_amt` DECIMAL(38,18) COMMENT '乳制品非AT履约实付毛利润金额，单位：元',
  `other_dlv_profit_amt` DECIMAL(38,18) COMMENT '其他品类履约实付毛利润金额，单位：元',
  `at_dlv_profit_amt` DECIMAL(38,18) COMMENT 'AT履约实付毛利润金额，单位：元',
  `dlv_cust_num` BIGINT COMMENT '自营品履约客户数量',
  `no_at_dlv_cust_num` BIGINT COMMENT '非AT履约客户数量',
  `fruit_dlv_cust_num` BIGINT COMMENT '鲜果履约客户数量',
  `dairy_dlv_cust_num` BIGINT COMMENT '乳制品履约客户数量',
  `dairy_no_at_dlv_cust_num` BIGINT COMMENT '乳制品不含AT履约客户数量',
  `other_dlv_cust_num` BIGINT COMMENT '其他品类履约客户数量',
  `at_dlv_cust_num` BIGINT COMMENT 'AT履约客户数量',
  `pop_real_total_gmv` DECIMAL(38,18) COMMENT '全品类交易实付GMV金额，单位：元',
  `pop_paid_users` BIGINT COMMENT '全品类交易客户数量',
  `pop_fruit_real_total_gmv` DECIMAL(38,18) COMMENT '全品类鲜果交易实付GMV金额，单位：元',
  `pop_fruit_paid_users` BIGINT COMMENT '全品类鲜果交易客户数量'
) 
COMMENT '平台销售KPI口径达成日表，记录销售人员每日KPI指标达成情况和业绩数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='销售KPI达成日表，包含销售人员的层级关系、区域信息、各类GMV达成金额、利润金额和客户数量等核心业务指标',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```