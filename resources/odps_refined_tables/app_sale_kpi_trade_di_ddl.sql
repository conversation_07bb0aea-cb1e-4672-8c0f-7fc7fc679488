CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sale_kpi_trade_di`(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
	`order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额，单位为元',
	`order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额，单位为元',
	`order_cust_cnt` BIGINT COMMENT '交易客户数，统计当日有交易行为的客户数量',
	`order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(平均每用户收入)，计算公式：应付总金额/客户数，单位为元/客户',
	`order_cnt` BIGINT COMMENT '交易订单数，统计当日产生的订单数量',
	`lose_cust_cnt` BIGINT COMMENT '交易流失客户数，定义为90天内活跃用户近60天未下单客户数',
	`lose_cust_ratio` DECIMAL(38,18) COMMENT '交易流失率，计算公式：流失客户数/总客户数',
	`delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位为元',
	`delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位为元',
	`delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数，统计当日有履约行为的客户数量',
	`delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润，单位为元',
	`delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润，单位为元',
	`delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润，单位为元',
	`delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次，平均履约天数',
	`delivery_point_cnt` BIGINT COMMENT '履约点位数，统计当日有履约行为的点位数量',
	`delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用，单位为元',
	`new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额，新客定义为首次履约客户，单位为元',
	`new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额，单位为元',
	`new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
	`new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润，单位为元',
	`old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额，老客定义为非首次履约客户，单位为元',
	`old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额，单位为元',
	`old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
	`old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润，单位为元',
	`order_sku_cnt` BIGINT COMMENT '交易SKU款数，统计当日有交易的SKU数量',
	`order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量，单位为KG',
	`delivery_sku_cnt` BIGINT COMMENT '履约SKU款数，统计当日有履约的SKU数量',
	`delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量，单位为KG'
) 
COMMENT '销售KPI指标汇总表，包含交易和履约相关的核心业务指标，用于销售绩效分析和业务监控'
PARTITIONED BY (
	`ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = '销售KPI指标汇总表',
	'lifecycle' = '30'
);