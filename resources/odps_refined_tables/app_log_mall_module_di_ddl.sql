CREATE TABLE IF NOT EXISTS app_log_mall_module_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
	`module_name` STRING COMMENT '模块名称，取值范围：meetings、个人中心、个人抽奖、中转页、优惠券、优惠券商品、其他、再来一单、分类、常用推荐、换购、搜索、支付、注册信息、活动页、特价专区、省心送、购物车、选择地址、采购助手、首页、首页商品、首页弹窗',
	`exposure_pv` BIGINT COMMENT '模块曝光PV（页面浏览量）',
	`exposure_uv` BIGINT COMMENT '模块曝光UV（独立访客数）',
	`click_pv` BIGINT COMMENT '模块点击PV（点击页面浏览量）',
	`click_uv` BIGINT COMMENT '模块点击UV（独立点击用户数）',
	`detail_click_pv` BIGINT COMMENT '模块详情页点击PV',
	`detail_click_uv` BIGINT COMMENT '模块详情页点击UV',
	`handle_click_pv` BIGINT COMMENT '模块handle点击PV',
	`handle_click_uv` BIGINT COMMENT '模块handle点击UV',
	`purchase_click_pv` BIGINT COMMENT '模块立即采购点击PV',
	`purchase_click_uv` BIGINT COMMENT '模块立即采购点击UV',
	`cart_buy_pv` BIGINT COMMENT '模块加购PV',
	`cart_buy_uv` BIGINT COMMENT '模块加购UV',
	`detail_cart_buy_pv` BIGINT COMMENT '模块详情页加购PV',
	`detail_cart_buy_uv` BIGINT COMMENT '模块详情页加购UV',
	`handle_cart_buy_pv` BIGINT COMMENT '模块handle加购PV',
	`handle_cart_buy_uv` BIGINT COMMENT '模块handle加购UV',
	`instant_buy_pv` BIGINT COMMENT '模块立即购买PV',
	`instant_buy_uv` BIGINT COMMENT '模块立即购买UV',
	`detail_instant_buy_pv` BIGINT COMMENT '模块详情页立即购买PV',
	`detail_instant_buy_uv` BIGINT COMMENT '模块详情页立即购买UV',
	`handle_instant_buy_pv` BIGINT COMMENT '模块handle立即购买PV',
	`handle_instant_buy_uv` BIGINT COMMENT '模块handle立即购买UV',
	`expand_buy_pv` BIGINT COMMENT '模块拓展购买确认支付PV',
	`expand_buy_uv` BIGINT COMMENT '模块拓展购买确认支付UV',
	`timing_buy_pv` BIGINT COMMENT '模块省心送确认支付PV',
	`timing_buy_uv` BIGINT COMMENT '模块省心送确认支付UV',
	`order_order_cnt` BIGINT COMMENT '模块购买次数',
	`order_cust_cnt` BIGINT COMMENT '模块购买人数'
) 
COMMENT '商城SKU流量分析表，记录各模块的曝光、点击、加购、购买等流量指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='商城SKU流量分析表，用于分析各模块的用户行为数据') 
LIFECYCLE 30;