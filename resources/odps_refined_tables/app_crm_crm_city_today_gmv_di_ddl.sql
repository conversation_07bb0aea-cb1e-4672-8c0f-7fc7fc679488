CREATE TABLE IF NOT EXISTS app_crm_crm_city_today_gmv_di(
    `city` STRING COMMENT '城市名称，枚举类型，取值范围包括：上海市、深圳市、恩施土家族苗族自治州、玉溪市、湘西土家族苗族自治州、红河哈尼族彝族自治州、梅州市、昆明市、曲靖市、桂林市、来宾市、河池市、保山市、岳阳市、襄阳市、衡阳市、武汉市、惠州市、江门市、眉山市、舟山市、六安市、泉州市、杭州市、台州市、珠海市、芜湖市、淮南市、永州市、马鞍山市、威海市、孝感市、咸宁市、无锡市、龙岩市、黄石市、德阳市、茂名市、淮安市、湘潭市、漳州市、成都市、潮州市、益阳市、邵阳市、仙桃市、重庆市、株洲市、厦门市、常德市等全国各城市',
    `area` STRING COMMENT '区域名称，枚举类型，取值范围包括：松江区、闵行区、嘉定区、龙岗区、杨浦区、长宁区、南山区、龙华区、奉贤区、浦东新区、静安区、光明区、罗湖区、普陀区、黄浦区、虹口区、福田区、青浦区、坪山区、金山区、宝山区、盐田区、宝安区、崇明区、徐汇区、无',
    `order_gmv` DECIMAL(38,18) COMMENT '下单总GMV，单位：元，保留18位小数精度',
    `order_merchant` BIGINT COMMENT '下单客户数，统计当日下单的商户数量',
    `fruit_gmv` DECIMAL(38,18) COMMENT '鲜果品类GMV，单位：元，保留18位小数精度',
    `dairy_gmv` DECIMAL(38,18) COMMENT '乳制品品类GMV，单位：元，保留18位小数精度',
    `non_dairy_gmv` DECIMAL(38,18) COMMENT '非乳制品品类GMV，单位：元，保留18位小数精度',
    `brand_gmv` DECIMAL(38,18) COMMENT '自营品牌GMV，单位：元，保留18位小数精度',
    `agent_gmv` DECIMAL(38,18) COMMENT '代售商品GMV，单位：元，保留18位小数精度',
    `reward_gmv` DECIMAL(38,18) COMMENT '固定奖励SKU的GMV，单位：元，保留18位小数精度',
    `pull_new_amount` BIGINT COMMENT '拉新数量，统计当日新增客户数',
    `visit_num` BIGINT COMMENT '拜访数量，统计当日拜访客户次数',
    `delivery_gmv` DECIMAL(38,18) COMMENT '配送服务GMV，单位：元，保留18位小数精度'
)
COMMENT '城市当日GMV统计表，按城市和区域维度统计各品类的销售GMV、客户数、拉新数、拜访数等业务指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '城市维度GMV日统计表，用于分析各城市区域的销售表现和业务指标',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;