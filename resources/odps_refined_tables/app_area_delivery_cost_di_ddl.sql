```sql
CREATE TABLE IF NOT EXISTS app_area_delivery_cost_di(
    area_no BIGINT COMMENT '城配仓编号，取值范围：1-144',
    area_name STRING COMMENT '城配仓名称，枚举值包括：南京仓、秀洲仓、东莞仓、中山仓、宁波仓、温州仓、长沙仓、无锡仓、盐城仓、武汉仓、株洲仓、惠州仓、烟台仓、常德仓、南宁仓、合肥仓、徐州仓、衡阳仓、宜春仓、泉州仓、南通仓、厦门仓、潮汕仓、荆门仓、成都仓、台州仓、福州仓、南昌仓、昆明仓、深圳仓、苏州仓、贵阳仓、重庆仓、广州仓、佛山仓、杭州三仓、襄阳仓、湖州仓、扬州仓、慈溪仓、嘉兴仓、青岛仓、淮安仓、上海仓、金丽衢仓、莆田仓、杭州仓、上海六仓、济南仓',
    service_area STRING COMMENT '服务区域，枚举值包括：华东、华南、华中、广西、福建、华西、昆明、贵阳、华北',
    area_type STRING COMMENT '城配仓类型，枚举值：内区、外区',
    path_cnt BIGINT COMMENT '线路数量',
    point_cnt BIGINT COMMENT '当前点位数',
    km_cnt DECIMAL(38,18) COMMENT '配送总公里数',
    delivery_amt DECIMAL(38,18) COMMENT '配送总费用',
    point_cnt_b1d BIGINT COMMENT '昨日点位数',
    total_amt_b1d DECIMAL(38,18) COMMENT '昨日总成本',
    point_cnt_b1w BIGINT COMMENT '上周点位数',
    total_amt_b1w DECIMAL(38,18) COMMENT '上周总成本',
    delivery_total_amt DECIMAL(38,18) COMMENT '配送GVM（总交易额）'
)
COMMENT '城配仓维度配送成本表，按城配仓统计配送相关的成本、线路、点位和公里数等指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='城配仓维度配送成本表，包含各城配仓的配送成本、线路数、点位数、公里数等关键指标',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```