CREATE TABLE IF NOT EXISTS app_crm_wecom_conversation_detail_di(
	conversation_group STRING COMMENT '对话分组ID，用于唯一标识不同的会话组，格式为BD企业微信ID-客户ID组合',
	conversation_list STRING COMMENT '对话消息列表，包含该会话中的所有消息记录，格式为JSON数组字符串',
	cust_id STRING COMMENT '客户ID，用于唯一标识客户。优先取merchant.m_id，如果匹配不到则取用户的微信ID',
	cust_name STRING COMMENT '客户名称，记录客户的姓名。优先取merchant.mname，如果匹配不到则取用户的微信ID',
	conversation_start_time STRING COMMENT '对话开始时间，记录会话的第一条消息时间，格式：yyyy-MM-dd HH:mm:ss',
	conversation_end_time STRING COMMENT '对话结束时间，记录会话的最后一条消息时间，格式：yyyy-MM-dd HH:mm:ss',
	bd_wecom_id STRING COMMENT 'BD的企业微信ID，用于标识负责该会话的商务开发人员',
	create_time STRING COMMENT '记录创建时间，用于跟踪数据在系统中的生成时间，格式：yyyy-MM-dd HH:mm:ss'
)
PARTITIONED BY (
	ds STRING NOT NULL COMMENT '数据分区字段，表示数据所属的业务日期，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='企业微信会话明细表，记录BD与客户在企业微信中的完整对话内容和时间信息')
LIFECYCLE 1000;