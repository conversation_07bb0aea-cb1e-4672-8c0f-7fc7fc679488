```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_store_record_day_summary_di`(
	`day_tag` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
	`warehouse_no` BIGINT COMMENT '库存仓ID，唯一标识仓库',
	`warehouse_name` STRING COMMENT '仓库名称，枚举值包括：杭州总仓、上海总仓、嘉兴总仓、测试总仓仓、广州总仓、华西总仓、重庆总仓、福州总仓、长沙总仓、南宁总仓、昆明总仓、苏州总仓、青岛总仓、福州临时仓、东莞总仓、贝塔余杭仓A、jj上海自营仓、saas余杭仓库、贝塔历险记、总仓、广州总部仓、虚拟仓库、西藏、香港、东莞冷冻总仓、广东省内虚拟仓库、嘉兴海盐总仓、南京总仓、郑州自营仓、自建仓库、武汉总仓、虚拟仓-设备、SIMPLE CUPS仓库、上海莲谷仓、红塔仓库、品牌自营仓、普冷武汉仓、普冷长沙仓、绝配北京仓、北京总仓、汕头仓、普冷南京仓、贵阳总仓、ljj杭州自营仓、测试123、河南商丘、唐鹏测试仓库007、椿风原料加工厂-清芳溪、深圳FAC1、合肥市本华农业科技有限公司等',
	`warehouse_provider` STRING COMMENT '仓库服务商，枚举值包括：杭州鲜沐科技有限公司、杭州五二兰餐饮管理有限公司、广州肆捌城餐饮管理有限公司、上海文雷餐饮有限公司、客思服（杭州）科技有限公司等',
	`pd_id` BIGINT COMMENT '货品编码，唯一标识货品',
	`sku` STRING COMMENT 'sku编号，唯一标识库存单位，取值范围广泛',
	`saas_sku_id` BIGINT COMMENT 'saas skuId，SaaS系统中的SKU标识',
	`category_id` BIGINT COMMENT '类目id，商品分类标识',
	`sku_tenant_id` BIGINT COMMENT 'sku租户id，SKU所属租户标识',
	`warehouse_tenant_id` BIGINT COMMENT '仓库租户id，仓库所属租户标识',
	`opening_quantity` BIGINT COMMENT '期初库存，当天开始时的库存数量',
	`opening_amount` DECIMAL(38,18) COMMENT '期初金额，当天开始时的库存金额',
	`ending_quantity` BIGINT COMMENT '期末库存，当天结束时的库存数量',
	`ending_amount` DECIMAL(38,18) COMMENT '期末金额，当天结束时的库存金额',
	`allocation_in_quantity` BIGINT COMMENT '调拨入库数量，通过调拨方式入库的数量',
	`allocation_in_amount` DECIMAL(38,18) COMMENT '调拨入库金额，通过调拨方式入库的金额',
	`purchase_in_quantity` BIGINT COMMENT '采购入库数量，通过采购方式入库的数量',
	`purchase_in_amount` DECIMAL(38,18) COMMENT '采购入库金额，通过采购方式入库的金额',
	`after_sale_in_quantity` BIGINT COMMENT '退货入库数量，售后退货入库的数量',
	`after_sale_in_amount` DECIMAL(38,18) COMMENT '退货入库金额，售后退货入库的金额',
	`stock_taking_in_quantity` BIGINT COMMENT '盘盈入库数量，盘点发现的盘盈入库数量',
	`stock_taking_in_amount` DECIMAL(38,18) COMMENT '盘盈入库金额，盘点发现的盘盈入库金额',
	`transfer_in_quantity` BIGINT COMMENT '转换入库数量，通过转换方式入库的数量',
	`transfer_in_amount` DECIMAL(38,18) COMMENT '转换入库金额，通过转换方式入库的金额',
	`allocation_abnormal_in_quantity` BIGINT COMMENT '调拨回库数量，调拨异常回库的数量',
	`allocation_abnormal_in_amount` DECIMAL(38,18) COMMENT '调拨回库金额，调拨异常回库的金额',
	`other_in_quantity` BIGINT COMMENT '其他入库数量，其他方式入库的数量',
	`other_in_amount` DECIMAL(38,18) COMMENT '其他入库金额，其他方式入库的金额',
	`in_quantity` BIGINT COMMENT '入库合计数量，所有入库方式的数量总和',
	`in_amount` DECIMAL(38,18) COMMENT '入库合计金额，所有入库方式的金额总和',
	`allocation_out_quantity` BIGINT COMMENT '调拨出库数量，通过调拨方式出库的数量',
	`allocation_out_amount` DECIMAL(38,18) COMMENT '调拨出库金额，通过调拨方式出库的金额',
	`sale_out_quantity` BIGINT COMMENT '销售出库数量，通过销售方式出库的数量',
	`sale_out_amount` DECIMAL(38,18) COMMENT '销售出库金额，通过销售方式出库的金额',
	`damage_out_quantity` BIGINT COMMENT '货损出库数量，因货物损坏而出库的数量',
	`damage_out_amount` DECIMAL(38,18) COMMENT '货损出库金额，因货物损坏而出库的金额',
	`stock_taking_out_quantity` BIGINT COMMENT '盘亏出库数量，盘点发现的盘亏出库数量',
	`stock_taking_out_amount` DECIMAL(38,18) COMMENT '盘亏出库金额，盘点发现的盘亏出库金额',
	`transfer_out_quantity` BIGINT COMMENT '转换出库数量，通过转换方式出库的数量',
	`transfer_out_amount` DECIMAL(38,18) COMMENT '转换出库金额，通过转换方式出库的金额',
	`purchase_back_out_quantity` BIGINT COMMENT '采购退货出库数量，采购退货出库的数量',
	`purchase_back_out_amount` DECIMAL(38,18) COMMENT '采购退货出库金额，采购退货出库的金额',
	`supply_again_out_quantity` BIGINT COMMENT '补货出库数量，补货出库的数量',
	`supply_again_out_amount` DECIMAL(38,18) COMMENT '补货出库金额，补货出库的金额',
	`own_self_out_quantity` BIGINT COMMENT '自提销售出库数量，自提销售出库的数量',
	`own_self_out_amount` DECIMAL(38,18) COMMENT '自提销售出库金额，自提销售出库的金额',
	`other_out_quantity` BIGINT COMMENT '其他出库数量，其他方式出库的数量',
	`other_out_amount` DECIMAL(38,18) COMMENT '其他出库金额，其他方式出库的金额',
	`out_quantity` BIGINT COMMENT '出库合计数量，所有出库方式的数量总和',
	`out_amount` DECIMAL(38,18) COMMENT '出库合计金额，所有出库方式的金额总和',
	`init_in_quantity` BIGINT COMMENT '期初入库数量，期初入库的数量',
	`init_in_amount` DECIMAL(38,18) COMMENT '期初入库金额，期初入库的金额',
	`lack_in_quantity` BIGINT COMMENT '缺货入库数量，缺货入库的数量',
	`lack_in_amount` DECIMAL(38,18) COMMENT '缺货入库金额，缺货入库的金额',
	`intercept_in_quantity` BIGINT COMMENT '拦截入库数量，拦截入库的数量',
	`intercept_in_amount` DECIMAL(38,18) COMMENT '拦截入库金额，拦截入库的金额',
	`out_more_in_quantity` BIGINT COMMENT '多出入库数量，多出入库的数量',
	`out_more_in_amount` DECIMAL(38,18) COMMENT '多出入库金额，多出入库的金额',
	`allocation_damage_out_quantity` BIGINT COMMENT '调拨货损出库数量，调拨过程中货损出库的数量',
	`allocation_damage_out_amount` DECIMAL(38,18) COMMENT '调拨货损出库金额，调拨过程中货损出库的金额'
) 
COMMENT '出入库汇总天表（含自营仓），按天汇总的出入库明细数据，包含自营仓数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='出入库汇总天表，记录每日各类出入库操作的汇总统计信息') 
LIFECYCLE 30;
```