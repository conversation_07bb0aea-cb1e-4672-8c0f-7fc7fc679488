```sql
CREATE TABLE IF NOT EXISTS app_crm_team_bd_mi(
    `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
    `bd_id` BIGINT COMMENT '销售ID，唯一标识一个销售人员',
    `bd_name` STRING COMMENT '销售姓名',
    `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元）',
    `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元）',
    `order_brand_cnt` BIGINT COMMENT '交易公司数量',
    `order_cust_cnt` BIGINT COMMENT '交易门店数量',
    `order_brandalias_cnt` BIGINT COMMENT '交易品牌数量',
    `order_order_cnt` BIGINT COMMENT '交易订单数量',
    `new_cust_cnt` BIGINT COMMENT '活跃门店数中新增门店数量',
    `new_cust_gmv` DECIMAL(38,18) COMMENT '新增活跃门店GMV（元）',
    `close_cust_cnt` BIGINT COMMENT '活跃门店数中倒闭门店数量',
    `close_cust_gmv` DECIMAL(38,18) COMMENT '倒闭门店GMV（元）',
    `old_cust_cnt` BIGINT COMMENT '老活跃门店数量',
    `old_cust_gmv` DECIMAL(38,18) COMMENT '老活跃门店GMV（元）',
    `new_noactive_cust_cnt` BIGINT COMMENT '拉新门店数量（仅注册未下单）',
    `new_active_cust_cnt` BIGINT COMMENT '拉新门店数量（注册且下单）',
    `new_active_gmv` DECIMAL(38,18) COMMENT '拉新门店GMV（元）',
    `order_replace_cust_cnt` BIGINT COMMENT '代下单门店数量',
    `order_replace_gmv` DECIMAL(38,18) COMMENT '代下单实付金额（元）',
    `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
    `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
    `cost_amt` DECIMAL(38,18) COMMENT '履约成本（元）',
    `order_cnt` BIGINT COMMENT '履约订单数量',
    `sku_cnt` BIGINT COMMENT '总配送件数',
    `point_cnt` BIGINT COMMENT '总点位数',
    `cust_cnt` BIGINT COMMENT '履约门店数量',
    `brand_cnt` BIGINT COMMENT '履约公司数量',
    `brandalias_cnt` BIGINT COMMENT '履约品牌数量',
    `self_real_total_amt` DECIMAL(38,18) COMMENT '自营实付总金额（元）',
    `self_cost_amt` DECIMAL(38,18) COMMENT '自营成本（元）',
    `self_cust_cnt` BIGINT COMMENT '自营品牌门店数量',
    `self_brand_cnt` BIGINT COMMENT '自营品牌公司数量',
    `self_brandalias_cnt` BIGINT COMMENT '自营品牌品牌数量',
    `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实付总金额（元）',
    `timing_cost_amt` DECIMAL(38,18) COMMENT '省心送成本（元）',
    `timing_cust_cnt` BIGINT COMMENT '省心送门店数量',
    `timing_brand_cnt` BIGINT COMMENT '省心送公司数量',
    `timing_brandalias_cnt` BIGINT COMMENT '省心送品牌数量',
    `fruit_real_total_amt` DECIMAL(38,18) COMMENT '鲜果实付总金额（元）',
    `fruit_cost_amt` DECIMAL(38,18) COMMENT '鲜果成本（元）',
    `fruit_cash_real_total_amt` DECIMAL(38,18) COMMENT '鲜果账期实付金额（元）',
    `fruit_cust_cnt` BIGINT COMMENT '鲜果门店数量',
    `dairy_real_total_amt` DECIMAL(38,18) COMMENT '乳制品实付总金额（元）',
    `dairy_cost_amt` DECIMAL(38,18) COMMENT '乳制品成本（元）',
    `dairy_cash_real_cost_amt` DECIMAL(38,18) COMMENT '乳制品账期实付金额（元）',
    `dairy_cust_cnt` BIGINT COMMENT '乳制品门店数量',
    `nodairy_real_total_amt` DECIMAL(38,18) COMMENT '非乳制品实付总金额（元）',
    `nodairy_cost_amt` DECIMAL(38,18) COMMENT '非乳制品成本（元）',
    `nodairy_cash_cost_amt` DECIMAL(38,18) COMMENT '非乳制品账期实付金额（元）',
    `nodairy_cust_cnt` BIGINT COMMENT '非乳制品门店数量',
    `cash_real_total_amt` DECIMAL(38,18) COMMENT '账期实付金额（元）',
    `nocash_real_total_amt` DECIMAL(38,18) COMMENT '非账期实付金额（元）',
    `replace_origin_total_amt` DECIMAL(38,18) COMMENT '代仓应付总金额（元）',
    `replace_real_total_amt` DECIMAL(38,18) COMMENT '代仓实付总金额（元）',
    `replace_cust_cnt` BIGINT COMMENT '代仓门店数量',
    `self_after_sale_amt` DECIMAL(38,18) COMMENT '自营售后金额（元）',
    `replace_after_sale_amt` DECIMAL(38,18) COMMENT '代仓售后金额（元）'
) 
COMMENT '大客户团队BD粒度汇总表，按BD维度统计交易、履约、自营、省心送、鲜果、乳制品、非乳制品等各业务线的关键指标数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，如20250922表示2025年9月22日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='大客户团队BD粒度业务数据汇总表',
    'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```