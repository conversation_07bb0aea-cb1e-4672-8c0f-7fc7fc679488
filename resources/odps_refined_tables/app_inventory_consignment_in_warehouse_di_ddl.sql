CREATE TABLE IF NOT EXISTS app_inventory_consignment_in_warehouse_di(
	`supplier_id` BIGINT COMMENT '供应商ID，唯一标识供应商',
	`supplier_name` STRING COMMENT '供应商名称',
	`warehouse_no` BIGINT COMMENT '仓库编号，唯一标识仓库',
	`warehouse_name` STRING COMMENT '仓库名称',
	`batch_no` STRING COMMENT '批次号，标识商品批次',
	`sku` STRING COMMENT 'SKU编码，唯一标识商品',
	`sku_desc` STRING COMMENT '商品描述，包含规格、包装等信息',
	`spu_name` STRING COMMENT 'SPU名称，标识商品品类',
	`stock_quantity` BIGINT COMMENT '库存数量，可为负数表示异常库存',
	`stock_amount` DECIMAL(38,18) COMMENT '库存金额，保留18位小数精度',
	`stock_type` BIGINT COMMENT '库存类型：0-正常库存，1-异常库存',
	`production_date` DATETIME COMMENT '生产日期，格式为年月日时分秒',
	`quality_date` DATETIME COMMENT '保质期截止日期，格式为年月日时分秒',
	`date_flag` STRING COMMENT '日期标识，格式为yyyyMMdd',
	`custom_sku` STRING COMMENT '供应商自有SKU编码',
	`sub_type` BIGINT COMMENT '库存子类型：1-自营代销不入仓，2-自营代销入仓，3-自营经销，4-代仓代仓'
)
COMMENT '代销入仓库存表，记录代销商品的入库库存信息，包含库存数量、金额、生产日期、保质期等详细信息'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = '代销入仓库存明细表',
	'columnar.nested.type' = 'true'
)
LIFECYCLE 30;