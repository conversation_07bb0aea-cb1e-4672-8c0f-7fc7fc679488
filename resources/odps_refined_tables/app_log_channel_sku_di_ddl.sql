CREATE TABLE IF NOT EXISTS app_log_channel_sku_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `fsku_id` STRING COMMENT 'SKU编号，商品最小库存单位的唯一标识',
    `category1` STRING COMMENT '一级类目，商品分类的最高层级，取值范围：乳制品、其他、鲜果等',
    `category2` STRING COMMENT '二级类目，商品分类的第二层级，取值范围：乳制品、饮料、饼干丨糖果丨可可豆制品、坚果制品、糖丨糖制品、新鲜水果等',
    `category3` STRING COMMENT '三级类目，商品分类的第三层级，取值范围：黄油、植物蛋白饮料、饼干、烘炒类、代糖｜甜味剂、柑果类等',
    `category4` STRING COMMENT '四级类目，商品分类的最细粒度层级，取值范围：乳酸黄油、其他植物蛋白饮料、饼干碎、坚果、固体甜味剂、金桔等',
    `sku_type` STRING COMMENT 'SKU类型，表示商品经营模式，取值范围：0-自营、1-代仓、2-代售',
    `spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
    `sku_disc` STRING COMMENT '商品描述，包含规格、包装等详细信息',
    `model` STRING COMMENT '渠道，流量来源渠道，取值范围：banner、分类、搜索',
    `uv` BIGINT COMMENT '曝光UV，独立访客数，表示看到该商品的独立用户数量',
    `pv` BIGINT COMMENT '曝光PV，页面浏览量，表示商品被展示的总次数',
    `click_uv` BIGINT COMMENT '点击UV，独立点击用户数，表示点击该商品的独立用户数量',
    `click_pv` BIGINT COMMENT '点击PV，点击量，表示商品被点击的总次数',
    `addbug_uv` BIGINT COMMENT '加购UV，独立加购用户数，表示将商品加入购物车的独立用户数量',
    `addbug_pv` BIGINT COMMENT '加购PV，加购量，表示商品被加入购物车的总次数',
    `cust_cnt` BIGINT COMMENT '交易人数，完成购买交易的独立用户数量'
)
COMMENT '流量分渠道SKU转化表，记录各渠道下SKU级别的流量转化数据，包括曝光、点击、加购和交易等指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('comment' = '流量分渠道SKU转化分析表，用于分析各渠道下商品级别的流量转化效果')
LIFECYCLE 30;