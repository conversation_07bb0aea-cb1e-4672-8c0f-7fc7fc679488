CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_pop_purchaser_supplier_final_di` (
  `supplier_name` STRING COMMENT '供应商名称，枚举值包括：刘东、李强、杨志闯、汪高洋、龙飞果业等',
  `purchaser` STRING COMMENT '采购员姓名，枚举值包括：何泽震、刘东、王晨耀、李强、杨志闯、汪高祥、张冲等',
  `sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
  `sku_disc` STRING COMMENT '商品描述，包含规格、等级、果径等详细信息',
  `spu_name` STRING COMMENT '商品名称，枚举值包括：云南蓝莓、东北蓝莓、泰国红心青柚、蓝标泰国龙眼、金标泰国龙眼等',
  `purchase_quantity` BIGINT COMMENT '应结数量，取值范围：0-21',
  `purchase_amount` DECIMAL(38,18) COMMENT '应结金额，保留18位小数精度',
  `back_quantity` BIGINT COMMENT '退供应扣数量，取值范围：0',
  `back_amount` DECIMAL(38,18) COMMENT '退供应扣金额，保留18位小数精度',
  `quality_amount` DECIMAL(38,18) COMMENT '质量问题应扣金额，保留18位小数精度',
  `settlement_amount` DECIMAL(38,18) COMMENT '实结金额：应结金额-退供应扣金额-质量问题应扣金额，保留18位小数精度'
)
COMMENT 'POP鲜沐应结数据表，记录供应商与采购员之间的商品结算信息，包括应结数量、金额、退货扣款和质量问题扣款等'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'POP鲜沐应结数据表，用于存储供应商结算的详细数据',
  'last_data_modified_time' = '2025-09-23 02:27:19'
)
LIFECYCLE 30;