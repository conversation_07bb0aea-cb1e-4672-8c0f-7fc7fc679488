CREATE TABLE IF NOT EXISTS app_self_delivery_warehouse_kpi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额，单位：元',
	`real_total_amt` DECIMAL(38,18) COMMENT '实际总金额，单位：元',
	`cost_amt` DECIMAL(38,18) COMMENT '成本金额，单位：元',
	`timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额，单位：元',
	`timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实际总金额，单位：元',
	`cust_cnt` BIGINT COMMENT '客户数量，统计当日的客户总数',
	`order_cnt` BIGINT COMMENT '订单数量，统计当日的订单总数',
	`point_cnt` BIGINT COMMENT '点位数，统计当日的配送点位总数',
	`day_point_cnt` BIGINT COMMENT '日均点位数，统计周期内的平均配送点位数量',
	`sku_cnt` BIGINT COMMENT 'SKU数量，统计当日的商品SKU总数',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费金额，单位：元',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，单位：元',
	`inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额，单位：元',
	`inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额，单位：元',
	`damage_amt` DECIMAL(38,18) COMMENT '货损总金额，单位：元',
	`damage_rate` DECIMAL(38,18) COMMENT '货损占比，货损金额占总金额的比例',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本，单位：元',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，单位：元',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本，单位：元',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本，单位：元',
	`other_amt` DECIMAL(38,18) COMMENT '其他成本，单位：元',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，单位：元',
	`heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶仓储成本，单位：元',
	`heytea_arterial_roads_amt` DECIMAL(38,18) COMMENT '喜茶调拨成本，单位：元',
	`heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送成本，单位：元',
	`heytea_way_deliver_amt` DECIMAL(38,18) COMMENT '喜茶专配成本，单位：元',
	`saas_point_cnt` BIGINT COMMENT 'SaaS点位数，使用SaaS系统的配送点位数量',
	`sample_point_cnt` BIGINT COMMENT '出样点位数，用于展示样品的配送点位数量',
	`after_sale_point_cnt` BIGINT COMMENT '补货点位数，用于售后补货的配送点位数量',
	`wholesale_point_cnt` BIGINT COMMENT '批发客户点位数，面向批发客户的配送点位数量',
	`heytea_point_cnt` BIGINT COMMENT '喜茶共配点位数，喜茶共同配送的点位数量',
	`heytea_way_point_cnt` BIGINT COMMENT '喜茶专配点位数，喜茶专属配送的点位数量',
	`delivery_out_times_amt` DECIMAL(38,18) COMMENT '运费+超时加单费，单位：元',
	`deliver_coupon_amt` DECIMAL(38,18) COMMENT '优惠券费用，单位：元',
	`total_point_cnt` BIGINT COMMENT '总配送点位，所有配送点位的总数',
	`out_times_amt` DECIMAL(38,18) COMMENT '超时加单费，单位：元',
	`precision_delivery_fee` DECIMAL(38,18) COMMENT '精准送费用，单位：元'
) 
COMMENT '履约口径KPI指标日汇总表(自营)，包含自营业务的各项履约相关KPI指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的统计日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='自营业务履约KPI指标日汇总表，用于统计和分析自营业务的各项履约相关指标') 
LIFECYCLE 30;