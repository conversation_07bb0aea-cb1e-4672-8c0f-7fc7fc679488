```sql
CREATE TABLE IF NOT EXISTS app_crm_team_bd_spu_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
    `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户',
    `brand_alias` STRING COMMENT '品牌别称，如：无、司乎、乐乐茶、七分甜、茉莉奶白等',
    `register_province` STRING COMMENT '注册时省份，如：浙江、湖南、四川、江苏、山东等',
    `register_city` STRING COMMENT '注册时城市，如：温州市、台州市、长沙市、杭州市等',
    `brand_id` BIGINT COMMENT '公司ID，唯一标识品牌公司',
    `brand_name` STRING COMMENT '公司名称，如：台州乐欧酵室餐饮企业管理有限公司、上海吉茶餐饮管理有限公司等',
    `bd_id` BIGINT COMMENT '销售ID，唯一标识销售人员',
    `bd_name` STRING COMMENT '销售姓名，如：彭强、刘莹莹、吴灿东等',
    `spu_no` STRING COMMENT 'SPU编号，商品唯一标识',
    `spu_name` STRING COMMENT '商品名称，如：酷盖纯牛奶、澄善葡萄罐头等',
    `sku_spec` STRING COMMENT '商品规格描述，如：1L*12盒、850g*12罐等',
    `sku_brand` STRING COMMENT '商品品牌，如：酷盖、澄善、妞丝达特等',
    `sku_variety` STRING COMMENT '商品品种，如：小茶点、半熟芝士、慕斯蛋糕、默认等',
    `category_1` STRING COMMENT '一级类目，枚举值：乳制品、其他、鲜果',
    `category_2` STRING COMMENT '二级类目，如：乳制品、水果制品、糕点丨面包等',
    `category_3` STRING COMMENT '三级类目，如：液体乳、罐头、冷冻面团等',
    `category_4` STRING COMMENT '四级类目，如：常温牛奶、水果罐头、熟制冷冻面团等',
    `sku_type` BIGINT COMMENT '商品类型，枚举值：0-自营、1-代仓、2-其他',
    `is_self_owned_brand` BIGINT COMMENT '是否自营品牌，枚举值：1-是、0-否',
    `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，精确到小数点后18位',
    `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，精确到小数点后18位',
    `cost_amt` DECIMAL(38,18) COMMENT '履约成本金额，精确到小数点后18位',
    `sku_cnt` BIGINT COMMENT '总配送件数，统计商品配送数量',
    `cust_cnt` BIGINT COMMENT '履约门店数，统计服务门店数量',
    `after_sale_amt` DECIMAL(38,18) COMMENT '售后金额，精确到小数点后18位'
)
COMMENT '大客户团队商品SPU与客户名称与BD结合维度汇总表，用于分析大客户团队的销售商品数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期分区'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='大客户团队商品销售数据汇总表，包含商品SPU信息、客户信息、BD信息和销售数据',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```