CREATE TABLE IF NOT EXISTS app_search_related_queries_df(
	query STRING COMMENT '搜索词，如：芒果、牛奶、奶油等，取值范围包括常见食品原料和商品名称',
	related_queries STRING COMMENT '搜索词联想结果，包含与主搜索词相关的10个推荐词，以逗号分隔',
	top_skus STRING COMMENT '热门商品SKU列表，JSON格式，包含sku编号、商品名称和权重信息',
	query_frequency BIGINT COMMENT '搜索频率，表示该搜索词在统计周期内的搜索次数，取值范围从35到771135'
)
COMMENT '搜索词联想结果表，包含用户搜索词的联想推荐和相关商品信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，数据日期，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='搜索词联想数据分析表，用于推荐系统和搜索优化') 
LIFECYCLE 60;