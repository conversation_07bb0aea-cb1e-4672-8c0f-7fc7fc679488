CREATE TABLE IF NOT EXISTS app_saas_replenishment_suggestion_infomation_df(
	warehouse_no BIGINT COMMENT '仓库编号，数值型标识',
	warehouse_name STRING COMMENT '仓库名称，枚举类型取值范围：上海总仓、嘉兴总仓、华西总仓、福州总仓、长沙总仓、青岛总仓、东莞总仓、总仓、广州总部仓、虚拟仓库、东莞冷冻总仓、嘉兴海盐总仓、南京总仓、自建仓库、上海莲谷仓、红塔仓库、品牌自营仓、普冷武汉仓、普冷长沙仓、绝配北京仓、北京总仓、汕头仓、广东省内虚拟仓库、武汉总仓、公司自营仓、贝塔余杭仓A等',
	sku STRING COMMENT 'SKU编码，商品唯一标识，字符串类型',
	saas_sku_id BIGINT COMMENT 'SaaS系统SKU ID，数值型标识',
	tenant_id BIGINT COMMENT '租户ID，数值型标识',
	category_id BIGINT COMMENT '货品类目ID，数值型标识',
	stock_level_minimum BIGINT COMMENT '安全库存下限，数值型',
	stock_level_maximum BIGINT COMMENT '安全库存上限，数值型',
	transfer_order_in_quantity BIGINT COMMENT '调拨订单在途数量，数值型',
	on_way_order_quantity BIGINT COMMENT '采购订单在途数量，数值型',
	enabled_quantity BIGINT COMMENT '可用库存数量，数值型',
	past_seven_day_sales_quantity BIGINT COMMENT '近7天销量，数值型',
	past_fifteen_day_sales_quantity BIGINT COMMENT '近15天销量，数值型',
	past_thirty_day_sales_quantity BIGINT COMMENT '近30天销量，数值型',
	replenishment_advice_flag BIGINT COMMENT '补货建议标识，枚举类型：0-不建议补货，1-建议补货',
	replenishment_advice_quantity BIGINT COMMENT '建议补货数量，数值型'
) 
COMMENT '补货建议信息表，包含仓库库存、销量数据和补货建议信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='补货建议信息表，提供基于库存和销售数据的智能补货建议') 
LIFECYCLE 30;