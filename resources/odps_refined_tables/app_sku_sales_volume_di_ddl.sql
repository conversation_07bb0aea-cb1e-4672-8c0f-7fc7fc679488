CREATE TABLE IF NOT EXISTS app_sku_sales_volume_di(
	sku STRING COMMENT 'SKU编码，商品唯一标识',
	warehouse_no BIGINT COMMENT '库存仓编号，取值范围：2-155',
	sales_volume BIGINT COMMENT 'SKU近14天日均出库量，取值范围：1-659',
	date_flag STRING COMMENT '数据日期标识，格式：yyyyMMdd'
) 
COMMENT 'SKU近14天日均出库量表，统计各SKU在各仓库的日均出库情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SKU近14天日均出库量统计表，用于分析商品销售趋势和库存管理') 
LIFECYCLE 30;