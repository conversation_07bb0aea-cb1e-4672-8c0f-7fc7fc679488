CREATE TABLE IF NOT EXISTS app_spu_delivery_selfowend_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
    `province` STRING COMMENT '省份名称',
    `admin_city` STRING COMMENT '城市名称',
    `area` STRING COMMENT '区县名称',
    `spu_id` BIGINT COMMENT '商品ID（SPU ID）',
    `spu_name` STRING COMMENT '商品名称',
    `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,水果/果切/榨汁店,其他',
    `brand_type` STRING COMMENT '大客户类型；枚举值：大客户,普通,批发客户',
    `brand_name` STRING COMMENT '品牌名称',
    `category1` STRING COMMENT '一级类目',
    `category2_id` STRING COMMENT '二级类目ID',
    `category2` STRING COMMENT '二级类目名称',
    `category3_id` STRING COMMENT '三级类目ID',
    `category3` STRING COMMENT '三级类目名称',
    `category4_id` STRING COMMENT '四级类目ID',
    `category4` STRING COMMENT '四级类目名称',
    `origin_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额（原始金额）',
    `real_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额（实际金额）',
    `cost_amt` DECIMAL(38,18) COMMENT '总成本金额',
    `origin_pay_margin` DECIMAL(38,18) COMMENT '应付毛利润（原始）',
    `real_pay_margin` DECIMAL(38,18) COMMENT '实付毛利润（实际）',
    `preferential_amt` DECIMAL(38,18) COMMENT '营销优惠金额',
    `after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
    `deliver_total_amt` DECIMAL(38,18) COMMENT '配送GMV（商品交易总额）'
)
COMMENT '城市整体配送数据日表，记录各城市商品配送的详细财务和业务数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '城市整体配送数据日表，包含商品配送的财务指标、客户类型、品牌信息和类目层级关系'
)
LIFECYCLE 30;