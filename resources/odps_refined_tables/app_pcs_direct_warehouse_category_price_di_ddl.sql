CREATE TABLE IF NOT EXISTS app_pcs_direct_warehouse_category_price_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据所属的业务日期',
    `warehouse_no` BIGINT COMMENT '库存仓号，取值范围：10-155，常见值：10(嘉兴总仓)、69(东莞总仓)、155(武汉总仓)、38(福州总仓)',
    `warehouse_name` STRING COMMENT '库存仓名称，枚举值：嘉兴总仓、东莞总仓、武汉总仓、福州总仓、南京总仓',
    `category4` STRING COMMENT '四级类目，枚举值：柠檬、芒果、柑、菠萝蜜、水果番茄丨圣女果｜西红柿、芭乐、草莓、西瓜、甘蓝、金桔',
    `purchase_no` STRING COMMENT '采购单号，格式为年月日+数字序列，如202509220797973002',
    `sku_id` STRING COMMENT 'SKU编号，商品唯一标识，包含数字和字母组合',
    `spu_name` STRING COMMENT '商品名称，枚举值：广东粗皮香水柠檬、红凯特芒、越南大青芒、越南金煌芒、广东青柑、越南红肉菠萝蜜、青凯特芒、芒果（三级果）、海南子弹头香水柠檬、山东黄千禧、广东红心芭乐、蒙特瑞草莓、美都无籽西瓜、羽衣甘蓝、海南小金桔、无籽青柠檬',
    `sku_disc` STRING COMMENT '商品描述，包含净重、等级、规格、特殊说明等信息',
    `purchase_sku_cnt` BIGINT COMMENT '采购数量，取值范围：1-610，平均值为96.44',
    `purchase_amt` DECIMAL(38,18) COMMENT '采购金额，单位为元，保留18位小数精度',
    `direct_avg_price` DECIMAL(38,18) COMMENT '直采成本单价，单位为元/单位，保留18位小数精度',
    `purchase_avg_price` DECIMAL(38,18) COMMENT '直采成本单价，单位为元/单位，保留18位小数精度'
)
COMMENT '直采采购数据明细表，记录各仓库四级类目商品的采购明细数据，包括采购数量、金额和成本单价等信息'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '直采采购数据明细表',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;