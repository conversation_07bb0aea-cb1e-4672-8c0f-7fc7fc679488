CREATE TABLE IF NOT EXISTS app_crm_wecom_user_summary_df(
	bd_id BIGINT COMMENT '销售ID，唯一标识一个销售人员',
	bd_name STRING COMMENT '销售人员姓名',
	private_sea_count BIGINT COMMENT '私海客户数量，即该销售专属的客户数量',
	effective_wecom_user_count BIGINT COMMENT '有效企微客户数量，指与企业微信建立有效联系的客户数',
	wecom_user_count BIGINT COMMENT '企微客户总数，包括所有已添加的企微客户',
	bd_delete_wecom_count BIGINT COMMENT '销售删除客户数量，指销售主动删除的企微客户数',
	user_delete_wecom_count BIGINT COMMENT '用户删除客户数量，指客户主动删除销售的企微客户数',
	delete_wecom_count BIGINT COMMENT '互删客户数量，指双方互相删除的企微客户数'
)
COMMENT 'CRM企微用户统计表，统计销售人员的企业微信客户相关数据，包括客户数量、删除情况等指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
	'comment'='CRM企微用户统计表，用于分析销售人员的企业微信客户管理情况',
	'columnar.nested.type'='true'
)
LIFECYCLE 30;