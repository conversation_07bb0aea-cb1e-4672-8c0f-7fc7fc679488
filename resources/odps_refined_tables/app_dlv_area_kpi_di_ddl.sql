CREATE TABLE IF NOT EXISTS app_dlv_area_kpi_di(
	`date` STRING COMMENT '日期，格式：yyyyMMdd，表示数据统计的日期',
	`area_name` STRING COMMENT '城配仓名称，枚举值包括：盐城仓、衡阳仓、淮安仓、慈溪仓、东莞仓、烟台仓、惠州仓、杭州仓、嘉兴仓、南京仓、合肥仓、贵阳仓、常德仓、上海仓、上海六仓、莆田仓、金丽衢仓、泉州仓、湖州仓、深圳仓、株洲仓、湖南快递履约虚拟仓、温州仓、南通仓、佛山仓、无、无锡仓、宜春仓、中山仓、福州仓、湛江茂名仓、济南仓、长沙仓、昆明仓、荆门仓、东莞快递履约仓、宁波仓、南昌仓、徐州仓、苏州仓、南宁仓、杭州三仓、武汉快递仓、成都仓、扬州仓、青岛仓、台州仓、重庆仓、秀洲仓、厦门仓等',
	`total_point_cnt` BIGINT COMMENT '总点位数，包含所有配送点的数量',
	`point_cnt` BIGINT COMMENT '点位数（不含喜茶），排除喜茶相关配送点的数量',
	`no_in_time_point_cnt` BIGINT COMMENT '不及时点位数（不含喜茶），未按时配送的点位数量',
	`delay_time_point_cnt_2` BIGINT COMMENT '前置2小时不及时点位数（不含喜茶），提前2小时仍未及时配送的点位数量',
	`out_time` DECIMAL(38,18) COMMENT '配送超时时间（不含喜茶），单位：小时，表示配送超时的总时长',
	`delay_time_2` DECIMAL(38,18) COMMENT '前置2小时配送超时时间（不含喜茶），单位：小时，表示提前2小时配送但仍超时的总时长',
	`path_cnt` BIGINT COMMENT '线路数（不含喜茶），配送线路的总数量',
	`delay_path_cnt` BIGINT COMMENT '出库不及时线路数（不含喜茶），出库不及时的配送线路数量',
	`out_distance_point_cnt` BIGINT COMMENT '超距离点位数（含喜茶），配送距离超标的点位数量，包含喜茶相关点位',
	`after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额，单位：元，所有售后相关的总金额',
	`after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责，单位：元，仓配责任导致的售后金额',
	`after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责，单位：元，采购责任导致的售后金额',
	`after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责，单位：元，品控责任导致的售后金额',
	`after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责，单位：元，采购和品控共同责任导致的售后金额',
	`after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责，单位：元，其他责任导致的售后金额',
	`delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额，单位：元，配送相关的总销售金额',
	`coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额，单位：元，使用的优惠券总金额',
	`sku_cnt` BIGINT COMMENT '配送件数，配送的商品总件数',
	`error_sku_cnt` BIGINT COMMENT '错误件数，配送错误的商品件数',
	`cust_cnt` BIGINT COMMENT '活跃客户数，活跃的客户数量',
	`error_cust_cnt` BIGINT COMMENT '错误客户数，信息错误或有问题的客户数量'
) 
COMMENT '仓配KPI配送数据汇总表，包含各城配仓的配送绩效指标、售后责任分摊金额等关键业务数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据所属的业务日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='仓配配送KPI数据汇总表，用于监控和分析各城配仓的配送效率、售后责任分摊等关键业务指标') 
LIFECYCLE 30;