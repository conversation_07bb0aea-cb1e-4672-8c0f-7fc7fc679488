CREATE TABLE IF NOT EXISTS app_follow_up_relation_release_detail_df(
    m_id BIGINT COMMENT '客户ID，唯一标识客户',
    release_rule STRING COMMENT '释放规则：1-30天未下单且15天未拜访，2-60天未下单，3-首单客户，4-公海转私海，不掉落客户-特殊标记客户',
    danger_day BIGINT COMMENT '释放倒计时天数，999表示不掉落客户',
    has_unfinished_delivery BIGINT COMMENT '是否有未履约订单：0-否，1-是',
    release_date DATETIME COMMENT '释放日期，格式为年月日时分秒，9999-12-31 00:00:00表示不掉落客户',
    cust_type STRING COMMENT '客户类型：高校-高校客户，非高校-非高校客户，不掉落客户-特殊标记客户',
    date_flag STRING COMMENT '日期标识，格式为yyyyMMdd'
)
COMMENT 'CRM客户释放公海规则和倒计时表，记录客户释放规则、倒计时天数、订单履约状态等信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment'='CRM客户释放公海规则和倒计时表，包含客户释放规则、倒计时、订单履约状态等详细信息')
LIFECYCLE 30;