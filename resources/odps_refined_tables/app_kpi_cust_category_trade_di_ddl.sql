```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_cust_category_trade_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `category` STRING COMMENT '品类，取值范围：鲜果、乳制品、其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，交易中应付的总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，交易中实际支付的总金额',
  `cust_cnt` BIGINT COMMENT '客户数，统计周期内的客户数量',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(平均每用户收入)，计算公式：应付总金额/客户数',
  `order_cnt` BIGINT COMMENT '订单数，统计周期内的订单数量',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价，计算公式：应付总金额/订单数',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额，未收到货的售后金额总和',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率，计算公式：未到货售后总金额/应付总金额',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额，直发采购模式的应付金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费，包含运费和超时加单费的总和',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额，省心送模式的应付金额'
) 
COMMENT '交易口径KPI指标日汇总表，按客户团队和品类维度统计的交易相关指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的业务日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('comment'='交易口径KPI指标日汇总表，包含按客户团队和品类维度统计的交易金额、客户数、订单数、ARPU、退货率等核心业务指标') 
LIFECYCLE 30;
```