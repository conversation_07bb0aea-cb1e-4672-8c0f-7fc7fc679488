```sql
CREATE TABLE IF NOT EXISTS app_saas_brand_sku_trade_mi(
    `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
    `brand_alias` STRING COMMENT '品牌名称或品牌别名',
    `title` STRING COMMENT '商品标题，描述商品的具体名称',
    `specification` STRING COMMENT '商品规格，描述商品的包装规格和单位',
    `category1` STRING COMMENT '后台一级类目，包括：无、水果制品、蔬菜制品、包材、成品原料、新鲜水果、食品添加剂、饼干丨糖果丨可可豆制品、乳制品、饮品原料、新鲜蔬菜、糖丨糖制品、茶制品、谷物制品、饮料、蛋丨蛋制品、其他、肉丨肉制品、调味品、糕点丨面包、帆台一级类目、食用油丨油脂及制品、坚果制品、测试一级类目T、海鲜｜水产品｜制品、电器、住宅家具、酒等',
    `sku_type` STRING COMMENT '商品类型，取值范围：商城下单、鲜沐自营、代仓、代售、客户自营',
    `total_gmv` DECIMAL(38,18) COMMENT '总交易GMV，商品总交易金额，保留18位小数精度',
    `sku_cnt` BIGINT COMMENT '商品销量，统计周期内的商品销售数量'
)
COMMENT 'SaaS品牌品类结构数据表，记录品牌商品的交易数据和品类结构信息'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SaaS品牌品类结构分析表，用于品牌商品交易数据的统计和分析',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```