CREATE TABLE IF NOT EXISTS app_kpi_category_trade_di(
	`date` STRING COMMENT '日期，格式：yyyyMMdd，表示年月日',
	`category` STRING COMMENT '品类，枚举值：鲜果、乳制品、其他',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，单位：元',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，单位：元',
	`cust_cnt` BIGINT COMMENT '客户数，去重后的客户数量',
	`cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值，计算公式：应付总金额/客户数，单位：元/客户',
	`order_cnt` BIGINT COMMENT '订单数，去重后的订单数量',
	`order_avg` DECIMAL(38,18) COMMENT '订单均价，计算公式：应付总金额/订单数，单位：元/订单',
	`after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额，单位：元',
	`after_sale_rate` DECIMAL(38,18) COMMENT '退货率，计算公式：未到货售后总金额/应付总金额，小数形式表示',
	`dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额，单位：元',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费，包含运费和超时加单费，单位：元',
	`timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额，单位：元'
) 
COMMENT '交易口径KPI指标日汇总表，按品类维度统计每日交易相关指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='交易口径KPI指标日汇总表，包含各品类的交易金额、客户数、订单数、ARPU、退货率等核心指标') 
LIFECYCLE 30;