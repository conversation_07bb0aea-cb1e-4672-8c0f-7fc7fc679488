```sql
CREATE TABLE IF NOT EXISTS app_crm_consign_order_detail_di(
    `order_date` STRING COMMENT '订单日期，格式：yyyyMMdd',
    `order_no` STRING COMMENT '订单编号，唯一标识一个订单',
    `cust_id` BIGINT COMMENT '客户ID，唯一标识一个客户',
    `cust_name` STRING COMMENT '客户名称，商家的店铺名称',
    `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、其他',
    `administrative_city` STRING COMMENT '商家对应的行政城市，如：温州市、绍兴市、深圳市等',
    `register_area` STRING COMMENT '注册时区，商家注册时所在的区域',
    `bd_id` BIGINT COMMENT '销售ID，-1表示无销售',
    `bd_name` STRING COMMENT '销售名称，无销售时显示"无"',
    `m1` STRING COMMENT '城市负责人（M1），无负责人时显示"无"',
    `m2` STRING COMMENT '区域负责人（M2），无负责人时显示"无"',
    `m3` STRING COMMENT '部门负责人（M3），无负责人时显示"无"',
    `sku_id` STRING COMMENT '商品SKU ID，唯一标识一个商品规格',
    `spu_name` STRING COMMENT '商品名称',
    `sku_spec` STRING COMMENT '商品规格描述，如：1KG*1袋、400g*1盒等',
    `sku_cnt` BIGINT COMMENT '商品数量',
    `origin_total_amt` DECIMAL(38,18) COMMENT '订单应付金额',
    `real_total_amt` DECIMAL(38,18) COMMENT '订单实付金额',
    `is_bd_visit` STRING COMMENT 'BD是否拜访；枚举值：是、否',
    `is_bd_drop_in_visit` STRING COMMENT 'BD是否上门拜访（上门/有效）；枚举值：是、否'
)
COMMENT '代售订单清单表(日)，记录每日的代售订单详细信息，包括客户信息、销售信息、商品信息和拜访情况'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '代售订单清单表(日)，用于存储和分析代售订单的详细数据',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```