```sql
CREATE TABLE IF NOT EXISTS app_merchant_sku_order_data_df(
    m_id BIGINT COMMENT '门店ID，唯一标识门店',
    sku STRING COMMENT 'SKU编码，商品唯一标识，取值范围：包含数字和字母组合的商品编码',
    last_order_time DATETIME COMMENT '最近一次下单时间，格式为年月日时分秒（YYYY-MM-DD HH:MM:SS）',
    last_order_quantity BIGINT COMMENT '最近一次下单数量，取值范围：1-200',
    last_thirty_days_order_count BIGINT COMMENT '最近30天下单次数，取值范围：0-49',
    last_sixty_days_order_count BIGINT COMMENT '最近60天下单次数，取值范围：0-57',
    last_two_years_order_count BIGINT COMMENT '最近2年下单次数，取值范围：1-480',
    delete_time_order_count BIGINT COMMENT '上次删除至今下单次数，取值范围：0-17',
    day_tag STRING COMMENT '数据同步日期，格式为年月日（YYYYMMDD）',
    last_order_real_unit_price DECIMAL COMMENT '最近一次下单单价，精确到小数点后两位'
)
COMMENT '采购助手-门店常购清单订单行为数据表，记录门店商品的订单行为统计信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为年月日（YYYYMMDD）')
STORED AS ALIORC
TBLPROPERTIES ('comment'='采购助手-门店常购清单订单行为数据，包含门店商品的订单时间、数量、频次等统计信息')
LIFECYCLE 30;
```