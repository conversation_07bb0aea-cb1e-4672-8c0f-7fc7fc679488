CREATE TABLE IF NOT EXISTS app_crm_wecom_bd_task_df(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
    `bd_id` BIGINT COMMENT '销售ID，唯一标识一个销售人员',
    `bd_name` STRING COMMENT '销售姓名',
    `m1_name` STRING COMMENT '城市负责人名称（M1），即销售的直接上级管理者',
    `m2_name` STRING COMMENT '区域负责人名称（M2），即M1的直接上级管理者',
    `m3_name` STRING COMMENT '部门负责人名称（M3），即M2的直接上级管理者',
    `region` STRING COMMENT '大区名称，取值范围包括：上海大区、福泉、川渝大区、华中大区、华南一区、苏皖大区、华南二区、浙江大区、山东大区、云贵桂大区、四川等',
    `message_id` STRING COMMENT '消息ID，唯一标识一条消息记录',
    `message_status` BIGINT COMMENT '消息状态：0-未发送，2-已发送',
    `send_count` BIGINT COMMENT '消息接收客户数，表示该消息发送给了多少个客户'
)
COMMENT '销售与客户沟通互动看板，记录销售人员与客户的沟通消息状态和统计信息'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '销售与客户沟通互动看板表',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;