CREATE TABLE IF NOT EXISTS app_cust_lifecycle_progress_mi(
	`month` STRING COMMENT '月份，格式为yyyyMM，表示上月月份',
	`cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
	`cust_type` STRING COMMENT '客户类型，枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、水果店、水果捞/果切店、社区生鲜店、其他',
	`register_province` STRING COMMENT '注册时省份',
	`register_city` STRING COMMENT '注册时城市',
	`life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举值：A1、A2、A3、B1、B2、L1、L2、L3、N0、N1、N2、S1、S2、W',
	`progress_type` STRING COMMENT '进度类型，枚举值：下1天、下2-3天、下4天及以上、未下单',
	`cust_cnt` BIGINT COMMENT '总客户数',
	`close_cust_cnt` BIGINT COMMENT '当月闭店客户数',
	`order_amt_avg_1_cust_cnt` BIGINT COMMENT '当月次均价0客户数',
	`order_amt_avg_2_cust_cnt` BIGINT COMMENT '当月次均价0-50客户数',
	`order_amt_avg_3_cust_cnt` BIGINT COMMENT '当月次均价50-200客户数',
	`order_amt_avg_4_cust_cnt` BIGINT COMMENT '当月次均价200-400客户数',
	`order_amt_avg_5_cust_cnt` BIGINT COMMENT '当月次均价400-600客户数',
	`order_amt_avg_6_cust_cnt` BIGINT COMMENT '当月次均价600+客户数',
	`fruit_amt` DECIMAL(38,18) COMMENT '当月鲜果实付GMV',
	`fruit_cust_cnt` BIGINT COMMENT '当月鲜果购买客户数',
	`dairy_amt` DECIMAL(38,18) COMMENT '当月乳品实付GMV',
	`dairy_cust_cnt` BIGINT COMMENT '当月乳品购买客户数',
	`other_amt` DECIMAL(38,18) COMMENT '当月其他实付GMV',
	`other_cust_cnt` BIGINT COMMENT '当月其他购买客户数',
	`self_amt` DECIMAL(38,18) COMMENT '当月自营品牌实付GMV',
	`self_cust_cnt` BIGINT COMMENT '当月自营品牌购买客户数',
	`spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均SPU数',
	`fruit_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均鲜果SPU数',
	`dairy_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均乳品SPU数',
	`other_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均标品SPU数',
	`lastmonth_order_amt_avg_1_cust_cnt` BIGINT COMMENT '上月次均价0客户数',
	`lastmonth_order_amt_avg_2_cust_cnt` BIGINT COMMENT '上月次均价0-50客户数',
	`lastmonth_order_amt_avg_3_cust_cnt` BIGINT COMMENT '上月次均价50-200客户数',
	`lastmonth_order_amt_avg_4_cust_cnt` BIGINT COMMENT '上月次均价200-400客户数',
	`lastmonth_order_amt_avg_5_cust_cnt` BIGINT COMMENT '上月次均价400-600客户数',
	`lastmonth_order_amt_avg_6_cust_cnt` BIGINT COMMENT '上月次均价600+客户数',
	`lastmonth_fruit_amt` DECIMAL(38,18) COMMENT '上月鲜果实付GMV',
	`lastmonth_fruit_cust_cnt` BIGINT COMMENT '上月鲜果购买客户数',
	`lastmonth_dairy_amt` DECIMAL(38,18) COMMENT '上月乳品实付GMV',
	`lastmonth_dairy_cust_cnt` BIGINT COMMENT '上月乳品购买客户数',
	`lastmonth_other_amt` DECIMAL(38,18) COMMENT '上月其他实付GMV',
	`lastmonth_other_cust_cnt` BIGINT COMMENT '上月其他购买客户数',
	`lastmonth_self_amt` DECIMAL(38,18) COMMENT '上月自营品牌实付GMV',
	`lastmonth_self_cust_cnt` BIGINT COMMENT '上月自营品牌购买客户数',
	`lastmonth_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月平均SPU数',
	`lastmonth_fruit_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月平均鲜果SPU数',
	`lastmonth_dairy_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月平均乳品SPU数',
	`lastmonth_other_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月平均标品SPU数'
) 
COMMENT '客户进度月汇总表，按月统计客户生命周期进度、订单行为、商品购买情况等指标'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	'comment'='客户进度月汇总表，包含客户生命周期、订单行为、商品购买等多维度统计指标')
LIFECYCLE 30;