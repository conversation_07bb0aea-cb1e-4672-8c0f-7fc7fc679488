CREATE TABLE IF NOT EXISTS app_wms_warehouse_sku_after_sale_rate_statistics_df(
	pt STRING COMMENT '统计日期，格式为yyyyMMdd，表示年月日',
	warehouse_no BIGINT COMMENT '仓库编码，取值范围：2-155',
	warehouse_name STRING COMMENT '仓库名称，枚举值包括：南宁总仓、东莞总仓、嘉兴总仓、华西总仓、青岛总仓、福州总仓、南京总仓、武汉总仓、济南总仓、长沙总仓、重庆总仓、贵阳总仓、上海总仓、嘉兴海盐总仓、东莞冷冻总仓、苏州总仓等',
	sku STRING COMMENT 'SKU编码，商品唯一标识',
	after_sale_rate_type BIGINT COMMENT '售后率类型，枚举值：1-近3天售后率',
	after_sale_rate DECIMAL(38,18) COMMENT '售后率，小数形式表示'
) 
COMMENT '仓库SKU售后率统计表，记录各仓库各SKU的售后率数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='仓库SKU售后率统计分析表，用于监控和分析各仓库商品的售后情况') 
LIFECYCLE 30;