CREATE TABLE IF NOT EXISTS app_bd_target_review_mi(
	months STRING COMMENT '月份，格式：yyyyMM',
	bd_id BIGINT COMMENT '业绩归属的BD_ID，销售人员唯一标识',
	bd_name STRING COMMENT '业绩归属的销售人员姓名',
	bd_m1 STRING COMMENT 'BD所属M1管理者姓名，即直接上级',
	bd_m2 STRING COMMENT 'BD所属M2管理者姓名，即M1的直接上级',
	bd_m3 STRING COMMENT 'BD所属M3管理者姓名，即M2的直接上级',
	bd_work_zone STRING COMMENT 'BD所在销售区域，枚举值：贵阳,重庆、四川',
	bd_work_city STRING COMMENT 'BD所在城市，枚举值：重庆市、成都市、绵阳市、德阳市、贵阳市',
	team_tag STRING COMMENT '销售团队，枚举值：平台销售',
	same_m1_gmv_last_m3 DECIMAL(38,18) COMMENT '非ATGMV前3月达成值，不含AT的GMV指标',
	same_m1_gmv_last_m2 DECIMAL(38,18) COMMENT '非ATGMV前2月达成值，不含AT的GMV指标',
	same_m1_gmv_last_m1 DECIMAL(38,18) COMMENT '非ATGMV上月达成值，不含AT的GMV指标',
	same_m1_avg_gmv DECIMAL(38,18) COMMENT '非ATGMV达成月均值，不含AT的GMV指标',
	no_at_gmv_target DECIMAL(38,18) COMMENT '非ATGMV本月目标，不含AT的GMV目标值',
	bd_gmv_increase_rate DECIMAL(38,18) COMMENT 'BD非ATGMV目标增长率，不含AT的GMV增长率',
	m1_gmv_increase_rate DECIMAL(38,18) COMMENT 'M1团队非ATGMV目标增长率，不含AT的GMV增长率',
	gmv_increase_rate_diff DECIMAL(38,18) COMMENT 'BD和团队非ATGMV目标增长率偏离值，不含AT的GMV增长率差异',
	is_gmv_diff_more_10 STRING COMMENT '非ATGMV目标偏离值是否超过10%，枚举值：是、否',
	same_m1_fruit_gmv_last_m3 DECIMAL(38,18) COMMENT '鲜果GMV前3月达成值_同城，鲜果品类GMV指标',
	same_m1_fruit_gmv_last_m2 DECIMAL(38,18) COMMENT '鲜果GMV前2月达成值_同城，鲜果品类GMV指标',
	same_m1_fruit_gmv_last_m1 DECIMAL(38,18) COMMENT '鲜果GMV上月达成值_同城，鲜果品类GMV指标',
	same_m1_avg_fruit_gmv DECIMAL(38,18) COMMENT '鲜果GMV前3月达成月均值_同城，鲜果品类GMV指标',
	fruit_gmv_target DECIMAL(38,18) COMMENT '鲜果GMV本月目标，鲜果品类GMV目标值',
	fruit_gmv_increase_rate DECIMAL(38,18) COMMENT 'BD鲜果GMV目标增长率，鲜果品类GMV增长率',
	m1_fruit_gmv_increase_rate DECIMAL(38,18) COMMENT 'M1团队鲜果GMV目标增长率，鲜果品类GMV增长率',
	fruit_gmv_increase_rate_diff DECIMAL(38,18) COMMENT 'BD和团队鲜果GMV目标增长率偏离值，鲜果品类GMV增长率差异',
	is_fruit_gmv_diff_more_10 STRING COMMENT '鲜果GMV目标偏离值是否超过10%，枚举值：是、否'
) 
COMMENT '销售目标偏差校对表，用于分析BD销售目标与实际达成之间的偏差情况，包含非ATGMV和鲜果GMV的多维度对比分析'
PARTITIONED BY (ds STRING COMMENT '分区字段，数据日期，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='销售目标偏差校对表，支持销售目标达成率的监控和分析') 
LIFECYCLE 30;