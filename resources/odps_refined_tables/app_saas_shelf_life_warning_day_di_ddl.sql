```sql
CREATE TABLE IF NOT EXISTS app_saas_shelf_life_warning_day_di(
    `warehouse_no` BIGINT COMMENT '仓库编号，唯一标识一个仓库',
    `warehouse_name` STRING COMMENT '仓库名称',
    `warehouse_provider` STRING COMMENT '仓库服务商名称',
    `pd_id` BIGINT COMMENT '货品编码，唯一标识一个货品',
    `sku` STRING COMMENT 'SKU编码，库存量单位编码',
    `saas_sku_id` BIGINT COMMENT 'SaaS系统中的SKU ID',
    `category_id` BIGINT COMMENT '商品类目ID',
    `sku_tenant_id` BIGINT COMMENT 'SKU所属租户ID',
    `warehouse_tenant_id` BIGINT COMMENT '仓库所属租户ID',
    `batch` STRING COMMENT '商品批次号',
    `production_date` DATETIME COMMENT '生产日期，格式：年月日时分秒',
    `quality_date` DATETIME COMMENT '保质期到期日期，格式：年月日时分秒',
    `quantity` BIGINT COMMENT '库存数量',
    `shelf_life_days` BIGINT COMMENT '保质期总天数',
    `remaining_days` BIGINT COMMENT '保质期剩余天数',
    `approaching_shelf_life_percentage` DECIMAL(38,18) COMMENT '临保百分比，保存百分数格式',
    `status` BIGINT COMMENT '保质期状态：1-正常、2-临保、3-过期',
    `day_tag` BIGINT COMMENT '数据所属日期，格式：yyyyMMdd',
    `use_flag` BIGINT COMMENT '使用标识：0-停用、1-启用'
)
COMMENT '按天汇总的SaaS库存保质期预警数据表，用于监控商品保质期状态和预警'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SaaS库存保质期预警日汇总表',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```