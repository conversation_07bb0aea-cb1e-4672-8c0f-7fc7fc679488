```sql
CREATE TABLE IF NOT EXISTS app_crm_bd_big_cust_month_gmv_mi(
    bd_id BIGINT COMMENT 'BD编号，销售人员唯一标识，取值范围：-1表示无BD，正整数表示有效BD编号',
    bd_name STRING COMMENT 'BD姓名，销售人员名称，枚举值包括：无、阿晖、张永丰、谈敏良、桂少达等约146个值',
    big_cust STRING COMMENT '大客户公司全称，如：上海函樾供应链有限公司、上海吉茶餐饮管理有限公司等约170个值',
    big_cust_alias STRING COMMENT '大客户品牌别名，如：柠檬向右手打柠檬茶、乐乐茶、霸王茶姬等约171个值',
    merchant_total_gmv DECIMAL(38,18) COMMENT '总业绩金额，包含所有业务的GMV总额',
    credit_paid_gmv DECIMAL(38,18) COMMENT '账期总业绩金额，采用信用账期支付的GMV',
    cash_settlement_gmv DECIMAL(38,18) COMMENT '现结总业绩金额，采用现金即时结算的GMV',
    merchant_total_gmv_ex DECIMAL(38,18) COMMENT '除茶百道外的总业绩金额，排除茶百道业务后的GMV总额',
    credit_paid_gmv_ex DECIMAL(38,18) COMMENT '除茶百道外的账期总业绩金额，排除茶百道业务后的信用账期GMV',
    cash_settlement_gmv_ex DECIMAL(38,18) COMMENT '除茶百道外的现结总业绩金额，排除茶百道业务后的现金结算GMV'
)
COMMENT '大客户本月GMV区分账期现结表，按BD维度统计大客户的月度业绩，区分账期和现结两种支付方式，并提供排除茶百道业务后的数据'
PARTITIONED BY (
    ym STRING COMMENT '分区字段，年月格式yyyyMM，表示数据所属的年月'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '大客户本月GMV业绩分析表，用于BD业绩核算和客户分析',
    'lifecycle' = '30'
);
```