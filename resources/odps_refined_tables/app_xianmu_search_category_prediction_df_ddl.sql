CREATE TABLE IF NOT EXISTS app_xianmu_search_category_prediction_df(
    `query` STRING COMMENT '搜索词，用户输入的搜索关键词，如：芒果、牛奶、蓝莓等',
    `category4` STRING COMMENT '四级类目名称，商品最细粒度的分类名称，如：芒果、常温牛奶、搅打型稀奶油等',
    `category4_id` STRING COMMENT '四级类目ID，四级类目的唯一标识符，如：533、607、605等',
    `category3` STRING COMMENT '三级类目名称，商品的上级分类名称，如：核果类、液体乳、浆果类等',
    `click_cnt` BIGINT COMMENT '点击次数，该query下对应四级类目商品的点击数量统计',
    `category_rank` DOUBLE COMMENT '类目排名得分，该四级类目的商品在该query的搜索结果页的点击数排名得分（0-1范围）',
    `category_percentile` DOUBLE COMMENT '类目百分位，该四级类目在搜索结果中的百分位排名（0-1范围）',
    `create_time` STRING COMMENT '创建时间，数据生成时间，格式为yyyy-MM-dd HH:mm:ss（年月日时分秒）'
)
COMMENT '搜索query-类目预测结果表，基于用户点击历史记录预测搜索词与商品类目的关联关系'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，数据日期，格式为yyyyMMdd（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='搜索query-类目预测模型输出表，用于搜索推荐和类目预测',
    'columnar.nested.type'='true'
)
LIFECYCLE 365;