CREATE TABLE IF NOT EXISTS app_timing_sku_top_di(
    sku STRING COMMENT '商品SKU编码，唯一标识一个商品，如：607164503701、L001S01R001等',
    area_no BIGINT COMMENT '运营服务城市编号，取值范围：1001-44237',
    order_sku_cnt BIGINT COMMENT '上周商品销量，取值范围：1-500',
    order BIGINT COMMENT '排序序号，取值范围：1-43',
    date_flag STRING COMMENT '信号表时间，格式：yyyyMMdd，表示年月日'
)
COMMENT '省心送商品TOP排名表，按城市和日期统计商品销量排名'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
     'comment'='省心送商品TOP排名表，按城市和日期维度统计商品销量并进行排名，用于业务分析和推荐') 
LIFECYCLE 30;