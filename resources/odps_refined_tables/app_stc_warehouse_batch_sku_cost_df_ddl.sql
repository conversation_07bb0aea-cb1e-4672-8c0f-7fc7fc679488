CREATE TABLE IF NOT EXISTS app_stc_warehouse_batch_sku_cost_df(
	`date` STRING COMMENT '日期，格式为yyyy-MM-dd，表示业务发生日期',
	`warehouse_no` BIGINT COMMENT '仓库编号，唯一标识一个仓库',
	`warehouse_name` STRING COMMENT '仓库名称，如贵阳总仓、重庆总仓等',
	`batch_no` STRING COMMENT '采购批次号，唯一标识一次采购批次',
	`product_batch_no` STRING COMMENT '生产批次号，唯一标识一个生产批次',
	`sku_id` STRING COMMENT '商品编码，唯一标识一个SKU',
	`sku_disc` STRING COMMENT '商品规格描述，如10KG*1箱、1KG*20包等',
	`spu_name` STRING COMMENT '商品名称，如ProtagxEva乳酸黄油、安佳片状乳酸黄油等',
	`category1` STRING COMMENT '一级类目，取值范围：乳制品、其他、鲜果',
	`sku_property` STRING COMMENT 'SKU性质，取值范围：常规、活动、临保、拆包、破袋',
	`sku_sale_unit` DECIMAL(38,18) COMMENT '当前售价，单位：元，保留18位小数',
	`store_quantity` BIGINT COMMENT '库存量，当前仓库中该SKU的总库存数量',
	`batch_quantity` BIGINT COMMENT '批次量，当前批次的库存数量',
	`batch_self_cost` DECIMAL(38,18) COMMENT '批次自提成本，单位：元，保留18位小数',
	`batch_allocate_cost` DECIMAL(38,18) COMMENT '批次调拨成本，单位：元，保留18位小数',
	`batch_1_quantity` BIGINT COMMENT '批次1库存量，第一批次的库存数量',
	`batch_1_self_cost` DECIMAL(38,18) COMMENT '批次1自提成本，单位：元，保留18位小数',
	`batch_1_allocate_cost` DECIMAL(38,18) COMMENT '批次1调拨成本，单位：元，保留18位小数',
	`batch_2_quantity` BIGINT COMMENT '批次2库存量，第二批次的库存数量',
	`batch_2_self_cost` DECIMAL(38,18) COMMENT '批次2自提成本，单位：元，保留18位小数',
	`batch_2_allocate_cost` DECIMAL(38,18) COMMENT '批次2调拨成本，单位：元，保留18位小数',
	`batch_3_quantity` BIGINT COMMENT '批次3库存量，第三批次的库存数量',
	`batch_3_self_cost` DECIMAL(38,18) COMMENT '批次3自提成本，单位：元，保留18位小数',
	`batch_3_allocate_cost` DECIMAL(38,18) COMMENT '批次3调拨成本，单位：元，保留18位小数',
	`batch_4_quantity` BIGINT COMMENT '批次4库存量，第四批次的库存数量',
	`batch_4_self_cost` DECIMAL(38,18) COMMENT '批次4自提成本，单位：元，保留18位小数',
	`batch_4_allocate_cost` DECIMAL(38,18) COMMENT '批次4调拨成本，单位：元，保留18位小数',
	`batch_5_quantity` BIGINT COMMENT '批次5库存量，第五批次的库存数量',
	`batch_5_self_cost` DECIMAL(38,18) COMMENT '批次5自提成本，单位：元，保留18位小数',
	`batch_5_allocate_cost` DECIMAL(38,18) COMMENT '批次5调拨成本，单位：元，保留18位小数',
	`batch_6_quantity` BIGINT COMMENT '批次6库存量，第六批次的库存数量',
	`batch_6_self_cost` DECIMAL(38,18) COMMENT '批次6自提成本，单位：元，保留18位小数',
	`batch_6_allocate_cost` DECIMAL(38,18) COMMENT '批次6调拨成本，单位：元，保留18位小数',
	`total_purchase_cost` DECIMAL(38,18) COMMENT '总采购单价，单位：元，保留18位小数',
	`total_unit_cost` DECIMAL(38,18) COMMENT '总单价，单位：元，保留18位小数',
	`total_cost` DECIMAL(38,18) COMMENT '总成本，单位：元，保留18位小数',
	`total_self_cost` DECIMAL(38,18) COMMENT '总自提单价，单位：元，保留18位小数',
	`total_allocate_cost` DECIMAL(38,18) COMMENT '总调拨单价，单位：元，保留18位小数'
) 
COMMENT '批次成本明细表，记录各仓库SKU的批次成本信息，包括自提成本、调拨成本等'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='批次成本明细表，用于成本核算和分析') 
LIFECYCLE 30;