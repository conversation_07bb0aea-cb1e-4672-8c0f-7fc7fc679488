```sql
CREATE TABLE IF NOT EXISTS app_finance_bill_revenue_details_di(
    order_no STRING COMMENT '订单编号，唯一标识一个订单',
    order_item_id BIGINT COMMENT '订单项编号，标识订单中的具体商品项',
    delivery_path_id BIGINT COMMENT '配送路径ID，标识商品的配送路线',
    pay_type STRING COMMENT '支付方式：None-无支付方式，线下支付-线下支付',
    service_area STRING COMMENT '配送仓大区：未知、华东、福建、西南、华中、贵州、山东、云南、广西',
    province STRING COMMENT '省份：广东、江苏、浙江、上海、福建、四川、湖北、湖南、江西、贵州、山东、重庆、云南、广西壮族自治区',
    city STRING COMMENT '城市：东莞市、南通市、绍兴市、上海市、厦门市、苏州市、成都市、武汉市、长沙市、南昌市等37个城市',
    m_id BIGINT COMMENT '客户ID，唯一标识一个客户',
    mname STRING COMMENT '客户名称，客户的店铺或公司名称',
    realname STRING COMMENT '品牌的工商注册名称，企业的法定名称',
    name_remakes STRING COMMENT '品牌的品牌名称，市场推广使用的品牌名',
    sku STRING COMMENT 'SKU编码，商品的唯一库存单位标识',
    pd_name STRING COMMENT '商品名称，商品的详细描述名称',
    category1 STRING COMMENT '商品一级类目：乳制品、鲜果、其他',
    tax_rate DECIMAL(38,18) COMMENT '税率，商品适用的增值税率',
    order_sku_cnt BIGINT COMMENT '订单商品数量，订单中该商品的数量',
    real_sku_cnt BIGINT COMMENT '实际送达商品数量，剔除缺货后的实际配送数量',
    real_total_amt DECIMAL(38,18) COMMENT '订单实付总价，客户实际支付的金额（含税）',
    origin_total_amt DECIMAL(38,18) COMMENT '订单应付总价，商品原价总金额（含税）',
    total_discount_amt DECIMAL(38,18) COMMENT '营销优惠金额，各种促销活动的优惠总额',
    delivery_amt DECIMAL(38,18) COMMENT '运费金额，配送服务产生的费用',
    out_times_amt DECIMAL(38,18) COMMENT '超时加单金额，超时配送产生的额外费用',
    pay_time DATETIME COMMENT '支付日期时间，格式为yyyy-MM-dd HH:mm:ss，订单支付的具体时间',
    finish_time DATETIME COMMENT '确认收入日期时间，格式为yyyy-MM-dd HH:mm:ss，财务确认收入的时间',
    revenue_amt DECIMAL(38,18) COMMENT '确认收入金额（含税），财务确认的收入金额',
    revenue_amt_notax DECIMAL(38,18) COMMENT '确认收入金额（不含税），扣除税款后的收入金额',
    tax_amt DECIMAL(38,18) COMMENT '税额，收入金额中对应的税款金额',
    unit_cost DECIMAL(38,18) COMMENT '成本单价（含税），单个商品的成本价格',
    unit_cost_notax DECIMAL(38,18) COMMENT '成本单价（不含税），扣除税款后的单个商品成本',
    cost DECIMAL(38,18) COMMENT '成本（含税），该商品项的总成本金额',
    cost_notax DECIMAL(38,18) COMMENT '成本（不含税），扣除税款后的总成本金额',
    delivery_coupon_amt DECIMAL(38,18) COMMENT '运费优惠金额，运费相关的优惠减免金额',
    cust_team STRING COMMENT '客户团队类型：平台客户-普通平台客户，大客户-重点大客户',
    remark STRING COMMENT '订单备注，订单的特殊说明或标识信息',
    agent_sale_flag BIGINT COMMENT '代售商品标志：0-是代售商品，1-非代售商品',
    sub_type BIGINT COMMENT '商品二级性质：1-自营代销不入仓，2-自营代销入仓，3-自营经销，4-代仓代仓',
    order_status BIGINT COMMENT '订单状态：2-待配送，3-待收货，6-已收货，8-已退款',
    precision_delivery_fee DECIMAL(38,18) COMMENT '精准送费用，精准配送服务产生的费用',
    settle_type STRING COMMENT '结算类型：空字符串-未设置，成本结算-按成本结算'
) 
COMMENT '财务口径账期收入明细表，包含订单的财务收入确认、成本核算、税款计算等详细信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
     'comment'='财务口径账期收入明细表，用于财务收入确认、成本核算和业务分析') 
LIFECYCLE 30;
```