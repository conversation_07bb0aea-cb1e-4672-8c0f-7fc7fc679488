CREATE TABLE IF NOT EXISTS app_saas_merchant_store_order_analysis_month_df(
	tenant_id BIGINT COMMENT '租户ID，取值范围：2-44',
	time_tag STRING COMMENT '时间标签，格式：yyyyMMdd，表示年月日，取值范围：20220601-20250922',
	store_id BIGINT COMMENT '门店ID，取值范围：1-348361',
	average_order_period DECIMAL(38,18) COMMENT '平均订货周期（天）',
	average_order_period_last_period DECIMAL(38,18) COMMENT '上周期平均订货周期（天）',
	average_order_period_upper_period DECIMAL(38,18) COMMENT '平均订货周期环比变化率',
	order_amount BIGINT COMMENT '订货数量，取值范围：1-35161',
	order_amount_last_period BIGINT COMMENT '上周期订货数量，取值范围：0-20115',
	order_amount_upper_period DECIMAL(38,18) COMMENT '订货数量环比变化率',
	order_price DECIMAL(38,18) COMMENT '订货金额（元）',
	order_price_last_period DECIMAL(38,18) COMMENT '上周期订货金额（元）',
	order_price_upper_period DECIMAL(38,18) COMMENT '订货金额环比变化率',
	last_order_time STRING COMMENT '最后订货日期，格式：yyyy-MM-dd，表示年月日',
	last_order_amount BIGINT COMMENT '最后订货数量，取值范围：1-7721',
	last_order_price DECIMAL(38,18) COMMENT '最后订货金额（元）'
) 
COMMENT 'SaaS门店订货分析月报表，包含各门店的订货周期、订货数量、订货金额等关键指标及其环比变化情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS门店订货分析月报表，用于分析各门店的订货行为和趋势变化') 
LIFECYCLE 30;