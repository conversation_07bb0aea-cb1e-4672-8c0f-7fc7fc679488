```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_order_coupon_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `sku_id` STRING COMMENT 'SKU编码，商品最小库存单位的唯一标识',
  `spu_name` STRING COMMENT '商品名称，即标准产品单元名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格包装信息，如10KG*1箱',
  `category1` STRING COMMENT '一级类目，枚举值：乳制品、其他',
  `coupon_amt_label` DECIMAL(38,18) COMMENT '营销费用占比，小数形式表示的费用比例',
  `cust_cnt` BIGINT COMMENT '客户数，购买该SKU的独立客户数量'
)
COMMENT '人群营销结构表，记录商品级别的营销费用占比和客户购买情况'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '人群营销结构分析表，用于分析商品营销费用与客户购买行为的关系',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```