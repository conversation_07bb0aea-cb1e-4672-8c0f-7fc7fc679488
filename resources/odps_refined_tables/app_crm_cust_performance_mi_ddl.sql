```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_cust_performance_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `cust_type` STRING COMMENT '客户类型:全部，茶饮，咖啡，水果/果切/榨汁店，甜品冰淇淋，糖水/水果捞，蛋糕店，西餐，西餐披萨，面包蛋糕，面包蛋糕点心，其他',
  `administrative_city` STRING COMMENT '行政城市名称，如上海市、北京市等',
  `m1` STRING COMMENT '城市负责人（M1管理者姓名）',
  `origin_amt` DECIMAL(38,18) COMMENT '应付GMV金额，单位：元',
  `real_amt` DECIMAL(38,18) COMMENT '实付GMV金额，单位：元',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额，单位：元',
  `origin_profit_ratio` DECIMAL(38,18) COMMENT '应付毛利率，计算公式：(应付GMV-成本)/应付GMV',
  `real_profit_ratio` DECIMAL(38,18) COMMENT '实付毛利率，计算公式：(实付GMV-成本)/实付GMV',
  `real_profit_avg` DECIMAL(38,18) COMMENT '人均实付毛利值，单位：元/人',
  `cust_cnt` BIGINT COMMENT '下单客户数',
  `spu_cnt_avg` DECIMAL(38,18) COMMENT '人均SPU数',
  `origin_arpu` DECIMAL(38,18) COMMENT 'ARPU值（应付），单位：元/人',
  `fruit_amt_ratio` DECIMAL(38,18) COMMENT '鲜果GMV占比',
  `dairy_amt_ratio` DECIMAL(38,18) COMMENT '乳制品GMV占比',
  `other_amt_ratio` DECIMAL(38,18) COMMENT '其他GMV占比',
  `fruit_profit_amt_ratio` DECIMAL(38,18) COMMENT '鲜果实付毛利值占比',
  `dairy_profit_amt_ratio` DECIMAL(38,18) COMMENT '乳制品实付毛利值占比',
  `other_profit_amt_ratio` DECIMAL(38,18) COMMENT '其他实付毛利值占比',
  `fruit_cust_cnt_ratio` DECIMAL(38,18) COMMENT '鲜果覆盖率（购买鲜果的客户占比）',
  `dairy_cust_cnt_ratio` DECIMAL(38,18) COMMENT '乳制品覆盖率（购买乳制品的客户占比）',
  `other_cust_cnt_ratio` DECIMAL(38,18) COMMENT '其他覆盖率（购买其他商品的客户占比）',
  `fruit_category4_cnt_avg` DECIMAL(38,18) COMMENT '鲜果人均四级类目数',
  `dairy_category4_cnt_avg` DECIMAL(38,18) COMMENT '乳制品人均四级类目数',
  `other_category4_cnt_avg` DECIMAL(38,18) COMMENT '其他人均四级类目数'
)
COMMENT '客户类型粒度平台销售业绩报表月汇总表，按月份、客户类型、行政城市维度统计销售业绩指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户类型粒度平台销售业绩报表月汇总表，包含GMV、毛利率、客户数、品类占比等核心业务指标',
  'lifecycle' = '30'
)
```