CREATE TABLE IF NOT EXISTS app_category_coupon_quota_reward_di(
    day_tag STRING COMMENT '数据更新标记，格式为yyyyMMdd，表示数据所属的年月日',
    admin_id BIGINT COMMENT 'M1管理者ID，-1表示未知或无效',
    admin_name STRING COMMENT 'M1管理者姓名',
    bd_id BIGINT COMMENT '申请人ID，-1表示未知或无效',
    bd_name STRING COMMENT '申请人姓名',
    amount DECIMAL(38,18) COMMENT '奖励金额'
)
COMMENT '商城搜索词流量分析表，记录BD申请人的奖励金额及相关管理者信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='商城搜索词流量分析表，包含BD申请人的奖励金额、管理者信息和数据时间标记')
LIFECYCLE 30;