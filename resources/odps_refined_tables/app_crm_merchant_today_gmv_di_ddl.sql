CREATE TABLE IF NOT EXISTS app_crm_merchant_today_gmv_di(
    cust_id BIGINT COMMENT '商户ID，唯一标识一个商户，取值范围：143-576054',
    cust_name STRING COMMENT '商户名称，如：CDcake、姚客、今至烘焙等',
    merchant_total_gmv DECIMAL(38,18) COMMENT '商户总GMV（剔除N001S01R005、N001S01R002），单位：元',
    fruit_gmv DECIMAL(38,18) COMMENT '鲜果类商品GMV，单位：元',
    dairy_gmv DECIMAL(38,18) COMMENT '乳制品类商品GMV，单位：元',
    non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品类商品GMV，单位：元',
    brand_gmv DECIMAL(38,18) COMMENT '自营品牌商品GMV，单位：元',
    reward_gmv DECIMAL(38,18) COMMENT '奖励SKU商品GMV，单位：元',
    distribution_gmv DECIMAL(38,18) COMMENT '配送GMV（第二天计划配送的订单），单位：元',
    spu_average DECIMAL(38,18) COMMENT '配送SPU均值，表示平均每个订单的商品种类数'
)
COMMENT '商户当日GMV明细表，记录各商户每日的商品交易总额及各品类GMV分布情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='商户GMV日统计表，用于分析商户销售表现和商品品类分布')
LIFECYCLE 30;