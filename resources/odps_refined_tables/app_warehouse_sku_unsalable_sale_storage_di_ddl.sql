CREATE TABLE IF NOT EXISTS app_warehouse_sku_unsalable_sale_storage_di(
    warehouse_no BIGINT COMMENT '库存仓编号',
    warehouse_name STRING COMMENT '库存仓名称，枚举值包括：上海总仓、金华总仓、宁波总仓、江苏总仓、嘉兴总仓、济南总仓、武汉总仓、深圳总仓等',
    sku_id STRING COMMENT 'SKU编号，商品唯一标识',
    category1 STRING COMMENT '一级类目，枚举值包括：乳制品、其他、鲜果',
    category4_id STRING COMMENT '四级类目ID',
    category4 STRING COMMENT '四级类目名称，枚举值包括：乳酸黄油、有盐黄油、搅打型稀奶油、无盐黄油、低筋面粉、高筋面粉、奶油奶酪等',
    sku_brand STRING COMMENT '品牌名称，枚举值包括：安佳、西诺迪斯、美玫、金像、Kiri、奥利奥、维益、爱氏晨曦等',
    spu_name STRING COMMENT 'SPU名称，标准产品单元名称',
    disc STRING COMMENT 'SKU描述，包含规格包装信息',
    sku_origin STRING COMMENT '商品产地，枚举值：进口、国产',
    sku_property STRING COMMENT 'SKU性质，枚举值：常规、拆包、临保、破袋',
    store_method STRING COMMENT '存储方式，枚举值：冷冻、冷藏、常温',
    warn_days BIGINT COMMENT '预警天数，商品库存预警阈值',
    purchase_in_quality BIGINT COMMENT '历史两周采购入库数量',
    sale_out_quality BIGINT COMMENT '历史两周销售出库数量',
    allocate_in_quality BIGINT COMMENT '历史两周调拨入库数量',
    allocate_out_quality BIGINT COMMENT '历史两周调拨出库数量',
    purchase_on_way_quality BIGINT COMMENT '采购在途数量',
    allocate_on_way_quality BIGINT COMMENT '调拨在途数量',
    lately_arrived_time STRING COMMENT '八周内最近入库时间，格式：yyyyMMdd，无入库记录时显示"无"',
    enable_quality BIGINT COMMENT '在库库存数量，当前可用库存',
    safe_quality BIGINT COMMENT '安全库存数量，库存安全阈值',
    avg_quality BIGINT COMMENT '过去2周日均出库量',
    doc_quality BIGINT COMMENT 'DOC（现货）数量，可直接出货的库存',
    doc_on_way_quality BIGINT COMMENT 'DOC（现货+在途）数量，包含在途的可用库存',
    use_days BIGINT COMMENT '库存可用天数，基于当前库存和日均出库量计算',
    duration_on_shelf DECIMAL(38,18) COMMENT '上架时长（天），商品上架后的存放时间',
    duration_sale_out DECIMAL(38,18) COMMENT '售罄时长（天），商品从入库到售罄的时间',
    sale_out_rask DECIMAL(38,18) COMMENT '售罄率，销售出库数量与库存数量的比率',
    is_sale_out_rask STRING COMMENT '售罄风险判定，枚举值：是、否',
    unsalable_decided STRING COMMENT '动销判定，枚举值：-（正常）、两周低动销、两周无动销',
    last_sale_time DATETIME COMMENT '最后一次销售时间，格式：yyyy-MM-dd HH:mm:ss，表示年月日时分秒',
    last_sale_day BIGINT COMMENT '最后一次销售距今时间（天）',
    storage_amt DECIMAL(38,18) COMMENT '在库库存成本（元）'
)
COMMENT '每日SKU、库存仓维度滞销情况表，用于监控商品动销情况和库存健康度'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='SKU滞销分析表，包含库存、销售、动销判定等关键指标')
LIFECYCLE 30;