CREATE TABLE IF NOT EXISTS app_city_trade_selfowened_di(
	`date` STRING COMMENT '日期，格式：yyyyMMdd',
	`register_province` STRING COMMENT '注册省份',
	`register_city` STRING COMMENT '注册城市',
	`register_area` STRING COMMENT '注册区域',
	`cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、其他',
	`brand_type` STRING COMMENT '大客户类型；枚举值：大客户、普通、批发客户',
	`brand_name` STRING COMMENT '品牌名称；枚举值：C味、Protag蛋白标签、SUMMERFARM、ZEROMIX艾诺兮、ZILIULIU、沐清友、澄善、酷盖',
	`category1` STRING COMMENT '一级类目；枚举值：其他、乳制品',
	`category2_id` STRING COMMENT '二级类目ID',
	`category2` STRING COMMENT '二级类目；枚举值：成品原料、谷物制品、蔬菜制品、乳制品、食用油丨油脂及制品、饮料、饮品原料、坚果制品、糖丨糖制品、茶制品、水果制品',
	`category3_id` STRING COMMENT '三级类目ID',
	`category3` STRING COMMENT '三级类目；枚举值：配料（小料）类、粉圆类配料、果冻类配料、谷物罐头、罐头、冷冻蔬菜、黄油、液体乳、食用油脂制品、植物蛋白饮料、果汁原料、咖啡豆及其制品、烘炒类、糖浆、茶叶、袋泡茶、冷冻水果、水果风味制品',
	`category4_id` STRING COMMENT '四级类目ID',
	`category4` STRING COMMENT '四级类目；枚举值：配料（小料）、麻薯、芋圆、珍珠、糯米粉圆、果冻、椰果、波波丨晶球、爆爆珠、杂粮罐头、冷冻熟蔬菜制品、乳酸黄油、混合黄油、常温牛奶、鲜牛奶、非氢化基底奶、植脂奶油、其他植物蛋白饮料、果汁原浆、咖啡豆、坚果、蔗糖糖浆、乌龙茶、绿茶、红茶、红茶茶包、乌龙茶包、绿茶包、水果罐头、冷冻果肉、冷冻整果、冷冻果泥、果茶酱、水果类馅料、果汁浓浆',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
	`cust_cnt` BIGINT COMMENT '客户数',
	`new_cust_cnt` BIGINT COMMENT '（历史截止今天）当天新客户数',
	`order_time_cnt` DECIMAL(38,18) COMMENT '下单时间间隔之和，单位：分钟',
	`order_time_avg` DECIMAL(38,18) COMMENT '平均下单时间间隔，单位：分钟'
) 
COMMENT '自营品牌城市整体交易数据日表，包含按城市维度的交易统计信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment' = '自营品牌城市交易数据统计表，用于分析各城市维度的交易情况和客户行为')
LIFECYCLE 30;