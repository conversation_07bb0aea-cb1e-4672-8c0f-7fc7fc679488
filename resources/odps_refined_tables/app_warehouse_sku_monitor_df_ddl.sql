CREATE TABLE IF NOT EXISTS app_warehouse_sku_monitor_df(
    `warehouse_no` BIGINT COMMENT '仓库编号，唯一标识一个库存仓',
    `warehouse_name` STRING COMMENT '仓库名称，枚举值包括：上海总仓、嘉兴总仓、华西总仓、重庆总仓、福州总仓、长沙总仓、南宁总仓、昆明总仓、苏州总仓、贵阳总仓、青岛总仓、东莞总仓、东莞冷冻总仓、嘉兴海盐总仓、南京总仓、济南总仓、嘉兴水果批发总仓、武汉总仓',
    `sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
    `sku_disc` STRING COMMENT '商品规格描述，如：900g*12包、1KG*1包等',
    `spu_name` STRING COMMENT 'SPU名称，标准产品单位名称',
    `category1` STRING COMMENT '一级类目，枚举值包括：其他、乳制品、鲜果',
    `sku_type` STRING COMMENT 'SKU类型，枚举值包括：自营',
    `sku_brand` STRING COMMENT '品牌名称',
    `sku_property` STRING COMMENT 'SKU性质，枚举值包括：常规、临保、拆包、破袋、活动',
    `store_method` STRING COMMENT '存储方式，枚举值包括：常温、冷冻、冷藏',
    `purchase_name` STRING COMMENT '采购员姓名',
    `plan_name` STRING COMMENT '计划员姓名',
    `level_label` STRING COMMENT '商品分层等级，枚举值包括：A、B、C',
    `supplier_id` BIGINT COMMENT '供应商编号',
    `supplier` STRING COMMENT '供应商名称',
    `is_core` STRING COMMENT '是否核心品类，枚举值：核心、非核心',
    `is_sale_on` STRING COMMENT '是否上架销售，枚举值：上架、下架',
    `is_inventory` STRING COMMENT '是否有库存，枚举值：是、否',
    `init_quantity` STRING COMMENT '在仓库存数量',
    `sku_cost_amt` DECIMAL(38,18) COMMENT '商品总成本金额',
    `sku_price_cost` DECIMAL(38,18) COMMENT '商品单件成本金额',
    `max_tock_age` BIGINT COMMENT '最大库龄（天）',
    `purchase_on_way_quality` BIGINT COMMENT '采购在途数量',
    `purchase_on_way_amt` DECIMAL(38,18) COMMENT '采购在途成本金额',
    `purchase_arr_quality` BIGINT COMMENT '采购预约数量',
    `allocate_on_way_quality` BIGINT COMMENT '调拨在途数量',
    `allocate_on_way_amt` DECIMAL(38,18) COMMENT '调拨在途成本金额',
    `out_quantity` BIGINT COMMENT '近一天出库数量',
    `out_amt` DECIMAL(38,18) COMMENT '近一天出库金额',
    `out_quantity_7day` BIGINT COMMENT '近7天出库数量',
    `out_amt_7day` DECIMAL(38,18) COMMENT '近7天出库金额',
    `out_quantity_14day` BIGINT COMMENT '近14天出库数量',
    `out_amt_14day` DECIMAL(38,18) COMMENT '近14天出库金额',
    `out_quantity_30day` BIGINT COMMENT '近30天出库数量',
    `out_amt_30day` DECIMAL(38,18) COMMENT '近30天出库金额',
    `nearest_7_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近7天库存总成本',
    `nearest_14_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近14天库存总成本',
    `nearest_30_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近30天库存总成本',
    `month_total_inventory_cost` DECIMAL(38,18) COMMENT '本月库存总成本',
    `duration_on_shelf` DECIMAL(38,18) COMMENT '近一天上架时长（小时）',
    `duration_sale_out` DECIMAL(38,18) COMMENT '近一天售罄时长（小时）',
    `duration_on_shelf_7day` DECIMAL(38,18) COMMENT '近7天上架时长（小时）',
    `duration_sale_out_7day` DECIMAL(38,18) COMMENT '近7天售罄时长（小时）',
    `duration_on_shelf_30day` DECIMAL(38,18) COMMENT '近30天上架时长（小时）',
    `duration_sale_out_30day` DECIMAL(38,18) COMMENT '近30天售罄时长（小时）',
    `month_duration_on_shelf` DECIMAL(38,18) COMMENT '本月上架时长（小时）',
    `month_duration_sale_out` DECIMAL(38,18) COMMENT '本月售罄时长（小时）',
    `temporary_cnt` BIGINT COMMENT '预计临保件数',
    `temporary_amt` DECIMAL(38,18) COMMENT '预计临保成本金额',
    `estimated_damage_cnt` BIGINT COMMENT '预计货损件数',
    `estimated_damage_amt` DECIMAL(38,18) COMMENT '预计货损成本金额',
    `timing_plan_sku_cnt_2week` BIGINT COMMENT '省心送两周内已配置计划数',
    `timing_plan_sku_cnt` BIGINT COMMENT '省心送已配置计划总数',
    `timing_no_dlv_sku_cnt` BIGINT COMMENT '省心送未履约SKU数量',
    `timing_no_dlv_sku_amt` DECIMAL(38,18) COMMENT '省心送未履约成本金额',
    `is_sale_out_rask` STRING COMMENT '售罄风险判定，枚举值：是、否',
    `unsalable_decided` STRING COMMENT '动销判定，枚举值：两周无动销、缺货品、-、两周低动销、新品',
    `use_cnt` BIGINT COMMENT '可用库存数量',
    `use_amt` DECIMAL(38,18) COMMENT '可用库存成本金额',
    `quality_tag` STRING COMMENT '是否40天内都有库存，枚举值：是、否',
    `month_sale_quality` BIGINT COMMENT '当月出库数量',
    `month_total_sale_cost` DECIMAL(38,18) COMMENT '当月出库金额'
)
COMMENT '库存仓商品监控数据表，用于监控各仓库商品的库存、销售、成本等关键指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'columnar.nested.type'='true',
    'comment'='库存仓商品监控数据表，包含商品基本信息、库存状态、销售数据、成本分析等综合监控指标'
)
LIFECYCLE 30;