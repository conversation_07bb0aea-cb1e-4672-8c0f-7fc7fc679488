```sql
CREATE TABLE IF NOT EXISTS app_xianmu_sale_purchase_item_change_order_df(
    purchase_no STRING COMMENT '采购批次号，唯一标识一次采购订单',
    sku STRING COMMENT '商品SKU编码，唯一标识具体商品',
    pd_name STRING COMMENT '商品名称，描述商品的具体名称',
    weight STRING COMMENT '商品规格/重量信息，示例值："None"表示无规格信息',
    supplier STRING COMMENT '供货商名称，示例值："杭州鲜沐科技有限公司"',
    quantity BIGINT COMMENT '计划采购数量，单位为个',
    actual_quantity BIGINT COMMENT '实际收货数量，单位为个',
    price_type STRING COMMENT '价格形式，枚举值：指定价',
    cost DECIMAL(38,18) COMMENT '单个商品成本价格，精度为38位数字，18位小数',
    total_cost DECIMAL(38,18) COMMENT '总成本金额，计算公式：cost * actual_quantity，精度为38位数字，18位小数'
)
COMMENT '销转采采购单明细表，记录销售转采购流程中的采购订单商品明细信息，包括采购批次、商品信息、数量、成本和供应商等数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='销转采采购单明细表，用于存储销售转采购业务中的采购商品明细数据')
LIFECYCLE 30;
```