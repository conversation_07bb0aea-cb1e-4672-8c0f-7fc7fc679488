```sql
CREATE TABLE IF NOT EXISTS app_saas_merchant_store_order_hysteresis_analysis_df(
    tenant_id BIGINT COMMENT '租户ID',
    store_id BIGINT COMMENT '门店ID',
    store_code STRING COMMENT '门店编号',
    store_type BIGINT COMMENT '门店类型：0-直营店，1-加盟店，2-托管店',
    store_name STRING COMMENT '门店名称',
    store_status BIGINT COMMENT '门店状态：0-审核中，1-审核通过，2-审核拒绝，3-已关店，4-拉黑，5-注销',
    item_id BIGINT COMMENT '商品ID',
    sku_id BIGINT COMMENT '货品ID',
    title STRING COMMENT '商品名称',
    specification STRING COMMENT '商品规格描述',
    specification_unit STRING COMMENT '规格单位：罐、包、g、袋、盒、箱、卷、件、组、桶、瓶、盆、套、块、台等',
    last_order_date DATETIME COMMENT '最后叫货日期，格式为年月日时分秒',
    last_order_price DECIMAL(38,18) COMMENT '最后叫货单价',
    last_order_quantity BIGINT COMMENT '最后叫货数量',
    last_order_total_price DECIMAL(38,18) COMMENT '最后叫货总金额',
    delay_days BIGINT COMMENT '滞叫天数，即距离最后叫货的天数',
    contact STRING COMMENT '门店联系人',
    phone STRING COMMENT '店长手机号'
)
COMMENT 'SaaS商户门店订单滞叫分析表，用于分析门店商品的滞叫情况，包含门店基本信息、商品信息、最后叫货记录和滞叫天数等数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SaaS商户门店订单滞叫分析表',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```