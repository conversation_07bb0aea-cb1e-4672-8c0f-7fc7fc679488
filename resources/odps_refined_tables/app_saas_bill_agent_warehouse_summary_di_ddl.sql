CREATE TABLE IF NOT EXISTS app_saas_bill_agent_warehouse_summary_di(
	`tenant_id` BIGINT COMMENT '租户ID，唯一标识一个租户',
	`time_tag` STRING COMMENT '时间标签，格式为yyyyMMdd，表示数据统计的日期',
	`total_actual_warehouse_expenses` DECIMAL(38,18) COMMENT '代仓实付费用合计，单位为元',
	`total_warehouse_expenses` DECIMAL(38,18) COMMENT '代仓应付费用合计，单位为元',
	`total_after_sale_warehouse_expenses` DECIMAL(38,18) COMMENT '已到货售后（代仓服务商责任）费用合计，单位为元',
	`total_sales_amount` DECIMAL(38,18) COMMENT '销售金额总计，单位为元',
	`total_sales_amount_wechat_pay` DECIMAL(38,18) COMMENT '销售金额总计（微信支付），单位为元',
	`total_sales_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '销售金额总计（账期加余额支付），单位为元',
	`after_sale_amount_wechat_pay` DECIMAL(38,18) COMMENT '销售售后金额（微信支付），单位为元',
	`after_sale_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '销售售后金额（账期加余额支付），单位为元',
	`deduct_after_sales_amount_wechat_pay` DECIMAL(38,18) COMMENT '扣除售后销售金额（微信支付），单位为元',
	`deduct_after_sales_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '扣除售后销售金额（账期加余额支付），单位为元',
	`delivery_fee_deduct_after_sales_amount` DECIMAL(38,18) COMMENT '剔除售后的订单配送费，单位为元'
) 
COMMENT 'SAAS对账单-代仓账单概要表，记录各租户的代仓费用和销售金额统计信息'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据入库日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SAAS对账单-代仓账单概要表，包含租户的代仓费用、销售金额等统计指标') 
LIFECYCLE 30;