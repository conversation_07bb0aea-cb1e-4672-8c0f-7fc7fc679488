CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_finance_cash_revenue_details_di` (
  `order_no` STRING COMMENT '订单编号，唯一标识一个订单',
  `order_item_id` BIGINT COMMENT '订单项编号，唯一标识订单中的商品项',
  `delivery_path_id` BIGINT COMMENT '配送路径ID，标识具体的配送路线',
  `pay_type` STRING COMMENT '支付方式，枚举值：鲜沐卡、微信支付、智付间联、微信B2B支付、招行支付、小程序支付',
  `service_area` STRING COMMENT '配送仓大区，枚举值：华东、未知、西南、福建、云南、山东、华中、贵州、广西',
  `province` STRING COMMENT '省份，枚举值：浙江、广东、重庆、江苏、福建、云南、上海、山东、四川、湖北、江西、湖南、安徽、贵州、广西壮族自治区',
  `city` STRING COMMENT '城市，枚举值：金华市、杭州市、佛山市、宁波市、重庆市、泰州市、扬州市、苏州市、厦门市、昆明市等58个城市',
  `m_id` BIGINT COMMENT '客户ID，唯一标识客户',
  `mname` STRING COMMENT '客户名称，即客户在系统中的显示名称',
  `realname` STRING COMMENT '品牌的工商注册名称，即法律实体名称',
  `name_remakes` STRING COMMENT '品牌的品牌名称，即市场推广使用的名称',
  `sku` STRING COMMENT '商品SKU编码，唯一标识商品',
  `pd_name` STRING COMMENT '商品名称',
  `category1` STRING COMMENT '商品一级类目，枚举值：鲜果、其他、乳制品',
  `tax_rate` DECIMAL(38,18) COMMENT '税率，小数表示，如0.09表示9%',
  `order_sku_cnt` BIGINT COMMENT '订单商品数量，下单时的商品数量',
  `real_sku_cnt` BIGINT COMMENT '实际送达商品数量，剔除缺货后的实际配送数量',
  `real_total_amt` DECIMAL(38,18) COMMENT '订单实付总价，含税金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '订单应付总价，原始订单金额',
  `total_discount_amt` DECIMAL(38,18) COMMENT '营销优惠金额，各种促销活动的减免金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费金额',
  `out_times_amt` DECIMAL(38,18) COMMENT '超时加单金额，超时配送产生的额外费用',
  `pay_time` DATETIME COMMENT '支付日期时间，格式：yyyy-MM-dd HH:mm:ss',
  `finish_time` DATETIME COMMENT '确认收入日期时间，格式：yyyy-MM-dd HH:mm:ss',
  `revenue_amt` DECIMAL(38,18) COMMENT '确认收入金额，含税金额',
  `revenue_amt_notax` DECIMAL(38,18) COMMENT '确认收入金额，不含税金额',
  `tax_amt` DECIMAL(38,18) COMMENT '税额',
  `unit_cost` DECIMAL(38,18) COMMENT '成本单价，含税单价',
  `unit_cost_notax` DECIMAL(38,18) COMMENT '成本单价，不含税单价',
  `cost` DECIMAL(38,18) COMMENT '成本金额，含税总成本',
  `cost_notax` DECIMAL(38,18) COMMENT '成本金额，不含税总成本',
  `delivery_coupon_amt` DECIMAL(38,18) COMMENT '运费优惠金额',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：平台客户、大客户',
  `remark` STRING COMMENT '订单备注信息',
  `sub_type` BIGINT COMMENT '商品二级性质，枚举值：1-自营-代销不入仓、2-自营-代销入仓、3-自营-经销、4-代仓-代仓',
  `order_status` BIGINT COMMENT '订单状态，枚举值：2-待配送、3-待收货、6-已收货、8-已退款',
  `precision_delivery_fee` DECIMAL(38,18) COMMENT '精准送费用',
  `settle_type` STRING COMMENT '结算类型，枚举值：空字符串、成本结算'
)
COMMENT '财务口径现结收入明细表，记录财务确认的现结收入相关数据，包含订单基本信息、商品信息、金额信息、客户信息等'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '财务口径现结收入明细表，用于财务收入确认和统计分析',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;