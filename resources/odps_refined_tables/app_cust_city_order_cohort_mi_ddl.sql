CREATE TABLE IF NOT EXISTS app_cust_city_order_cohort_mi(
	`month` STRING COMMENT '月份，格式：yyyyMM',
	`cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
	`cust_type` STRING COMMENT '客户行业类型，枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、糖水/水果捞、蛋糕店、西餐披萨、面包蛋糕点心、其他',
	`register_province` STRING COMMENT '注册时省份',
	`register_city` STRING COMMENT '注册时城市',
	`first_order_cust_cnt` BIGINT COMMENT '首购客户数',
	`first_order_real_amt` DECIMAL(38,18) COMMENT '首购客户实付金额',
	`first_order2_cust_cnt` BIGINT COMMENT '首购二次下单客户数',
	`first_order2_real_amt` DECIMAL(38,18) COMMENT '首购二次下单客户实付金额',
	`first_order_daycnt_avg` DECIMAL(38,18) COMMENT '首购客户平均下单天数',
	`first_order_amt_dayavg_1_cust_cnt` BIGINT COMMENT '首购客户天均价0-200元人数',
	`first_order_amt_dayavg_2_cust_cnt` BIGINT COMMENT '首购客户天均价200-400元人数',
	`first_order_amt_dayavg_3_cust_cnt` BIGINT COMMENT '首购客户天均价400-600元人数',
	`first_order_amt_dayavg_4_cust_cnt` BIGINT COMMENT '首购客户天均价600元以上人数',
	`notfirst_order_cust_cnt` BIGINT COMMENT '非首购客户数',
	`notfirst_order_real_amt` DECIMAL(38,18) COMMENT '非首购客户实付金额',
	`notfirst_order2_cust_cnt` BIGINT COMMENT '非首购二次下单客户数',
	`notfirst_order2_real_amt` DECIMAL(38,18) COMMENT '非首购二次下单客户实付金额',
	`notfirst_order_daycnt_avg` DECIMAL(38,18) COMMENT '非首购客户平均下单天数',
	`notfirst_order_amt_dayavg_1_cust_cnt` BIGINT COMMENT '非首购客户天均价0-200元人数',
	`notfirst_order_amt_dayavg_2_cust_cnt` BIGINT COMMENT '非首购客户天均价200-400元人数',
	`notfirst_order_amt_dayavg_3_cust_cnt` BIGINT COMMENT '非首购客户天均价400-600元人数',
	`notfirst_order_amt_dayavg_4_cust_cnt` BIGINT COMMENT '非首购客户天均价600元以上人数',
	`first_order_amt_dayavg_5_cust_cnt` BIGINT COMMENT '首购客户天均价0-50元人数',
	`notfirst_order_amt_dayavg_5_cust_cnt` BIGINT COMMENT '非首购客户天均价0-50元人数',
	`first_order_daycnt_1_cust_cnt` BIGINT COMMENT '首购客户下单1天客户数',
	`first_order_daycnt_2_cust_cnt` BIGINT COMMENT '首购客户下单2天客户数',
	`first_order_daycnt_3_cust_cnt` BIGINT COMMENT '首购客户下单3天客户数',
	`first_order_daycnt_4_cust_cnt` BIGINT COMMENT '首购客户下单>=4天客户数',
	`notfirst_order_daycnt_1_cust_cnt` BIGINT COMMENT '非首购客户下单1天客户数',
	`notfirst_order_daycnt_2_cust_cnt` BIGINT COMMENT '非首购客户下单2天客户数',
	`notfirst_order_daycnt_3_cust_cnt` BIGINT COMMENT '非首购客户下单3天客户数',
	`notfirst_order_daycnt_4_cust_cnt` BIGINT COMMENT '非首购客户下单>=4天客户数'
) 
COMMENT '客户城市复购分析表，按月份、客户团队类型、客户行业类型、注册地域维度统计客户复购行为数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='客户城市复购分析表，统计不同维度下的客户首购和非首购行为指标') 
LIFECYCLE 30;