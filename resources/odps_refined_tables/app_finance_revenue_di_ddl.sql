```sql
CREATE TABLE IF NOT EXISTS app_finance_revenue_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
    `service_area` STRING COMMENT '大区：云南、华东、华中、山东、广西、未知、福建、西南、贵州',
    `warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识，范围2-155',
    `warehouse_name` STRING COMMENT '库存仓名称：昆明总仓、上海总仓、嘉兴总仓、苏州总仓、长沙总仓、青岛总仓、南宁总仓、东莞总仓、东莞冷冻总仓、嘉兴海盐总仓、南京总仓、济南总仓、武汉总仓、福州总仓、华西总仓、重庆总仓、贵阳总仓',
    `cust_team` STRING COMMENT '客户团队类型：平台客户、大客户',
    `category1` STRING COMMENT '商品一级类目：鲜果、乳制品、其他',
    `cash_revenue_amt` DECIMAL(38,18) COMMENT '现结含税收入金额，单位为元',
    `bill_revenue_amt` DECIMAL(38,18) COMMENT '账期含税收入金额，单位为元',
    `cash_revenue_amt_notax` DECIMAL(38,18) COMMENT '现结不含税收入金额，单位为元',
    `bill_revenue_amt_notax` DECIMAL(38,18) COMMENT '账期不含税收入金额，单位为元',
    `revenue_amt` DECIMAL(38,18) COMMENT '含税总收入金额（现结+账期），单位为元',
    `revenue_profit_amt` DECIMAL(38,18) COMMENT '含税毛利润金额，单位为元',
    `revenue_amt_notax` DECIMAL(38,18) COMMENT '不含税总收入金额（现结+账期），单位为元',
    `revenue_profit_amt_notax` DECIMAL(38,18) COMMENT '不含税毛利润金额，单位为元'
)
COMMENT '财务口径收入数据表，包含各区域、仓库、客户类型和商品类别的收入及利润明细数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='财务口径收入数据表，用于财务分析和收入报表生成')
LIFECYCLE 30;
```