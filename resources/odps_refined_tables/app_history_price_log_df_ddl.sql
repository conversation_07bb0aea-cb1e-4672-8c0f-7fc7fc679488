CREATE TABLE IF NOT EXISTS app_history_price_log_df(
    sku STRING COMMENT '商品SKU编码，唯一标识商品，如：631531407046、631531407815等',
    area_no BIGINT COMMENT '城市编号，取值范围：1001-44269',
    price DECIMAL(38,18) COMMENT '商品标准售价，单位：元',
    activity_price DECIMAL(38,18) COMMENT '商品活动价格，单位：元（当无活动时为None）',
    date_key STRING COMMENT '业务日期，格式：yyyyMMdd，表示价格记录对应的业务日期'
)
COMMENT '进货单-商品历史价格表，记录商品在不同城市和日期的价格信息，包括标准售价和活动价格'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据加载日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='商品历史价格记录表，用于追踪商品价格变化和活动定价')
LIFECYCLE 30;