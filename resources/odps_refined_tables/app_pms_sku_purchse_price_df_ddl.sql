CREATE TABLE IF NOT EXISTS app_pms_sku_purchse_price_df(
	statistics_date DATETIME COMMENT '统计日期，格式为年月日时分秒',
	warehouse_no BIGINT COMMENT '仓库编号，取值范围：1-155',
	sku STRING COMMENT 'SKU编码，商品唯一标识',
	type BIGINT COMMENT '抽数类型：1-近7天采购平均价（目前只有1种类型）',
	average_price DECIMAL(38,18) COMMENT '平均价格，保留18位小数精度'
)
COMMENT '历史七天采购品平均价统计表，记录近7天内各SKU在不同仓库的平均采购价格'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='采购价格统计分析表，用于监控和优化采购成本') 
LIFECYCLE 30;