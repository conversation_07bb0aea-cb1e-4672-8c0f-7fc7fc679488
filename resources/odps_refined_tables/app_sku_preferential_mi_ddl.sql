CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_preferential_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `sku_id` STRING COMMENT 'SKU编码，商品最小库存单位的唯一标识',
  `spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格、包装等详细信息',
  `category1` STRING COMMENT '一级类目，商品的主要分类：乳制品、其他',
  `preferential_type` STRING COMMENT '活动类型，取值范围：特价活动、临保活动、奶油卡、销售品类券、售后补偿券、销售现货券、行业活动券、红包、区域拉新券、平台活动券、销售囤货券、销售客情券、其他、销售月活券、换购、满减、区域活动券、配送补偿券',
  `coupon_amt_label` DECIMAL(38,18) COMMENT '营销费用，表示优惠券或营销活动的金额'
)
COMMENT '人群营销结构表，记录商品营销活动和优惠信息'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='人群营销结构表，记录商品营销活动和优惠信息',
  'lifecycle'='30'
);