CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_cust_trade_di` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd',
  `cust_class` STRING COMMENT '客户类型枚举：Mars大客户、平台客户、集团大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（应付总金额/客户数）',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价（应付总金额/订单数）',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率（未到货售后总金额/应付总金额）',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
) 
COMMENT '交易口径KPI指标日汇总表，包含各类客户类型的交易指标统计'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('comment'='交易口径KPI指标日汇总表，按客户类型维度统计交易相关指标')
LIFECYCLE 30;