CREATE TABLE IF NOT EXISTS app_damage_sale_ratio_report_df(
	`damage_date` DATETIME COMMENT '货损日期，格式为年月日时分秒',
	`sku_id` BIGINT COMMENT '商品SKU ID，唯一标识一个具体商品规格',
	`spu_id` BIGINT COMMENT '商品SPU ID，标识同一类商品',
	`xianmu_sku_id` STRING COMMENT '鲜沐SKU ID，鲜沐系统的商品编码',
	`xianmu_spu_id` BIGINT COMMENT '鲜沐SPU ID，鲜沐系统的商品类目编码',
	`name` STRING COMMENT '商品名称',
	`specification` STRING COMMENT '商品规格描述',
	`unit` STRING COMMENT '计量单位，取值范围：袋、箱、包、瓶、件、筐、盒、台、个、块、套、罐、组、桶、份、条、卷等',
	`warehouse_no` BIGINT COMMENT '仓库编号，唯一标识一个仓库',
	`warehouse_name` STRING COMMENT '仓库名称，取值范围包括：广州总仓、嘉兴总仓、南宁总仓、杭州总仓、福州总仓、东莞总仓、上海总仓等',
	`purchaser` STRING COMMENT '采购人员姓名',
	`tenant_id` BIGINT COMMENT '租户ID，标识不同的业务租户',
	`category_id` BIGINT COMMENT '一级类目ID，商品分类标识',
	`damage_quantity` BIGINT COMMENT '货损数量，单位与unit字段对应',
	`damage_amount` DECIMAL(38,18) COMMENT '货损金额，单位为元',
	`outbound_quantity` BIGINT COMMENT '销售出库数量，单位与unit字段对应',
	`outbound_amount` DECIMAL(38,18) COMMENT '销售出库金额，单位为元',
	`back_quantity` BIGINT COMMENT '退货数量，单位与unit字段对应',
	`back_amount` DECIMAL(38,18) COMMENT '退货金额，单位为元',
	`damage_sale_ratio` DECIMAL(38,18) COMMENT '货损比，计算公式为：货损金额/(销售出库金额+退货金额)',
	`time_tag` STRING COMMENT '时间标签，格式为yyyyMMdd，表示年月日',
	`warehouse_service_provider` STRING COMMENT '仓库服务商，取值范围包括：杭州鲜沐科技有限公司、广州肆捌城餐饮管理有限公司、杭州五二兰餐饮管理有限公司、客思服（杭州）科技有限公司、上海文雷餐饮有限公司等'
)
COMMENT '损售比报表，记录商品货损与销售情况的对比分析数据，用于监控和分析商品损耗情况'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment'='损售比分析报表，包含商品货损、销售、退货等关键指标数据',
	'columnar.nested.type'='true'
)
LIFECYCLE 30;