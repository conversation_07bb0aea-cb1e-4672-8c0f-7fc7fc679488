CREATE TABLE IF NOT EXISTS app_check_stock_arrange_df(
    purchase_no STRING COMMENT '采购编号，采购单的唯一标识，如：202507250485846159',
    sku STRING COMMENT '商品SKU编码，商品的唯一标识，如：654602887580',
    arrange_count BIGINT COMMENT '已安排数量，采购单中已安排入库的商品数量',
    task_count BIGINT COMMENT '任务总数，采购单中需要入库的总商品数量'
)
COMMENT '业务数据一致性校验-采购入库对账表，用于校验采购入库数据的完整性和一致性'
PARTITIONED BY (ds STRING COMMENT '分区字段，数据日期，格式为yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment'='业务数据一致性校验-采购入库对账表')
LIFECYCLE 30;