CREATE TABLE IF NOT EXISTS app_kpi_operate_large_category_delivery_di(
	`date` STRING COMMENT '月份，格式为yyyyMMdd，表示年月日',
	`large_area_name` STRING COMMENT '运营服务大区，取值范围：上海大区、南宁大区、成都大区、昆明大区、福州大区、苏南大区、苏州大区、重庆大区、广州大区、杭州大区、武汉大区、贵阳大区、长沙大区、青岛大区',
	`category` STRING COMMENT '商品品类，取值范围：鲜果、乳制品、其他',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
	`marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
	`cost_amt` DECIMAL(38,18) COMMENT '成本金额',
	`origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
	`real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
	`origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
	`real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
	`cust_cnt` BIGINT COMMENT '履约客户数',
	`point_cnt` BIGINT COMMENT '点位数',
	`origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
	`real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
	`timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
	`timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
	`consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
	`consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
	`consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
	`consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
	`consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
	`consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储费',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
	`other_amt` DECIMAL(38,18) COMMENT '其他费',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送费'
) 
COMMENT '运营履约KPI表（平台客户），包含各运营大区、商品品类的履约相关指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='运营履约KPI统计表，按大区和商品品类维度统计履约相关的GMV、费用、利润等核心业务指标') 
LIFECYCLE 30;