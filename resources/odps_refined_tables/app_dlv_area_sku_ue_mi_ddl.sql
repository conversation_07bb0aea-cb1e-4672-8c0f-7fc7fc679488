CREATE TABLE IF NOT EXISTS app_dlv_area_sku_ue_mi(
	`month` STRING COMMENT '月份，格式为yyyyMM，例如：202509表示2025年9月',
	`large_area_id` BIGINT COMMENT '运营服务大区ID，数值型标识',
	`large_area_name` STRING COMMENT '运营服务大区名称，枚举值包括：杭州大区、上海大区、昆明快递大区、广州大区、成都大区、重庆大区、福州大区、长沙大区、南宁大区、昆明大区、苏州大区、贵阳大区、青岛大区、美团和喜茶大区、苏南大区、可可快递服务区、武汉大区、广东一点点快递区域',
	`sku_type` STRING COMMENT '商品类型，枚举值：自营-自营商品，代仓-代仓商品',
	`point_cnt` BIGINT COMMENT '点位数，统计服务网点数量',
	`cust_cnt` BIGINT COMMENT '客户数，统计服务客户数量',
	`order_cnt` BIGINT COMMENT '订单数，统计订单数量',
	`origin_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额，原始应付金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '配送实付总金额，实际支付金额',
	`cost_amt` DECIMAL(38,18) COMMENT '配送成本总金额，总成本金额',
	`deliver_amt` DECIMAL(38,18) COMMENT '运费，运输费用',
	`after_sale_amt` DECIMAL(38,18) COMMENT '售后金额，售后服务产生的费用',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本，仓储相关费用',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，主干线路运输费用',
	`delivery_amt` DECIMAL(38,18) COMMENT '配送成本，配送环节费用',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，商品调拨产生的费用',
	`self_picked_amt` DECIMAL(38,18) COMMENT '自提成本，客户自提产生的费用',
	`other_amt` DECIMAL(38,18) COMMENT '其他成本，其他未分类费用'
)
COMMENT '运营服务大区商品类型UE表，按月份和大区统计各类商品的服务指标和成本数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='运营服务大区商品类型UE分析表，包含各区域各类商品的运营指标和成本明细')
LIFECYCLE 30;