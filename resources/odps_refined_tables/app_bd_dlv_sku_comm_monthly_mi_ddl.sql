```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_bd_dlv_sku_comm_monthly_mi` (
  `months` STRING COMMENT '履约月份，格式：yyyyMM',
  `order_date` STRING COMMENT '下单日期，格式：yyyyMMdd',
  `deliver_date` STRING COMMENT '履约日期，格式：yyyyMMdd',
  `order_source` STRING COMMENT '订单来源，枚举值：鲜沐',
  `point_id` BIGINT COMMENT '点位ID',
  `administrative_city` STRING COMMENT '注册城市名称',
  `city_level` STRING COMMENT '城市分层，枚举值：A/B/C',
  `cust_id` BIGINT COMMENT '客户ID',
  `cust_name` STRING COMMENT '客户名称',
  `brand_id` STRING COMMENT '所属品牌ID，枚举值：-1(无品牌)',
  `brand_alias` STRING COMMENT '所属品牌名称，枚举值：无',
  `admin_type` STRING COMMENT '系统客户类别，枚举值：平台客户',
  `cust_team` STRING COMMENT '客户老分组，枚举值：平台客户',
  `cust_group` STRING COMMENT '客户新分组，枚举值：平台客户',
  `brand_grade` STRING COMMENT '品牌等级，枚举值：无',
  `bd_id` BIGINT COMMENT '业绩归属的BD_ID',
  `bd_name` STRING COMMENT '业绩归属的销售姓名',
  `m1_name` STRING COMMENT 'M1管理者姓名',
  `m2_name` STRING COMMENT 'M2管理者姓名',
  `m3_name` STRING COMMENT 'M3管理者姓名',
  `zone_name` STRING COMMENT '销售区域名称',
  `order_no` STRING COMMENT '订单编号',
  `is_credit_paid` STRING COMMENT '是否账期，枚举值：0(否)',
  `is_self_owned_brand_sku` STRING COMMENT '是否鲜沐自营品牌商品，枚举值：0(否)',
  `spu_no` STRING COMMENT '商品编号',
  `sku_id` STRING COMMENT 'SKU_ID',
  `sku_disc` STRING COMMENT 'SKU规格描述',
  `spu_name` STRING COMMENT '商品名称',
  `spu_id` STRING COMMENT '商品ID',
  `sku_type` STRING COMMENT '商品类型，枚举值：自营经销/全品类',
  `sku_category` STRING COMMENT '商品类目，枚举值：乳制品',
  `comm_category` STRING COMMENT '抽佣结算类目，枚举值：乳制品',
  `category_comm_rate` DECIMAL(38,18) COMMENT '类目抽佣比例',
  `sku_dlv_ori_amt` DECIMAL(38,18) COMMENT 'sku履约应付金额',
  `sku_dlv_real_amt` DECIMAL(38,18) COMMENT 'sku履约实付金额',
  `sku_cnt` BIGINT COMMENT 'sku履约件数',
  `item_profit_amt` DECIMAL(38,18) COMMENT '自营商品毛利润',
  `pid_dlv_amt` DECIMAL(38,18) COMMENT '当天履约实付GMV',
  `pid_spu_num` BIGINT COMMENT '当天履约商品款数',
  `dlv_gmv_rate` DECIMAL(38,18) COMMENT 'GMV配送系数',
  `dlv_spu_rate` DECIMAL(38,18) COMMENT 'SPU配送系数',
  `dlv_comm_rate` DECIMAL(38,18) COMMENT '订单配送系数：GMV配送系数+SPU配送系数',
  `fruit_comm_amt_tmp` DECIMAL(38,18) COMMENT '鲜果类目佣金(未乘达成系数)',
  `un_fruit_comm_amt_tmp` DECIMAL(38,18) COMMENT '非鲜果类目佣金(未乘达成系数)',
  `sku_comm_amout_before` DECIMAL(38,18) COMMENT 'sku维度抽佣金额_不考虑品牌毛利率',
  `brand_profit_amt` DECIMAL(38,18) COMMENT '品牌月累自营毛利润',
  `brand_dlv_ori_amt` DECIMAL(38,18) COMMENT '品牌月累自营履约应付GMV',
  `brand_profit_rate` DECIMAL(38,18) COMMENT '品牌月累自营品毛利率',
  `sku_comm_amout` DECIMAL(38,18) COMMENT 'sku维度抽佣金额：品牌毛利润小于12%则为0',
  `bd_m1` STRING COMMENT 'BD所属M1管理者姓名',
  `bd_m2` STRING COMMENT 'BD所属M2管理者姓名',
  `bd_m3` STRING COMMENT 'BD所属M3管理者姓名',
  `bd_work_zone` STRING COMMENT 'BD所在销售区域名称',
  `bd_work_city` STRING COMMENT 'BD所在城市名称',
  `p_rate` DECIMAL(38,18) COMMENT 'P系数',
  `fruit_rate` DECIMAL(38,18) COMMENT '鲜果目标系数',
  `brand_account_financial_warning` STRING COMMENT '财务账期预警_红黄，枚举值：红/黄/None'
) 
COMMENT '月度算薪_销售SKU维度佣金核算表，用于销售人员的月度佣金计算和核算'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '数据日期，分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='月度算薪_销售SKU维度佣金核算表，包含销售订单、商品信息、佣金计算等相关数据',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```