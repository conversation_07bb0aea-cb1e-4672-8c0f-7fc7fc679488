CREATE TABLE IF NOT EXISTS app_saas_brand_sku_delivery_wi(
	year STRING COMMENT '年份，格式：YYYY',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
	sunday STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
	brand_alias STRING COMMENT '品牌名称，枚举值包括：榴莲嘟嘟、益禾堂、乳果说茶饮、爆珠公·老红糖珍珠鲜奶茶、遇见村上订货、蔡小甜、裕蘭茶楼等',
	sku_id STRING COMMENT '商品SKU ID，唯一标识符',
	title STRING COMMENT '商品标题',
	specification STRING COMMENT '商品规格描述',
	category1 STRING COMMENT '后台一级类目，枚举值包括：饮料、新鲜水果、水果制品、乳制品、食用油丨油脂及制品、新鲜蔬菜、饮品原料、谷物制品、成品原料、蔬菜制品、糖丨糖制品、饼干丨糖果丨可可豆制品',
	delivery_gmv DECIMAL(38,18) COMMENT '履约GMV，商品销售金额',
	cost_amt DECIMAL(38,18) COMMENT '商品成本金额'
)
COMMENT 'SaaS利润数据表现表（仅为鲜沐自营数据），包含品牌商品维度的周度履约GMV和成本数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：YYYYMMDD（年月日）')
STORED AS ALIORC
TBLPROPERTIES ('comment'='SaaS利润数据表现表，用于分析品牌商品的周度销售表现和利润情况')
LIFECYCLE 30;