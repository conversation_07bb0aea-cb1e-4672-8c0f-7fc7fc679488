CREATE TABLE IF NOT EXISTS app_kpi_sku_manage_honour_di(
	`date` STRING COMMENT '日期，格式：yyyyMMdd',
	`manage_type` STRING COMMENT '商品经营类型，取值范围：自营、代售、代仓',
	`origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额(gmv)',
	`real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
	`origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率',
	`real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率',
	`preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
	`refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
	`after_sale_received_order_cnt` BIGINT COMMENT '已到货售后订单数',
	`cust_cnt` BIGINT COMMENT '客户数',
	`cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价',
	`order_cnt` BIGINT COMMENT '订单数',
	`point_cnt` BIGINT COMMENT '点位数',
	`point_out_rate` DECIMAL(38,18) COMMENT '外区点位占比',
	`point_in_rate` DECIMAL(38,18) COMMENT '内区点位占比',
	`inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额',
	`inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额',
	`damage_amt` DECIMAL(38,18) COMMENT '货损总金额',
	`damage_rate` DECIMAL(38,18) COMMENT '货损率',
	`replenish_out_amt` DECIMAL(38,18) COMMENT '补发出库总金额',
	`return_in_amt` DECIMAL(38,18) COMMENT '退货入库总金额',
	`sku_cnt` BIGINT COMMENT 'sku数量',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
	`other_amt` DECIMAL(38,18) COMMENT '其他成本',
	`point_day_cnt` BIGINT COMMENT '均日点位数',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费'
) 
COMMENT '履约口径商品经营维度KPI指标日汇总表，包含商品经营相关的各项财务和运营指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商品经营KPI指标日度汇总数据表') 
LIFECYCLE 30;