```sql
CREATE TABLE IF NOT EXISTS app_cust_brand_alias_category_performance_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的业务日期',
    `brand_alias` STRING COMMENT '品牌别名，如：GIGI LUCKY舒芙蕾、GOODSLOVE CAFE、Keke可可同学订货商城等',
    `category` STRING COMMENT '品类，枚举值：鲜果-新鲜水果类, 乳制品-奶制品类, 其他-其他商品类别',
    `order_origin_amt` DECIMAL(38,18) COMMENT '交易应付总金额，单位：元',
    `order_real_amt` DECIMAL(38,18) COMMENT '交易实付总金额，单位：元',
    `delivery_origin_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位：元',
    `delivery_real_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位：元',
    `delivery_cash_real_amt` DECIMAL(38,18) COMMENT '履约现结实付总金额，单位：元',
    `delivery_bill_real_amt` DECIMAL(38,18) COMMENT '履约账期实付总金额，单位：元',
    `delivery_cost_amt` DECIMAL(38,18) COMMENT '履约商品成本金额，单位：元',
    `delivery_real_gross` DECIMAL(38,18) COMMENT '履约实付毛利润，单位：元',
    `delivery_real_gross_rate` DECIMAL(38,18) COMMENT '履约实付毛利率，小数形式表示的比例值',
    `delivery_cust_cnt` BIGINT COMMENT '履约客户数，统计期内完成履约的客户数量',
    `delivery_point_cnt` BIGINT COMMENT '履约累计点位数，统计期内完成履约的网点数量',
    `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，单位：元',
    `after_sale_rate` DECIMAL(38,18) COMMENT '售后比例，已到货售后金额/履约实付GMV，小数形式表示的比例值'
) 
COMMENT '大客户品牌+品类粒度监控表，按品牌别名和品类维度统计交易、履约、售后等业务指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据的技术分区日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='大客户品牌+品类粒度业务监控表，用于监控各品牌品类维度的交易履约表现') 
LIFECYCLE 30;
```