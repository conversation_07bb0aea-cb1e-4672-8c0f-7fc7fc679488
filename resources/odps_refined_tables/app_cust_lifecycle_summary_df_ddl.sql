CREATE TABLE IF NOT EXISTS app_cust_lifecycle_summary_df(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
	`life_cycle_detail` STRING COMMENT '生命周期标签（细），取值范围：A1、A2、A3、B1、B2、L1、L2、L3、N0、N1、N2、S1、S2、W',
	`cust_cnt` BIGINT COMMENT '客户数，统计该生命周期标签下的客户数量',
	`order_real_amt_30d` DECIMAL(38,18) COMMENT '近30天交易实付金额，统计客户在近30天内的实际支付金额',
	`order_spu_cnt_30d_avg` DECIMAL(38,18) COMMENT '平均近30天订单去重spu数，统计客户在近30天内平均购买的商品品类数量',
	`order_days_30d_avg` DECIMAL(38,18) COMMENT '平均近30天下单频次（下单天数），统计客户在近30天内平均下单的天数',
	`new_cust_cnt` BIGINT COMMENT '当日新增客户数，统计当天新增进入该生命周期标签的客户数量',
	`lost_cust_cnt` BIGINT COMMENT '流失客户数，统计当天从该生命周期标签流失的客户数量'
) 
COMMENT '客户生命周期汇总表，按日期、客户团队类型和生命周期标签维度统计客户数量、交易行为和客户流转情况'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的业务日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='客户生命周期行为汇总表，用于分析不同客户团队在各生命周期阶段的客户数量、交易表现和客户流转趋势') 
LIFECYCLE 30;