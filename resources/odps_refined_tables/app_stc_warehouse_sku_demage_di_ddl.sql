```sql
CREATE TABLE IF NOT EXISTS app_stc_warehouse_sku_demage_di(
    warehouse_no BIGINT COMMENT '库存仓编号，数值型标识',
    warehouse_name STRING COMMENT '库存仓名称，枚举值包括：上海总仓、嘉兴总仓、华西总仓、重庆总仓、福州总仓、长沙总仓、南宁总仓、昆明总仓、贵阳总仓、青岛总仓、东莞总仓、虚拟仓库、东莞冷冻总仓、广东省内虚拟仓库、嘉兴海盐总仓、南京总仓、自建仓库、济南总仓、武汉总仓、红塔仓库、普冷武汉仓、绝配北京仓、北京总仓、汕头仓、苏州总仓、广州总部仓',
    purchaser STRING COMMENT '采购人姓名，如：谷人说订货小站管理员、周碧灵、财务等',
    batch_no STRING COMMENT '批次编号，业务唯一标识',
    sku_id STRING COMMENT 'SKU编号，商品唯一标识',
    category STRING COMMENT '商品分类，枚举值：鲜果、标品、其他、乳制品',
    spu_name STRING COMMENT '商品名称（SPU名称）',
    sku_disc STRING COMMENT '商品描述，包含规格包装信息',
    weight STRING COMMENT '规格重量描述',
    sku_type STRING COMMENT 'SKU类型，枚举值：自营、代仓',
    sku_property STRING COMMENT 'SKU属性，枚举值：核心、非核心',
    unit_cost DECIMAL(38,18) COMMENT '单价，单位成本金额',
    damage_cnt BIGINT COMMENT '货损数量，整数值',
    damage_amt DECIMAL(38,18) COMMENT '货损总金额',
    sale_cnt BIGINT COMMENT '销售出库数量，整数值',
    sale_amt DECIMAL(38,18) COMMENT '销售出库金额',
    store_quantity BIGINT COMMENT '当日库存数量，整数值'
)
COMMENT '仓库+SKU货损数据表，记录各仓库SKU的货损情况、销售情况和库存信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='仓库SKU货损明细表，用于分析商品货损情况和库存管理',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```