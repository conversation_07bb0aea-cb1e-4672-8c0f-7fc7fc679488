```sql
CREATE TABLE IF NOT EXISTS app_finance_saas_revenue_di(
    `date` STRING COMMENT '业务日期，格式为yyyyMMdd，表示数据对应的业务发生日期',
    `service_area` STRING COMMENT '大区：华东/华中/广西/福建/西南/未知',
    `warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识，用于唯一标识仓库',
    `warehouse_name` STRING COMMENT '库存仓名称：上海总仓/嘉兴总仓/长沙总仓/南宁总仓/东莞总仓/东莞冷冻总仓/嘉兴海盐总仓/南京总仓/福州总仓/华西总仓/重庆总仓',
    `cust_team` STRING COMMENT '客户团队类型：平台客户/大客户',
    `category1` STRING COMMENT '商品一级类目：鲜果/乳制品/其他',
    `cash_revenue_amt` DECIMAL(38,18) COMMENT '现结含税收入金额，包含税款的现结交易收入',
    `bill_revenue_amt` DECIMAL(38,18) COMMENT '账期含税收入金额，包含税款的账期交易收入',
    `cash_revenue_amt_notax` DECIMAL(38,18) COMMENT '现结不含税收入金额，扣除税款的现结交易收入',
    `bill_revenue_amt_notax` DECIMAL(38,18) COMMENT '账期不含税收入金额，扣除税款的账期交易收入',
    `revenue_amt` DECIMAL(38,18) COMMENT '含税总收入金额，包含税款的总收入（现结+账期）',
    `revenue_profit_amt` DECIMAL(38,18) COMMENT '含税毛利润金额，包含税款的毛利润',
    `revenue_amt_notax` DECIMAL(38,18) COMMENT '不含税总收入金额，扣除税款的总收入（现结+账期）',
    `revenue_profit_amt_notax` DECIMAL(38,18) COMMENT '不含税毛利润金额，扣除税款的毛利润'
)
COMMENT 'SaaS财务口径收入数据表，包含按大区、仓库、客户团队、商品类目维度的收入及利润数据'
PARTITIONED BY (`ds` STRING COMMENT '数据分区字段，格式为yyyyMMdd，表示数据加载日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='SaaS财务收入分析表，用于财务收入统计和业务分析',
    'lifecycle'='30'
);
```