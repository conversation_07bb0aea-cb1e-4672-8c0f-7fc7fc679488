CREATE TABLE IF NOT EXISTS app_saas_merchant_store_purchase_month_di(
	`tenant_id` BIGINT COMMENT '租户ID',
	`time_tag` STRING COMMENT '时间标签，格式为yyyyMMdd（当月1号），表示统计月份',
	`pay_type` BIGINT COMMENT '支付方式：1-现结，2-账期',
	`store_in_operation_num` BIGINT COMMENT '经营中门店总数',
	`direct_store_in_operation_num` BIGINT COMMENT '经营中直营门店数',
	`join_store_in_operation_num` BIGINT COMMENT '经营中加盟门店数',
	`managed_store_in_operation_num` BIGINT COMMENT '经营中托管门店数',
	`purchased_store_num` BIGINT COMMENT '采购门店总数',
	`purchased_direct_store_num` BIGINT COMMENT '采购直营门店数',
	`purchased_join_store_num` BIGINT COMMENT '采购加盟门店数',
	`purchased_managed_store_num` BIGINT COMMENT '采购托管门店数'
)
COMMENT 'SaaS门店采购概况表（月维度），统计各租户门店经营和采购情况'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = 'SaaS门店采购月维度统计表，包含门店经营状态和采购情况的月度汇总数据',
	'columnar.nested.type' = 'true'
)
LIFECYCLE 30;