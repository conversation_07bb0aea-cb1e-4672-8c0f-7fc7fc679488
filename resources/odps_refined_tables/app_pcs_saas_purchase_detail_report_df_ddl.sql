```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_pcs_saas_purchase_detail_report_df`(
	`purchase_date` DATETIME COMMENT '采购日期，格式为年月日时分秒',
	`purchase_no` STRING COMMENT '采购批次编号，唯一标识一次采购',
	`purchaser` STRING COMMENT '采购人姓名或账号',
	`warehouse_no` BIGINT COMMENT '仓库编号，数字标识',
	`warehouse_name` STRING COMMENT '仓库名称',
	`purchase_status` STRING COMMENT '采购单状态：全部入库、部分入库、全未入库',
	`sku_id` BIGINT COMMENT 'SKU ID，商品最小库存单位标识',
	`spu_id` BIGINT COMMENT 'SPU ID，商品标准单位标识',
	`xianmu_sku` STRING COMMENT '鲜沐SKU编码',
	`xianmu_spu_id` BIGINT COMMENT '鲜沐SPU ID',
	`name` STRING COMMENT '商品名称',
	`supplier_id` BIGINT COMMENT '供应商ID，数字标识',
	`supplier` STRING COMMENT '供应商名称',
	`specification` STRING COMMENT '商品规格描述',
	`unit` STRING COMMENT '规格单位：包、盒、箱、袋、桶、罐、瓶、组、块、件、个、张、台、条、捆、卷、顶、斤、付、套、份、筐等',
	`inbound_status` STRING COMMENT '入库状态：全部入库、部分入库、全未入库',
	`inbound_date` DATETIME COMMENT '入库时间，格式为年月日时分秒',
	`purchase_quantity` BIGINT COMMENT '采购数量',
	`purchase_amount` DECIMAL(38,18) COMMENT '采购金额',
	`inbound_quantity` BIGINT COMMENT '实际入库数量',
	`inbound_amount` DECIMAL(38,18) COMMENT '实际入库金额',
	`tenant_id` BIGINT COMMENT '租户ID',
	`category_id` BIGINT COMMENT '三级类目ID',
	`time_tag` STRING COMMENT '时间标签，格式为yyyyMMdd',
	`warehouse_service_provider` STRING COMMENT '仓库服务商名称',
	`back_amount` DECIMAL(38,18) COMMENT '退货金额，批次+sku累计退货金额',
	`back_quantity` BIGINT COMMENT '退货数量，批次+sku累计退货数量',
	`brand_name` STRING COMMENT '品牌名称',
	`price` DECIMAL(38,18) COMMENT '采购单价'
)
COMMENT 'SaaS采购明细表，记录采购订单的详细信息，包括商品信息、供应商信息、仓库信息、采购状态和金额等'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = 'SaaS采购明细结果表，用于采购业务数据分析和报表生成',
	'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```