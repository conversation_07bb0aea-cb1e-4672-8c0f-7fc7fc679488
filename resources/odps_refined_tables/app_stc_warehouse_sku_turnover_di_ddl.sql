CREATE TABLE IF NOT EXISTS app_stc_warehouse_sku_turnover_di(
	`warehouse_no` BIGINT COMMENT '库存仓编号，数值型标识',
	`warehouse_name` STRING COMMENT '库存仓名称，枚举值包括：上海总仓、山东总仓、嘉兴总仓、华西总仓、重庆总仓、福州总仓、长沙总仓、南宁总仓、昆明总仓、苏州总仓、贵阳总仓、青岛总仓、东莞总仓、美团优选杭州一仓、美团优选杭州二仓、ljj杭州自营仓、美团优选上海仓、美团虚拟代下单总仓、虚拟仓库、自营奶虚拟仓、多多买菜杭州一仓、美团代加工虚拟仓、东莞冷冻总仓、嘉兴海盐总仓、南京总仓、多多买菜宁波仓、多多买菜温州仓、多多买菜揭阳仓、叮咚买菜昆山仓、济南总仓、武汉总仓',
	`sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
	`sku_type` STRING COMMENT 'SKU类型，枚举值：自营、代仓',
	`spu_id` BIGINT COMMENT 'SPU ID，商品品类ID',
	`spu_no` STRING COMMENT 'SPU编号，商品品类编号',
	`spu_name` STRING COMMENT '商品名称',
	`sku_disc` STRING COMMENT '商品描述，包含规格包装信息',
	`category1` STRING COMMENT '一级类目，枚举值：乳制品、其他、鲜果',
	`category2` STRING COMMENT '二级类目，枚举值包括：乳制品、其他、饮料、调味品、包材、饼干丨糖果丨可可豆制品、成品原料、谷物制品、水果制品、茶制品、饮品原料、仓配-物资、新鲜水果、糖丨糖制品、无、蔬菜制品、糕点丨面包、坚果制品、酒、新鲜蔬菜、帆台一级类目、肉丨肉制品、电器、鲜果礼盒、POP—鲜果、食用油丨油脂及制品、食品添加剂、蛋丨蛋制品、海鲜｜水产品｜制品',
	`sku_property` STRING COMMENT 'SKU属性，枚举值：常规、拆包、临保、破袋、空值',
	`sku_life` STRING COMMENT 'SKU生命周期状态，枚举值：使用中、已删除、上新处理中',
	`sku_core_type` STRING COMMENT 'SKU核心类型，枚举值：非核心、核心',
	`sale_quantity` BIGINT COMMENT '当日销量数量',
	`sale_cost` DECIMAL(38,18) COMMENT '当日销量成本，精确到小数点后18位',
	`inventory_quantity` BIGINT COMMENT '当日库存数量',
	`inventory_cost` DECIMAL(38,18) COMMENT '当日库存成本，精确到小数点后18位',
	`nearest_7_days_sale_quantity` BIGINT COMMENT '近7天销量数量',
	`nearest_7_days_total_sale_cost` DECIMAL(38,18) COMMENT '近7天总销量成本，精确到小数点后18位',
	`nearest_7_days_inventory_quantity` BIGINT COMMENT '近7天库存数量',
	`nearest_7_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近7天总库存成本，精确到小数点后18位',
	`nearest_30_days_sale_quantity` BIGINT COMMENT '近30天销量数量',
	`nearest_30_days_total_sale_cost` DECIMAL(38,18) COMMENT '近30天总销量成本，精确到小数点后18位',
	`nearest_30_days_inventory_quantity` BIGINT COMMENT '近30天库存数量',
	`nearest_30_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近30天总库存成本，精确到小数点后18位',
	`month_sale_quality` BIGINT COMMENT '当月销量数量',
	`month_total_sale_cost` DECIMAL(38,18) COMMENT '当月总销量成本，精确到小数点后18位',
	`month_inventory_quantity` BIGINT COMMENT '当月库存数量',
	`month_total_inventory_cost` DECIMAL(38,18) COMMENT '当月总库存成本，精确到小数点后18位',
	`nearest_14_days_inventory_quantity` BIGINT COMMENT '近14天库存数量',
	`nearest_14_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近14天总库存成本，精确到小数点后18位',
	`nearest_14_days_sale_quantity` BIGINT COMMENT '近14天销量数量',
	`nearest_14_days_total_sale_cost` DECIMAL(38,18) COMMENT '近14天总销量成本，精确到小数点后18位'
) 
COMMENT '库存仓+SKU维度近30天库存周转分析表，包含各时间维度的销量和库存数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='库存周转分析事实表，用于监控和分析各仓库SKU的库存周转情况') 
LIFECYCLE 30;