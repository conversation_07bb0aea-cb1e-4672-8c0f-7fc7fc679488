CREATE TABLE IF NOT EXISTS app_stc_warehouse_category_kpi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
	`warehouse_no` BIGINT COMMENT '仓库编号，唯一标识一个仓库',
	`warehouse_name` STRING COMMENT '仓库名称，如：东莞总仓、嘉兴水果批发总仓等',
	`category` STRING COMMENT '商品类目：鲜果-新鲜水果类，标品-标准化商品类',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，订单原始总金额（含优惠前）',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，用户实际支付金额（含优惠后）',
	`after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额，所有售后订单的金额总和',
	`after_sale_amt_check` DECIMAL(38,18) COMMENT '售后金额品控责任，品控部门负责的售后金额',
	`damage_cnt` BIGINT COMMENT '货损总数量，所有责任方造成的货损数量总和',
	`damage_amt` DECIMAL(38,18) COMMENT '货损总金额，所有责任方造成的货损金额总和',
	`damage_cnt_wah` BIGINT COMMENT '货损数量_仓配责任，仓库配送环节造成的货损数量',
	`damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额_仓配责任，仓库配送环节造成的货损金额',
	`damage_cnt_pur` BIGINT COMMENT '货损数量_采购责任，采购环节造成的货损数量',
	`damage_amt_pur` DECIMAL(38,18) COMMENT '货损金额_采购责任，采购环节造成的货损金额',
	`damage_cnt_opr` BIGINT COMMENT '货损数量_运营责任，运营环节造成的货损数量',
	`damage_amt_opr` DECIMAL(38,18) COMMENT '货损金额_运营责任，运营环节造成的货损金额',
	`damage_cnt_oth` BIGINT COMMENT '货损数量_其他责任，其他责任方造成的货损数量',
	`damage_amt_oth` DECIMAL(38,18) COMMENT '货损金额_其他责任，其他责任方造成的货损金额',
	`sale_cnt` BIGINT COMMENT '销售数量，当日成功销售的商品数量',
	`sale_amt` DECIMAL(38,18) COMMENT '销售金额，当日成功销售的商品金额',
	`test_cnt` BIGINT COMMENT '抽检数量，当日抽检的商品数量',
	`qualified_cnt` BIGINT COMMENT '合格数量，抽检中合格的商品数量',
	`check_cnt` BIGINT COMMENT '货检数量，当日进行货检的商品数量',
	`inbound_cnt` BIGINT COMMENT '入库数量，当日入库的商品数量'
) 
COMMENT '品控KPI统计表，按仓库和商品类目维度统计品控相关指标，包括货损、售后、销售等数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据统计日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='品控KPI日粒度统计表，用于监控和分析各仓库品控表现') 
LIFECYCLE 30;