CREATE TABLE IF NOT EXISTS app_log_mall_search_skey_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd',
    `cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
    `cust_type` STRING COMMENT '客户行业类型，取值范围：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他、水果/果切/榨汁店',
    `life_cycle` STRING COMMENT '生命周期标签（粗），取值范围：稳定期、导入期、准流失期、已流失期、成长期、新人期、沉默期、适应期',
    `life_cycle_detail` STRING COMMENT '生命周期标签（细），取值范围：S2、S1、N0、L2、L3、A1、A2、A3、B1、B2、N1、N2、L1、W',
    `register_province` STRING COMMENT '注册时省份名称',
    `register_city` STRING COMMENT '注册时城市名称',
    `register_area` STRING COMMENT '注册时区县名称',
    `city_id` BIGINT COMMENT '运营服务区ID',
    `city_name` STRING COMMENT '运营服务区名称',
    `large_area_id` BIGINT COMMENT '运营服务大区ID',
    `large_area_name` STRING COMMENT '运营服务大区名称，取值范围：上海大区、杭州大区、长沙大区、重庆大区、福州大区、武汉大区、广州大区、苏州大区、青岛大区、苏南大区、南宁大区、成都大区、昆明大区、可可快递服务区、贵阳大区、昆明快递大区',
    `module_name` STRING COMMENT '模块名称，目前取值均为"其他"',
    `skey` STRING COMMENT '搜索词',
    `pv` BIGINT COMMENT '页面浏览量',
    `uv` BIGINT COMMENT '独立访客数'
)
COMMENT '商城搜索词流量分析表，用于分析商城搜索词的流量情况，包含客户属性、地域信息和搜索行为数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='商城搜索词流量分析表',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;