CREATE TABLE IF NOT EXISTS app_finance_terminal_quantity_di(
    `service_area` STRING COMMENT '大区，取值范围：华东、西南、福建、华中、广西',
    `warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识',
    `warehouse_name` STRING COMMENT '库存仓名，取值范围：上海总仓、嘉兴总仓、华西总仓、重庆总仓、合肥进口总仓、福州总仓、长沙总仓、南宁总仓',
    `sku` STRING COMMENT 'SKU，商品唯一编码',
    `pd_name` STRING COMMENT '商品名',
    `quantity` BIGINT COMMENT '期末库存量，数值型',
    `cost_amt` DECIMAL(38,18) COMMENT '期末总金额，含税金额，数值型',
    `date_flag` STRING COMMENT '日期标识，格式为yyyyMMdd，表示年月日',
    `category1` STRING COMMENT '商品一级类目，取值范围：其他、乳制品、鲜果',
    `cost_amt_notax` DECIMAL(38,18) COMMENT '期末总金额(不含税)，数值型',
    `sub_type` BIGINT COMMENT '商品二级性质，枚举类型：1-自营-代销不入仓、2-自营-代销入仓、3-自营-经销、4-代仓-代仓',
    `settle_type` STRING COMMENT '结算类型，取值范围：空字符串、成本结算'
)
COMMENT '财务口径期末库存表，记录各仓库商品的期末库存情况和成本金额'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '财务口径期末库存表，包含大区、仓库、商品信息及库存金额数据',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;