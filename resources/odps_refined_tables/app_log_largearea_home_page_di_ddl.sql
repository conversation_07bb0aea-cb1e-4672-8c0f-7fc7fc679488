CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_largearea_home_page_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd',
  `largearea_no` STRING COMMENT '运营大区ID，取值范围：1,2,5,14,24,29,38,50,70,71,72,74,75,82,84,89,91,93,未知',
  `largearea_name` STRING COMMENT '运营大区名称，取值范围：杭州大区,广州大区,上海大区,成都大区,重庆大区,福州大区,昆明快递大区,长沙大区,南宁大区,昆明大区,苏州大区,贵阳大区,青岛大区,柠季快递大区,苏南大区,可可快递服务区,武汉大区,广东一点点快递区域,未知',
  `cust_type` STRING COMMENT '客户业态，取值范围：其他,咖啡,烘焙,茶饮',
  `home_page_uv` BIGINT COMMENT '首页独立访客数',
  `home_page_pv` BIGINT COMMENT '首页页面浏览量',
  `search_uv` BIGINT COMMENT '搜索功能使用独立访客数',
  `search_pv` BIGINT COMMENT '搜索功能使用页面浏览量',
  `classification_uv` BIGINT COMMENT '分类功能使用独立访客数',
  `classification_pv` BIGINT COMMENT '分类功能使用页面浏览量',
  `banner_uv` BIGINT COMMENT '顶部Banner独立访客数',
  `banner_pv` BIGINT COMMENT '顶部Banner页面浏览量',
  `waist_banner_uv` BIGINT COMMENT '腰部Banner独立访客数',
  `waist_banner_pv` BIGINT COMMENT '腰部Banner页面浏览量',
  `carousel_banner_uv` BIGINT COMMENT '轮播Banner独立访客数',
  `carousel_banner_pv` BIGINT COMMENT '轮播Banner页面浏览量',
  `activity_uv` BIGINT COMMENT '活动专区独立访客数',
  `activity_pv` BIGINT COMMENT '活动专区页面浏览量',
  `fresh_artners_uv` BIGINT COMMENT '鲜拍档专区独立访客数',
  `fresh_artners_pv` BIGINT COMMENT '鲜拍档专区页面浏览量',
  `special_uv` BIGINT COMMENT '特价活动专区独立访客数',
  `special_pv` BIGINT COMMENT '特价活动专区页面浏览量',
  `temporary_uv` BIGINT COMMENT '临保商品专区独立访客数',
  `temporary_pv` BIGINT COMMENT '临保商品专区页面浏览量',
  `boutique_uv` BIGINT COMMENT '精品优选专区独立访客数',
  `boutique_pv` BIGINT COMMENT '精品优选专区页面浏览量',
  `common_recommend_uv` BIGINT COMMENT '常用商品推荐独立访客数',
  `common_recommend_pv` BIGINT COMMENT '常用商品推荐页面浏览量',
  `recommend_uv` BIGINT COMMENT '商品推荐独立访客数',
  `recommend_pv` BIGINT COMMENT '商品推荐页面浏览量',
  `cust_flag` STRING COMMENT '是否老客标识，取值范围：是,否',
  `bottom_nav_pv` BIGINT COMMENT '底部导航栏总页面浏览量',
  `bottom_nav_uv` BIGINT COMMENT '底部导航栏总独立访客数',
  `category_pv` BIGINT COMMENT '底部导航栏-分类页面浏览量',
  `procurement_assistant_pv` BIGINT COMMENT '底部导航栏-采购助手页面浏览量',
  `shopping_cart_pv` BIGINT COMMENT '底部导航栏-购物车页面浏览量',
  `personal_center_pv` BIGINT COMMENT '底部导航栏-个人中心页面浏览量',
  `category_uv` BIGINT COMMENT '底部导航栏-分类独立访客数',
  `procurement_assistant_uv` BIGINT COMMENT '底部导航栏-采购助手独立访客数',
  `shopping_cart_uv` BIGINT COMMENT '底部导航栏-购物车独立访客数',
  `personal_center_uv` BIGINT COMMENT '底部导航栏-个人中心独立访客数',
  `category_tankeng_pv` BIGINT COMMENT '分类弹坑总页面浏览量',
  `category_tankeng_uv` BIGINT COMMENT '分类弹坑总独立访客数',
  `fresh_fruit_pv` BIGINT COMMENT '分类弹坑-鲜果页面浏览量',
  `wangyou_tavern_pv` BIGINT COMMENT '分类弹坑-忘忧酒馆页面浏览量',
  `dairy_products_pv` BIGINT COMMENT '分类弹坑-乳制品页面浏览量',
  `coffee_pv` BIGINT COMMENT '分类弹坑-咖啡页面浏览量',
  `baking_supplies_pv` BIGINT COMMENT '分类弹坑-烘培辅料页面浏览量',
  `frozen_cakes_pv` BIGINT COMMENT '分类弹坑-冷冻蛋糕页面浏览量',
  `bar_supplies_pv` BIGINT COMMENT '分类弹坑-水吧辅料页面浏览量',
  `sugar_syrup_pv` BIGINT COMMENT '分类弹坑-糖|糖浆页面浏览量',
  `western_ingredients_pv` BIGINT COMMENT '分类弹坑-西餐辅料页面浏览量',
  `packaging_materials_pv` BIGINT COMMENT '分类弹坑-包材页面浏览量',
  `fresh_fruit_uv` BIGINT COMMENT '分类弹坑-鲜果独立访客数',
  `wangyou_tavern_uv` BIGINT COMMENT '分类弹坑-忘忧酒馆独立访客数',
  `dairy_products_uv` BIGINT COMMENT '分类弹坑-乳制品独立访客数',
  `coffee_uv` BIGINT COMMENT '分类弹坑-咖啡独立访客数',
  `baking_supplies_uv` BIGINT COMMENT '分类弹坑-烘培辅料独立访客数',
  `frozen_cakes_uv` BIGINT COMMENT '分类弹坑-冷冻蛋糕独立访客数',
  `bar_supplies_uv` BIGINT COMMENT '分类弹坑-水吧辅料独立访客数',
  `sugar_syrup_uv` BIGINT COMMENT '分类弹坑-糖|糖浆独立访客数',
  `western_ingredients_uv` BIGINT COMMENT '分类弹坑-西餐辅料独立访客数',
  `packaging_materials_uv` BIGINT COMMENT '分类弹坑-包材独立访客数',
  `recipe_market_pv` BIGINT COMMENT '配方集市页面浏览量',
  `recipe_market_uv` BIGINT COMMENT '配方集市独立访客数',
  `home_page_ai_assistant_pv` BIGINT COMMENT '首页AI助手页面浏览量',
  `home_page_ai_assistant_uv` BIGINT COMMENT '首页AI助手独立访客数'
) 
COMMENT '首页各模块流量统计表，按运营大区、客户业态、新老客维度统计首页各功能模块的UV/PV数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='首页各模块流量统计表，包含首页各功能模块的UV/PV数据，用于分析用户行为和页面流量分布',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;