```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_wms_warehouse_storage_statistics_di`(
    `settle_date` DATETIME COMMENT '结算日，格式为年月日时分秒',
    `warehouse_no` BIGINT COMMENT '仓编号（仅鲜沐仓），取值范围：2-155',
    `warehouse_name` STRING COMMENT '仓库名，枚举值包括：重庆总仓、武汉总仓、南宁总仓、上海总仓、长沙总仓、华西总仓、青岛总仓、嘉兴水果批发总仓、福州总仓、嘉兴海盐总仓、苏州总仓、东莞总仓、贵阳总仓、济南总仓、东莞冷冻总仓、昆明总仓、南京总仓、嘉兴总仓、美团优选杭州一仓、美团优选杭州二仓、美团优选上海仓、美团虚拟代下单总仓、美团代加工虚拟仓、叮咚买菜昆山仓',
    `refrigerated_pallet` DECIMAL(38,18) COMMENT '冷藏托盘数量',
    `frozen_pallet` DECIMAL(38,18) COMMENT '冷冻托盘数量',
    `ambient_pallet` DECIMAL(38,18) COMMENT '常温托盘数量',
    `outbound_quantity` BIGINT COMMENT '出库件数，取值范围：0-15659',
    `outbound_weight` DECIMAL(38,18) COMMENT '出库吨数',
    `inbound_weight` DECIMAL(38,18) COMMENT '入库吨数',
    `standard_product_temporary_quantity` BIGINT COMMENT '标品临保数，取值范围：0-11',
    `fruit_handling_quantity` BIGINT COMMENT '水果搬运数，取值范围：0-1900',
    `fresh_fruit_sorting_quantity` BIGINT COMMENT '鲜果分包数，取值范围：-9-9161',
    `standard_product_unpacking_quantity` BIGINT COMMENT '标品拆包件数，取值范围：0-4385'
)
COMMENT 'BMS仓储数据统计表，包含各仓库的存储、出入库、加工等运营数据'
PARTITIONED BY (
    `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'BMS仓储运营数据统计表，用于仓储管理和运营分析',
    'last_data_modified_time' = '2025-09-23 02:06:15'
)
LIFECYCLE 30;
```