```sql
CREATE TABLE IF NOT EXISTS app_cust_values_df(
    cust_id BIGINT COMMENT '客户ID，唯一标识客户',
    cust_name STRING COMMENT '客户名称',
    area_name STRING COMMENT '运营区域',
    cust_type STRING COMMENT '客户类型；枚举值：面包蛋糕、西餐、咖啡、茶饮、甜品冰淇淋、其他、水果/果切/榨汁店',
    zone_size STRING COMMENT '销售区域；枚举值：西南大区、苏皖大区、华中大区、华南大区、浙江大区、上海大区、闽桂大区、山东大区、昆明大区',
    first_delivery_date DATETIME COMMENT '首次履约日期，格式：年月日时分秒',
    last_delivery_date STRING COMMENT '最近履约日期，格式：年月日时分秒',
    total_origin_amt_30 DECIMAL(38,18) COMMENT '近30天履约应付金额',
    total_origin_amt_60 DECIMAL(38,18) COMMENT '近31-60天履约应付金额',
    total_origin_amt_90 DECIMAL(38,18) COMMENT '近61-90天履约应付金额',
    avg_origin_amt DECIMAL(38,18) COMMENT '近30天、31-60天、61-90天履约应付金额平均值',
    stddev_origin_amt DECIMAL(38,18) COMMENT '近30天、31-60天、61-90天履约应付金额标准差',
    cust_life_cycle STRING COMMENT '客户生命周期标签；枚举值：流失、稳定、风险、跳跃活跃、间隔活跃、沉睡、观察期',
    cust_life_ltv DECIMAL(38,18) COMMENT '月LTV（生命周期总价值）',
    cust_life_ltv_level STRING COMMENT '月LTV价值等级；枚举值：低、中、高',
    cust_life_ltv_values DECIMAL(38,18) COMMENT '存活LTV价值',
    cust_life_ltv_values_level STRING COMMENT '存活LTV价值等级；枚举值：低、中、高'
)
COMMENT '客户生命周期标签表及价值分析表，包含客户基本信息、履约数据、生命周期标签和价值评估指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='客户生命周期和价值分析表，用于客户分层和价值评估')
LIFECYCLE 30;
```