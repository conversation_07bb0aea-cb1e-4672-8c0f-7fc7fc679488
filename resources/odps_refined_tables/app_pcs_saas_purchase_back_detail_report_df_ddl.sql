```sql
CREATE TABLE IF NOT EXISTS app_pcs_saas_purchase_back_detail_report_df(
    back_date DATETIME COMMENT '采购退货日期，格式为年月日时分秒',
    back_no STRING COMMENT '退货批次号，唯一标识一次退货操作',
    back_type BIGINT COMMENT '退货类型：0-未入库退货，1-已入库退货',
    operator STRING COMMENT '退货操作发起人姓名',
    purchaser STRING COMMENT '采购负责人姓名',
    sku_id BIGINT COMMENT '商品SKU编号，唯一标识具体规格的商品',
    spu_id BIGINT COMMENT '商品SPU编号，标识商品品类',
    xianmu_sku STRING COMMENT '鲜沐系统SKU编码',
    xianmu_spu_id BIGINT COMMENT '鲜沐系统SPU编号',
    name STRING COMMENT '商品名称',
    specification STRING COMMENT '商品规格描述',
    unit STRING COMMENT '计量单位：包、箱、件、盒、瓶、袋、桶、卷、组、条等',
    back_warehouse_no BIGINT COMMENT '退货仓库编号',
    back_warehouse_name STRING COMMENT '退货仓库名称：重构测试仓、测试总仓仓、东莞总仓、虚拟仓库、广州总部仓等',
    production_date DATETIME COMMENT '商品生产日期，格式为年月日',
    quality_date DATETIME COMMENT '商品保质期截止日期，格式为年月日',
    outbound_status STRING COMMENT '出库状态：全部出库、全未出库、部分出库',
    back_quantity BIGINT COMMENT '退货数量',
    back_amount DECIMAL(38,18) COMMENT '退货金额，保留18位小数精度',
    tenant_id BIGINT COMMENT '租户ID，标识业务租户',
    category_id BIGINT COMMENT '三级类目ID，商品分类标识',
    time_tag STRING COMMENT '时间标签，格式为yyyyMMdd的业务时间标记',
    supplier STRING COMMENT '供应商名称',
    warehouse_service_provider STRING COMMENT '仓库服务商：杭州鲜沐科技有限公司、广州肆捌城餐饮管理有限公司、客思服（杭州）科技有限公司等',
    price DECIMAL(38,18) COMMENT '采购单价，保留18位小数精度'
)
COMMENT 'SaaS采购退货明细表，记录采购退货的详细信息，包括商品信息、仓库信息、金额统计等'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，用于数据管理和查询优化')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='SaaS采购退货明细事实表，包含完整的退货业务链条数据')
LIFECYCLE 30;
```