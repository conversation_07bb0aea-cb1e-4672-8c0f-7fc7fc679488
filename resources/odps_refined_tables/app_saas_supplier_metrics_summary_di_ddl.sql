```sql
CREATE TABLE IF NOT EXISTS app_saas_supplier_metrics_summary_di(
    `time_tag` STRING COMMENT '日期标签，格式为yyyyMMdd，表示数据统计的日期',
    `tenant_id` BIGINT COMMENT 'SKU租户ID，唯一标识一个租户',
    `supplier_no` BIGINT COMMENT '供应商序号，唯一标识一个供应商',
    `supplier_name` STRING COMMENT '供应商名称',
    `supplier_type` STRING COMMENT '供应商类型，取值范围：企业（生产商）、企业（经销商）、个人',
    `purchase_to_warehouse_on_time_rate` DECIMAL(38,18) COMMENT '近30天采购到仓准时率，百分比数值，范围0-100',
    `fully_stocked_purchase_tasks_num_30d` BIGINT COMMENT '近30天完全入库的采购入库任务数量',
    `fully_stocked_on_time_purchase_tasks_num_30d` BIGINT COMMENT '近30天完全入库且准时入库的采购入库任务数量',
    `to_warehouse_accuracy` DECIMAL(38,18) COMMENT '近30天采购到仓准确率，百分比数值，范围0-100',
    `purchase_tasks_num_30d` BIGINT COMMENT '近30天采购入库单总数',
    `received_equal_incoming_tasks_num` BIGINT COMMENT '近30天实收数量等于应入数量的入库单数量'
)
COMMENT 'SaaS供应商指标汇总表，包含供应商的各项运营指标数据，用于供应商绩效分析和评估'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的业务日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SaaS供应商指标汇总表，按天分区存储供应商的各项运营指标数据',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```