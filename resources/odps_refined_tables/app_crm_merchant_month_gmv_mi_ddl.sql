```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_merchant_month_gmv_mi` (
  `cust_id` BIGINT COMMENT '商户ID，唯一标识一个商户',
  `cust_name` STRING COMMENT '商户名称',
  `city_id` BIGINT COMMENT '商户所在运营区域ID',
  `merchant_total_gmv` DECIMAL(38,18) COMMENT '商户总GMV（商品交易总额）',
  `distribution_gmv` DECIMAL(38,18) COMMENT '配送业务GMV',
  `delivery_unit_price` DECIMAL(38,18) COMMENT '配送客单价（平均每单金额）',
  `distribution_amout` BIGINT COMMENT '配送次数',
  `fruit_gmv` DECIMAL(38,18) COMMENT '鲜果品类GMV',
  `dairy_gmv` DECIMAL(38,18) COMMENT '乳制品品类GMV',
  `non_dairy_gmv` DECIMAL(38,18) COMMENT '非乳制品品类GMV',
  `brand_gmv` DECIMAL(38,18) COMMENT '自营品牌GMV',
  `reward_gmv` DECIMAL(38,18) COMMENT '奖励SKU的GMV',
  `core_merchant_tag` BIGINT COMMENT '核心客户标记：0-否，1-是',
  `bd_id` BIGINT COMMENT 'BD编号，公海为0',
  `bd_name` STRING COMMENT 'BD名称',
  `l1` DECIMAL(38,18) COMMENT 'L1层级指标（具体业务含义需确认）',
  `l2` DECIMAL(38,18) COMMENT 'L2层级指标（具体业务含义需确认）',
  `sku_num` BIGINT COMMENT '本月累计下单商品种类数（SKU数量）',
  `spu_num` BIGINT COMMENT '本月累计下单SPU种类数',
  `province` STRING COMMENT '省份，枚举值：浙江、上海、江苏、湖北、江西、湖南、广东、安徽、福建、重庆、四川、广西壮族自治区、山东',
  `city` STRING COMMENT '城市，枚举值：杭州市、金华市、嘉兴市、上海市、南京市、台州市、绍兴市、苏州市、湖州市、武汉市等50+城市',
  `area` STRING COMMENT '区域/区县，枚举值：西湖区、余杭区、上城区、拱墅区、钱塘区等300+区域',
  `agent_gmv` DECIMAL(38,18) COMMENT '代售业务GMV'
)
COMMENT '商户本月GMV统计表，按月份分区存储各商户的交易数据，包含各品类GMV分解和地域信息'
PARTITIONED BY (
  `ym` STRING COMMENT '分区字段，年月格式yyyyMM'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '商户本月GMV统计表，用于分析商户交易表现和业务分布',
  'lifecycle' = '30'
);
```