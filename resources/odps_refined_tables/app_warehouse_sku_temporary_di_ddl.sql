```sql
CREATE TABLE IF NOT EXISTS app_warehouse_sku_temporary_di(
    warehouse_no BIGINT COMMENT '库存仓编号',
    warehouse_name STRING COMMENT '库存仓名称，枚举值包括：上海总仓、嘉兴总仓、测试总仓仓、华西总仓、重庆总仓、福州总仓、重构测试仓、长沙总仓、南宁总仓、昆明总仓、苏州总仓、贵阳总仓、青岛总仓、东莞总仓、美团虚拟代下单总仓、东莞冷冻总仓、嘉兴海盐总仓',
    sku_id STRING COMMENT 'SKU编号，商品唯一标识',
    spu_name STRING COMMENT 'SPU名称，标准产品单元名称',
    category1 STRING COMMENT '一级类目名称，枚举值包括：乳制品、其他、鲜果',
    category4_id STRING COMMENT '四级类目ID',
    category4 STRING COMMENT '四级类目名称',
    sku_brand STRING COMMENT '品牌名称',
    sku_property STRING COMMENT 'SKU性质，枚举值：常规、临保、拆包、破袋',
    disc STRING COMMENT 'SKU描述，商品规格描述',
    quality_date STRING COMMENT '保质期日期，格式：yyyyMMdd，年月日格式',
    warn_days BIGINT COMMENT '到期预警天数，距离保质期到期前的预警天数',
    unit_amt DECIMAL(38,18) COMMENT '成本单价，商品单位成本价格',
    store_quantity BIGINT COMMENT '在仓库存数量，当前仓库中的库存数量',
    sale_out_quality BIGINT COMMENT '历史两周销售出库数量，过去14天的销售出库量',
    allocate_out_quality BIGINT COMMENT '历史两周调拨出库数量，过去14天的调拨出库量',
    avg_quality BIGINT COMMENT '过去2周日均出库量，过去14天的平均日出库量',
    store_use_days BIGINT COMMENT '库存可用天数，基于当前库存和日均出库量计算的可用天数，-1表示无限期或无法计算',
    temporary_date STRING COMMENT '临保日期，格式：yyyyMMdd，年月日格式，商品进入临保状态的日期',
    estimated_date STRING COMMENT '预计售罄日期，格式：yyyyMMdd，年月日格式，预计库存售罄的日期，"-"表示无法预估',
    damage_rask STRING COMMENT '货损风险标识，枚举值：是、否，标识商品是否存在货损风险'
)
COMMENT '库存仓、SKU维度临保数据表，记录各仓库中SKU商品的临保状态、库存情况、销售预测等信息，用于库存管理和风险预警'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，年月日格式')
STORED AS ALIORC
TBLPROPERTIES ('comment'='库存仓SKU临保数据表，用于库存管理和临保商品监控')
LIFECYCLE 30;
```