```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_category_cust_self_delivery_kpi_di` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd，表示业务日期',
  `category` STRING COMMENT '商品类型：乳制品、鲜果、其他',
  `cust_team` STRING COMMENT '客户团队类型：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实际总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `day_point_cnt` BIGINT COMMENT '日点位数',
  `sku_cnt` BIGINT COMMENT 'SKU数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本'
) 
COMMENT '履约口径客户、商品类型维度KPI指标日汇总表，包含各类成本和业务指标数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据分区日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '履约口径客户和商品类型维度的KPI指标日汇总表',
  'lifecycle' = '30'
);
```