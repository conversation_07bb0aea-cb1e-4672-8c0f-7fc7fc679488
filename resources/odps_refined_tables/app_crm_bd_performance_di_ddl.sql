CREATE TABLE IF NOT EXISTS app_crm_bd_performance_di(
	`date` STRING COMMENT '业务日期，格式为yyyyMMdd，表示数据统计的日期',
	`bd_id` BIGINT COMMENT '销售人员的唯一标识ID',
	`bd_name` STRING COMMENT '销售人员的姓名',
	`administrative_city` STRING COMMENT '销售人员所属的行政城市，枚举值包括：上海市、广州市、深圳市、东莞市、杭州市、清远市、肇庆市、绵阳市、无锡市、湛江市、佛山市、绍兴市、长沙市、金华市、株洲市、嘉兴市、镇江市、武汉市、苏州市、成都市、宜昌市、桂林市、台州市、江门市、贵阳市、泰州市、南昌市、萍乡市、上饶市、宁波市、温州市、青岛市、柳州市、福州市、重庆市、南京市、珠海市、扬州市、盐城市、茂名市、汕头市、洛阳市、淮安市、泉州市、昆明市、襄阳市、九江市、济南市、德阳市、合肥市等',
	`zone_name` STRING COMMENT '销售人员所属的区域名称，枚举值包括：上海、广州、深圳、东莞、浦西、杭州、佛山、四川、无锡、大粤西、杭州湾、测试权限数据、浙南、长沙、浦东、徽京、武汉、苏州、薄荷测试、广西、贵阳、苏北、江西、南京、济南,青岛、虚拟区域、福泉、贵阳,重庆、昆明、重庆、厦门、青岛、无',
	`m1` STRING COMMENT '城市负责人（M1）的姓名，枚举值包括：曹鹏飞、林献、张茂权、肖时煌、陈露露、翟远方、骆婷婷、陶京龙、李光远、陈俊生、陈洪亮、王业鼎、陈锐石、徐晟昊、付望、李钱程、向腾、冯朝皇、唐宽、王金浩、李茂源、韦贵丰、王旭东、葛世豪、习燕庆、陈忠良、庄典、张浩亮、汪林俊、蒋柳选、姜浪、陈开、赵奎、罗慧、孙军杰、郭宗旺、无',
	`m2` STRING COMMENT '区域负责人（M2）的姓名，枚举值包括：胡译丰、陈欲豪、赵奎、翟远方、李茂源、姜浪、桂少达、彭琨、林金秋、黄科、孙军杰、高偌桐、孙日达、无',
	`m3` STRING COMMENT '部门负责人（M3）的姓名，枚举值包括：吕建杰、孙日达、李茂源、剑锋、无',
	`total_gmv_amt` DECIMAL(38,18) COMMENT '总GMV金额（含SAAS），单位为元',
	`brand_gmv_amt` DECIMAL(38,18) COMMENT '自营品牌GMV金额，单位为元',
	`brand_cust_cnt` BIGINT COMMENT '自营品牌下单客户数量',
	`fruit_gmv_amt` DECIMAL(38,18) COMMENT '鲜果品类GMV金额，单位为元',
	`fruit_cust_cnt` BIGINT COMMENT '鲜果品类下单客户数量',
	`dairy_gmv_amt` DECIMAL(38,18) COMMENT '乳制品品类GMV金额，单位为元',
	`dairy_cust_cnt` BIGINT COMMENT '乳制品品类下单客户数量',
	`non_dairy_gmv_amt` DECIMAL(38,18) COMMENT '非乳制品品类GMV金额，单位为元',
	`non_dairy_cust_cnt` BIGINT COMMENT '非乳制品品类下单客户数量',
	`reward_gmv_amt` DECIMAL(38,18) COMMENT '安佳铁塔产品GMV金额，单位为元',
	`reward_cust_cnt` BIGINT COMMENT '安佳铁塔产品下单客户数量',
	`timing_gmv_amt` DECIMAL(38,18) COMMENT '省心送订单GMV金额，单位为元',
	`timing_cust_cnt` BIGINT COMMENT '省心送订单下单客户数量',
	`timing_reward_gmv_amt` DECIMAL(38,18) COMMENT '安佳铁塔省心送订单GMV金额，单位为元',
	`timing_reward_cust_cnt` BIGINT COMMENT '安佳铁塔省心送订单下单客户数量',
	`core_cust_total_gmv_amt` DECIMAL(38,18) COMMENT '核心客户总GMV金额，单位为元',
	`core_cust_cnt` BIGINT COMMENT '核心客户数量',
	`total_cust_cnt` BIGINT COMMENT '活跃客户总数（含SAAS）',
	`private_cust_cnt` BIGINT COMMENT '私海活跃客户数量',
	`open_cust_cnt` BIGINT COMMENT '公海活跃客户数量',
	`new_noactive_cust_cnt` BIGINT COMMENT '拉新客户数量（仅注册未下单）',
	`new_active_cust_cnt` BIGINT COMMENT '拉新客户数量（注册且下单）',
	`new_active_gmv_amt` DECIMAL(38,18) COMMENT '拉新客户GMV金额，单位为元',
	`ordinary_cnt` BIGINT COMMENT '普通拜访次数',
	`ordinary_cust_cnt` BIGINT COMMENT '普通拜访客户数量',
	`drop_in_visit_cnt` BIGINT COMMENT '普通上门拜访次数',
	`drop_in_visit_cust_cnt` BIGINT COMMENT '普通上门拜访客户数量',
	`efficient_cnt` BIGINT COMMENT '有效拜访次数',
	`efficient_cust_cnt` BIGINT COMMENT '有效拜访客户数量',
	`worth_cnt` BIGINT COMMENT '价值拜访次数',
	`worth_cust_cnt` BIGINT COMMENT '价值拜访客户数量',
	`deliver_gmv_amt` DECIMAL(38,18) COMMENT '配送GMV金额，单位为元',
	`deliver_point_cnt` BIGINT COMMENT '配送点位数',
	`deliver_gmv_avg` DECIMAL(38,18) COMMENT '点位平均单价，单位为元',
	`deliver_spu_cnt` BIGINT COMMENT '配送SPU数量（按点位累加）',
	`deliver_spu_avg` DECIMAL(38,18) COMMENT '平均点位购买SPU数量',
	`saas_total_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS总GMV金额，单位为元',
	`saas_total_cust_cnt` BIGINT COMMENT 'SAAS活跃客户数量',
	`normal_new_active_cust_cnt` BIGINT COMMENT '普通拉新客户数量（注册且首单<15元）',
	`normal_new_active_gmv_amt` DECIMAL(38,18) COMMENT '普通拉新客户GMV金额，单位为元',
	`private_effect_cust_cnt` BIGINT COMMENT '私海有效活跃客户数量（实付金额>=20元）',
	`private_normal_cust_cnt` BIGINT COMMENT '私海普通活跃客户数量（实付金额<20元）',
	`open_effect_cust_cnt` BIGINT COMMENT '公海有效活跃客户数量（实付金额>=20元）',
	`open_normal_cust_cnt` BIGINT COMMENT '公海普通活跃客户数量（实付金额<20元）',
	`saas_brand_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS自营品牌订单实付金额，单位为元',
	`saas_fruit_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS鲜果订单实付金额，单位为元',
	`saas_dairy_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS乳制品订单实付金额（剔除N001S01R005、N001S01R002），单位为元',
	`saas_non_dairy_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS非乳制品订单实付金额，单位为元',
	`saas_reward_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS安佳铁塔订单实付金额（N001S01R005、N001S01R002），单位为元',
	`saas_effect_cust_cnt` BIGINT COMMENT 'SAAS有效活跃客户数量（实付金额>=20元）'
) 
COMMENT 'BD粒度业绩报表日汇总表，按销售人员维度统计每日的销售业绩、客户拜访、品类GMV等关键业务指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='销售人员业绩日报表，包含GMV、客户数、拜访情况等多维度业务指标') 
LIFECYCLE 30;