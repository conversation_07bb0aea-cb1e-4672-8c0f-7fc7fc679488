CREATE TABLE IF NOT EXISTS app_crm_merchant_area_type_top_di(
	month_tag STRING COMMENT '月份标记，格式：yyyyMM，如202509表示2025年9月',
	month_type BIGINT COMMENT '所选时间类型：0-本月（枚举值：0）',
	type STRING COMMENT '商户经营类型（枚举值：其他、咖啡、水果/果切/榨汁店、甜品冰淇淋、糖水/水果捞、茶饮、蛋糕店、西餐、西餐披萨、面包蛋糕、面包蛋糕点心）',
	area_no BIGINT COMMENT '运营区域编号，取值范围：1001-44269',
	category_id_list STRING COMMENT '品类TOP10，以英文逗号分隔的品类ID字符串',
	pd_id_list STRING COMMENT '商品TOP10，以英文逗号分隔的商品ID字符串'
)
COMMENT '商户同区域同行业品类和商品的TOP10排名表，用于分析同区域同行业商户的热门品类和商品'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商户同区域同行业品类和商品的TOP10排名表，包含月份、区域、经营类型等维度的热门品类和商品数据')
LIFECYCLE 30;