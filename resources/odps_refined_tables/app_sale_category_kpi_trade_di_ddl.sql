CREATE TABLE IF NOT EXISTS app_sale_category_kpi_trade_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`category` STRING COMMENT '品类，取值范围：鲜果、乳制品、其他',
	`order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额，单位为元',
	`order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额，单位为元',
	`order_cust_cnt` BIGINT COMMENT '交易客户数，去重后的客户数量',
	`order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(平均每用户收入)，计算公式：应付总金额/客户数，单位为元',
	`order_cnt` BIGINT COMMENT '交易订单数',
	`delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位为元',
	`delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位为元',
	`delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数，去重后的客户数量',
	`delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润，单位为元',
	`delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润，单位为元',
	`delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润，单位为元',
	`delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次，平均履约天数',
	`delivery_point_cnt` BIGINT COMMENT '履约点位数，去重后的点位数量',
	`delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用，单位为元',
	`new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额，单位为元',
	`new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额，单位为元',
	`new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数，去重后的客户数量',
	`new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润，单位为元',
	`old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额，单位为元',
	`old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额，单位为元',
	`old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数，去重后的客户数量',
	`old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润，单位为元',
	`order_sku_cnt` BIGINT COMMENT '交易SKU款数，去重后的SKU数量',
	`order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量，单位为千克(KG)',
	`delivery_sku_cnt` BIGINT COMMENT '履约SKU款数，去重后的SKU数量',
	`delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量，单位为千克(KG)'
) 
COMMENT '销售KPI指标汇总表，按品类维度统计交易和履约相关的关键绩效指标，包括金额、客户数、订单数、SKU数等核心业务指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='销售KPI指标汇总表') 
LIFECYCLE 30;