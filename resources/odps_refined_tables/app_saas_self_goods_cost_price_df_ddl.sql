CREATE TABLE IF NOT EXISTS app_saas_self_goods_cost_price_df(
    tenant_id BIGINT COMMENT '租户ID，取值范围：2-32',
    sku_id BIGINT COMMENT '鲜沐SKU主键ID，取值范围：5916-32054',
    sku_code STRING COMMENT '鲜沐SKU编码，示例值：1038757775048、734115302785等',
    province STRING COMMENT '省份名称，枚举值：河南省、浙江省、福建省、上海市、江西省、广东省、安徽省、广西壮族自治区、江苏省、黑龙江省、辽宁省、湖南省、海南省、河北省、吉林省、内蒙古自治区、云南省、山东省、四川省、宁夏回族自治区、北京市、甘肃省、新疆维吾尔自治区',
    city STRING COMMENT '城市名称，枚举值：商丘市、杭州市、焦作市、三明市、上海市、上饶市、东莞市、中山市等',
    area STRING COMMENT '区域名称，枚举值：宁陵县、梁园区、睢阳区、虞城县、上城区、临安区等',
    price DECIMAL(38,18) COMMENT '货品成本价格',
    valid_time DATETIME COMMENT '价格有效期，格式：年月日时分秒'
)
COMMENT 'SAAS自营货品成本价格表，记录不同地区不同SKU的成本价格信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='SAAS自营货品成本价格表，包含租户、SKU、地区维度的成本价格数据')
LIFECYCLE 30;