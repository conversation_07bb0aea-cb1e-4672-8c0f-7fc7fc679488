```sql
CREATE TABLE IF NOT EXISTS app_crm_city_month_gmv_mi(
    administrative_city STRING COMMENT '行政城市名称，枚举类型，包含全国各城市名称如：上海市、深圳市、北京市等',
    team_tag BIGINT COMMENT '团队类型：0-全部团队，1-平台销售团队，2-大客户团队',
    total_gmv DECIMAL(38,18) COMMENT '总GMV（商品交易总额），单位：元',
    brand_gmv DECIMAL(38,18) COMMENT '自有品牌GMV，单位：元',
    pull_new_amount BIGINT COMMENT '拉新客户数量',
    core_merchant_amount BIGINT COMMENT '核心客户数量',
    month_live_amount BIGINT COMMENT '月活跃客户总数',
    open_merchant_month_live BIGINT COMMENT '公海客户月活跃数量',
    private_merchant_month_live BIGINT COMMENT '私海客户月活跃数量',
    open_merchant_amount BIGINT COMMENT '公海客户总数',
    private_merchant_amount BIGINT COMMENT '私海客户总数',
    operate_merchant_num BIGINT COMMENT '倒闭客户数量',
    visit_num BIGINT COMMENT '总拜访次数',
    drop_in_visit_num BIGINT COMMENT '普通上门拜访次数',
    efficient_num BIGINT COMMENT '有效拜访次数',
    escort_num BIGINT COMMENT '陪访次数',
    spu_average DECIMAL(38,18) COMMENT 'SPU（标准化产品单元）平均值',
    ordinary_pull_new_amount BIGINT COMMENT '普通拉新客户数量',
    private_merchant_effective_month_live BIGINT COMMENT '私海有效月活跃客户数量',
    open_merchant_effective_month_live BIGINT COMMENT '公海有效月活跃客户数量'
)
COMMENT '行政城市本月GMV统计表，按城市和团队类型维度统计月度交易数据和客户活跃指标'
PARTITIONED BY (ym STRING COMMENT '分区字段，年月格式yyyyMM，如：202508表示2025年8月')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='行政城市月度GMV统计表，包含各城市不同团队类型的交易额、客户活跃度、拜访情况等核心业务指标')
LIFECYCLE 30;
```