CREATE TABLE IF NOT EXISTS app_saas_product_sales_overview_month_di(
	tenant_id BIGINT COMMENT '租户ID',
	time_tag STRING COMMENT '时间标签，格式为yyyyMMdd，表示当月1号（年月日）',
	category_id BIGINT COMMENT '三级类目ID',
	store_type BIGINT COMMENT '门店类型：0-直营店，1-加盟店，2-托管店',
	store_id BIGINT COMMENT '门店ID',
	store_name STRING COMMENT '门店名称',
	province STRING COMMENT '省份',
	city STRING COMMENT '城市',
	pay_success_num BIGINT COMMENT '支付成功商品件数',
	pay_success_price DECIMAL(38,18) COMMENT '支付成功金额',
	refund_num BIGINT COMMENT '退款件数',
	refund_price DECIMAL(38,18) COMMENT '退款金额',
	warehouse_type BIGINT COMMENT '归属类型：0-自营品，1-三方品，2-其他',
	delivery_type BIGINT COMMENT '配送方式：0-品牌方配送，1-三方配送',
	item_id BIGINT COMMENT '商品编码',
	title STRING COMMENT '商品名称',
	goods_type BIGINT COMMENT '商品类型：0-无货商品，1-报价货品，2-自营货品'
) 
COMMENT 'SaaS商品销售概况表（月维度），记录各门店商品销售、退款等月度汇总数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd（年月日）')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS商品销售月维度概况表，包含租户、门店、商品维度的销售和退款数据统计') 
LIFECYCLE 30;