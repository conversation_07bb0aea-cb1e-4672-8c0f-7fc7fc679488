CREATE TABLE IF NOT EXISTS app_sku_cust_monitor_wi(
	`year` STRING COMMENT '年份，格式：YYYY',
	`week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
	`monday` STRING COMMENT '周一日期，格式：yyyyMMdd（年月日）',
	`sunday` STRING COMMENT '周日日期，格式：yyyyMMdd（年月日）',
	`cust_type` STRING COMMENT '客户类型，取值范围：ALL/平台客户/大客户/批发',
	`warehouse_no` STRING COMMENT '库存仓号',
	`sku_id` STRING COMMENT 'SKU编码',
	`category1` STRING COMMENT '一级类目',
	`category2` STRING COMMENT '二级类目',
	`category3` STRING COMMENT '三级类目',
	`category4` STRING COMMENT '四级类目',
	`sku_spec` STRING COMMENT '规格',
	`sku_brand` STRING COMMENT '品牌',
	`weight` STRING COMMENT '重量（kg）',
	`volume` STRING COMMENT '体积（m³）',
	`first_on_sale_date` STRING COMMENT '首次上架日期，格式：yyyyMMdd（年月日）',
	`quality_time` BIGINT COMMENT '效期（天）',
	`is_sale` STRING COMMENT '是否上架，取值范围：是/否',
	`is_new` STRING COMMENT '是否在架库存新品，取值范围：是/否',
	`pv` BIGINT COMMENT '曝光PV',
	`click_pv` BIGINT COMMENT '点击PV',
	`add_pv` BIGINT COMMENT '加购PV',
	`buy_pv` BIGINT COMMENT '直接购买PV',
	`pv_value` DECIMAL(38,18) COMMENT '千次曝光价值',
	`uv` BIGINT COMMENT '曝光UV',
	`click_uv` BIGINT COMMENT '点击UV',
	`add_uv` BIGINT COMMENT '加购UV',
	`buy_uv` BIGINT COMMENT '直接购买UV',
	`exposure_conversion_rate` DECIMAL(38,18) COMMENT '曝光点击转化率',
	`buy_conversion_rate` DECIMAL(38,18) COMMENT '购买转化率',
	`order_cnt` BIGINT COMMENT '订单数',
	`cust_cnt` BIGINT COMMENT '客户数',
	`origin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV',
	`real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV',
	`order_sku_cnt` BIGINT COMMENT '销量',
	`order_sku_weight` DECIMAL(38,18) COMMENT '交易商品重量（kg）',
	`timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送交易应付GMV',
	`timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送交易实付GMV',
	`max_order_date` STRING COMMENT '最新动销日期，格式：yyyyMMdd（年月日）',
	`dlv_cust_cnt` BIGINT COMMENT '履约客户数',
	`dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
	`dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
	`cost_amt` DECIMAL(38,18) COMMENT '成本费用',
	`dlv_market_amt` DECIMAL(38,18) COMMENT '营销费用',
	`dlv_market_roi` DECIMAL(38,18) COMMENT '营销费用ROI',
	`gross_profit_amt` DECIMAL(38,18) COMMENT '实付毛利润',
	`origin_profit_rate` DECIMAL(38,18) COMMENT '应付毛利率',
	`real_profit_rate` DECIMAL(38,18) COMMENT '实付毛利率',
	`dlv_sku_cnt` BIGINT COMMENT '履约数量',
	`dlv_pet_origin_amt` DECIMAL(38,18) COMMENT '履约单件应付GMV',
	`dlv_pet_real_amt` DECIMAL(38,18) COMMENT '履约单件实付GMV',
	`dlv_pet_cost_amt` DECIMAL(38,18) COMMENT '履约单件成本',
	`dlv_pet_profit_amt` DECIMAL(38,18) COMMENT '履约单件毛利润',
	`dlv_sku_weight` DECIMAL(38,18) COMMENT '履约重量（kg）',
	`dlv_pet_weight_origin_amt` DECIMAL(38,18) COMMENT '履约单kg应付GMV',
	`dlv_pet_weight_real_amt` DECIMAL(38,18) COMMENT '履约单kg实付GMV',
	`dlv_pet_weight_cost_amt` DECIMAL(38,18) COMMENT '履约单kg成本',
	`dlv_pet_weight_profit_amt` DECIMAL(38,18) COMMENT '履约单kg毛利润',
	`dlv_timing_origin_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV',
	`dlv_timing_real_amt` DECIMAL(38,18) COMMENT '省心送履约实付GMV',
	`dlv_timing_sku_cnt` BIGINT COMMENT '省心送履约数量',
	`dlv_no_timing_sku_cnt` BIGINT COMMENT '省心送未履约数量',
	`dlv_no_timing_sku_amt` DECIMAL(38,18) COMMENT '省心送未履约金额',
	`after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后金额',
	`after_sale_noreceived_proportion` DECIMAL(38,18) COMMENT '未到货售后金额占比',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后金额',
	`after_sale_received_proportion` DECIMAL(38,18) COMMENT '已到货售后金额占比',
	`store_quantity` BIGINT COMMENT '可用库存',
	`store_amt` DECIMAL(38,18) COMMENT '可用库存金额',
	`quality_amt_4_3` DECIMAL(38,18) COMMENT '3/4效期库存金额',
	`quality_amt_2_1` DECIMAL(38,18) COMMENT '1/2效期库存金额',
	`quality_amt_4_1` DECIMAL(38,18) COMMENT '1/4效期库存金额',
	`quality_amt_under_4_1` DECIMAL(38,18) COMMENT '1/4效期以下库存金额',
	`advent_amt` DECIMAL(38,18) COMMENT '临期库存金额',
	`turnover` DECIMAL(38,18) COMMENT '近三十天周转率',
	`purchase_in_cnt` BIGINT COMMENT '采购入库数量',
	`purchase_in_amt` DECIMAL(38,18) COMMENT '采购入库金额',
	`damage_in_cnt` BIGINT COMMENT '货损出库数量',
	`damage_in_amt` DECIMAL(38,18) COMMENT '货损出库金额',
	`original_price` DECIMAL(38,18) COMMENT '原价',
	`sale_price` DECIMAL(38,18) COMMENT '售价',
	`dlv_origin_amt_label` BIGINT COMMENT '履约应付GMV类目排名（四级），取值范围：1-15',
	`dlv_total_last_origin_rate` DECIMAL(38,18) COMMENT '履约应付GMV占比（四级）上级占比'
) 
COMMENT '单品监控周表，用于监控SKU级别的销售、库存、履约等核心业务指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='单品监控周表，包含SKU维度的销售、库存、履约、售后等全方位业务指标数据') 
LIFECYCLE 30;