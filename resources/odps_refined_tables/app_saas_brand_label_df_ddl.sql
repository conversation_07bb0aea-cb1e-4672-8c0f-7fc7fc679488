```sql
CREATE TABLE IF NOT EXISTS app_saas_brand_label_df(
    `date` STRING COMMENT '数据日期，格式为yyyyMMdd，表示年月日',
    `brand_id` BIGINT COMMENT '品牌唯一标识ID',
    `brand_alias` STRING COMMENT '品牌别名/简称，如：蔡小甜、艾炒酸奶、何沅璋等',
    `brand_name` STRING COMMENT '品牌完整的企业注册名称，如：乐蔻食品（上海）有限公司、上海博膳品牌管理有限公司等',
    `tenant_id` BIGINT COMMENT '租户唯一标识ID',
    `tenant_name` STRING COMMENT '租户名称，如：AMLCAKE艾抹琳、艾炒酸奶的订货商城、测试权限租户等',
    `all_store_cnt` BIGINT COMMENT '门店总注册数量',
    `direct_store_cnt` BIGINT COMMENT '直营门店注册数量',
    `join_store_cnt` BIGINT COMMENT '加盟门店注册数量',
    `managed_store_cnt` BIGINT COMMENT '托管门店注册数量',
    `last_order_time` DATETIME COMMENT '最近一次交易时间，格式为yyyy-MM-dd HH:mm:ss，表示年月日时分秒',
    `order_store_cnt_30d` BIGINT COMMENT '近30天有交易的门店总数',
    `order_direct_store_cnt_30d` BIGINT COMMENT '近30天有交易的直营门店数量',
    `order_join_store_cnt_30d` BIGINT COMMENT '近30天有交易的加盟门店数量',
    `order_managed_store_cnt_30d` BIGINT COMMENT '近30天有交易的托管门店数量',
    `store_order_cnt_30d_avg` DECIMAL(38,18) COMMENT '近30天门店平均交易次数',
    `order_province_cnt_30d` BIGINT COMMENT '近30天交易覆盖的省份数量',
    `order_city_cnt_30d` BIGINT COMMENT '近30天交易覆盖的城市数量',
    `total_gmv_30d` DECIMAL(38,18) COMMENT '近30天总交易金额(GMV)',
    `xianmu_gmv_30d` DECIMAL(38,18) COMMENT '近30天鲜沐自营业务交易GMV',
    `xianmu_gmv_30d_rate` DECIMAL(38,18) COMMENT '近30天鲜沐自营GMV占总GMV的比例',
    `xianmu_store_cnt_30d` BIGINT COMMENT '近30天鲜沐自营业务交易门店数',
    `dc_gmv_30d` DECIMAL(38,18) COMMENT '近30天代仓业务交易GMV',
    `dc_gmv_30d_rate` DECIMAL(38,18) COMMENT '近30天代仓GMV占总GMV的比例',
    `dc_store_cnt_30d` BIGINT COMMENT '近30天代仓业务交易门店数',
    `ds_gmv_30d` DECIMAL(38,18) COMMENT '近30天代售业务交易GMV',
    `ds_gmv_30d_rate` DECIMAL(38,18) COMMENT '近30天代售GMV占总GMV的比例',
    `ds_store_cnt_30d` BIGINT COMMENT '近30天代售业务交易门店数',
    `khzy_gmv_30d` DECIMAL(38,18) COMMENT '近30天客户自营交易GMV（剔除供应商直发）',
    `khzy_gmv_30d_rate` DECIMAL(38,18) COMMENT '近30天客户自营GMV占比（剔除供应商直发）',
    `khzy_store_cnt_30d` BIGINT COMMENT '近30天客户自营交易门店数',
    `supplier_direct_gmv_30d` DECIMAL(38,18) COMMENT '近30天供应商直发交易GMV',
    `supplier_direct_gmv_30d_rate` DECIMAL(38,18) COMMENT '近30天供应商直发GMV占比',
    `supplier_direct_store_cnt_30d` BIGINT COMMENT '近30天供应商直发交易门店数',
    `fruit_gmv_30d` DECIMAL(38,18) COMMENT '近30天鲜果类商品交易GMV',
    `not_fruit_gmv_30d` DECIMAL(38,18) COMMENT '近30天标品类商品交易GMV',
    `fruit_xianmu_gmv_30d` DECIMAL(38,18) COMMENT '近30天鲜沐自营鲜果交易GMV',
    `fruit_30d_rate` DECIMAL(38,18) COMMENT '近30天鲜果渗透率（鲜沐自营交易鲜果GMV / 鲜果交易GMV）',
    `dc_30d_rate` DECIMAL(38,18) COMMENT '近30天代仓渗透率（代仓交易门店数 / 门店总注册数）',
    `order_store_rate` DECIMAL(38,18) COMMENT '近30天SaaS使用渗透率（交易门店数 / 门店总注册数）'
) 
COMMENT 'SaaS品牌标签表，包含品牌基本信息、门店分布、交易统计和业务渗透率等多维度标签数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SaaS品牌多维度标签分析表，用于品牌画像和业务分析',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```