```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_year_bill_di` (
  `cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
  `cust_name` STRING COMMENT '客户名称，如店铺名称或个人名称',
  `register_date` STRING COMMENT '注册日期，格式为yyyyMMdd，表示客户注册的年月日',
  `register_date_cnt` BIGINT COMMENT '注册时长，从注册日期到统计日期的天数',
  `is_loading` STRING COMMENT '近365天是否登录（过去365天），枚举值：是/否',
  `is_order` STRING COMMENT '近365天是否下单（过去365天），枚举值：是/否',
  `order_cn` BIGINT COMMENT '下单次数（过去365天）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付金额（过去365天），单位：元',
  `delivery_cnt` BIGINT COMMENT '配送次数（过去365天）',
  `perential_cnt` BIGINT COMMENT '优惠次数（过去365天）',
  `perential_amt` DECIMAL(38,18) COMMENT '优惠金额（过去365天），单位：元',
  `perential_amt_rk` STRING COMMENT '优惠金额超越**%客户数（过去365天），枚举值：5%/10%/20%/30%/40%/50%/60%/70%/80%/90%/95%',
  `pt_perential_cnt` DECIMAL(38,18) COMMENT 'pt优惠量（过去365天）',
  `last_order_time` DATETIME COMMENT '最晚下单时间（过去365天）最晚时间节点截止3:00前，格式为yyyy-MM-dd HH:mm:ss，表示年月日时分秒',
  `max_view_time_date` STRING COMMENT '浏览时长最长日期（过去365天），格式为yyyyMMdd，表示年月日',
  `max_view_time_cnt` DECIMAL(38,18) COMMENT '浏览时长最长时间\'分\'（过去365天），单位：分钟',
  `max_order_date` STRING COMMENT '最大交易应付日期（过去365天），格式为yyyyMMdd，表示年月日',
  `max_order_amt` DECIMAL(38,18) COMMENT '最大交易应付金额（过去365天），单位：元',
  `max_spu_name` STRING COMMENT '下单最多的商品名称（过去365天）'
)
COMMENT '客户年账单表，统计客户过去365天的行为数据，包括登录、下单、交易金额、优惠等信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据统计的年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户年账单明细表',
  'lifecycle' = '30'
)
```