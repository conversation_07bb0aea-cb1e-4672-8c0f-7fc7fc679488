```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_pcs_supplier_rebate_target_cumulative_df` (
  `year` BIGINT COMMENT '年度，格式：YYYY',
  `period` STRING COMMENT '统计周期，取值范围：H1(上半年)、H2(下半年)、1月-12月、Q1-Q4(季度)、年(全年)',
  `supplier_id` BIGINT COMMENT '供货商ID',
  `supplier_name` STRING COMMENT '供货商名称',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `warehouse_name` STRING COMMENT '仓库名称，取值范围：上海总仓、嘉兴总仓、华西总仓、重庆总仓、福州总仓、长沙总仓、南宁总仓、昆明总仓、贵阳总仓、青岛总仓、东莞总仓、南京总仓、东莞冷冻总仓、嘉兴海盐总仓、杭州总仓、武汉总仓',
  `commodity_temperature_zone` STRING COMMENT '商品温区，取值范围：冷藏、常温、冷冻',
  `purchase_order_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购下单金额（含税）',
  `purchase_order_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购下单金额（不含税）',
  `purchase_order_quantity_in_period` BIGINT COMMENT '周期内采购下单件数',
  `purchase_order_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购下单重量（单位：千克）',
  `purchase_order_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购下单体积（单位：立方米）',
  `purchase_inbound_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购入库金额（含税）',
  `purchase_inbound_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购入库金额（不含税）',
  `purchase_inbound_quantity_in_period` BIGINT COMMENT '周期内采购入库件数',
  `purchase_inbound_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购入库重量（单位：千克）',
  `purchase_inbound_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购入库体积（单位：立方米）',
  `purchase_reservation_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购预约金额（含税）',
  `purchase_reservation_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购预约金额（不含税）',
  `purchase_reservation_quantity_in_period` BIGINT COMMENT '周期内采购预约件数',
  `purchase_reservation_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购预约重量（单位：千克）',
  `purchase_reservation_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购预约体积（单位：立方米）'
) 
COMMENT '供应商返利目标累计月维度表，按月度累计统计供应商的采购相关指标，用于返利目标计算'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '供应商返利目标累计月维度表，包含供应商在各仓库、各温区的采购下单、入库、预约等累计数据',
  'lifecycle' = '30'
);
```