CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_area_nohonour_cost_di`(
  `service_area` STRING COMMENT '区域名称，枚举值包括：昆明、华南、福建、华东、贵阳、华西、华北、广西、华中',
  `allocate_amt` DECIMAL(38,18) COMMENT '调拨费用，单位为元',
  `purchase_car_amt` DECIMAL(38,18) COMMENT '采购用车费用，单位为元',
  `big_cust_amt` DECIMAL(38,18) COMMENT '大客户费用，单位为元',
  `purchase_store_amt` DECIMAL(38,18) COMMENT '采购仓储费用，单位为元',
  `total_nohonour_amt` DECIMAL(38,18) COMMENT '非履约费用总额，单位为元',
  `deliver_total_amt` DECIMAL(38,18) COMMENT '配送费用总额，单位为元'
)
COMMENT '区域维度非履约成本表，按区域统计各类非履约相关费用'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '区域维度非履约成本明细表，包含调拨费用、采购用车费用、大客户费用、采购仓储费用、非履约费用总额和配送费用等成本数据',
  'last_data_modified_time' = '2025-09-23 02:17:00'
)
LIFECYCLE 30;