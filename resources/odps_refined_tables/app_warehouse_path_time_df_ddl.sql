CREATE TABLE IF NOT EXISTS app_warehouse_path_time_df(
	in_warehouse_no BIGINT COMMENT '转入仓编号，取值范围：2-146',
	out_warehouse_no BIGINT COMMENT '转出仓编号，取值范围：2-178',
	cost_time BIGINT COMMENT '用时(天)，取值范围：2-4天，平均用时约3.88天'
)
COMMENT '开放仓到仓之间路途时间表，记录不同仓库之间的运输时间成本'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式的日期，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='开放仓到仓之间路途时间分析表，用于仓储物流路径优化和时间成本计算')
LIFECYCLE 30;