```sql
CREATE TABLE IF NOT EXISTS app_self_category_delivery_warehouse_kpi_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `category` STRING COMMENT '商品品类：鲜果-新鲜水果类，乳制品-奶制品类，其他-其他商品类别',
    `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额，订单原始金额总计',
    `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额，订单实际成交金额总计',
    `cost_amt` DECIMAL(38,18) COMMENT '成本金额，商品成本总计',
    `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额，省心送服务的原始订单金额',
    `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实际总金额，省心送服务的实际成交金额',
    `cust_cnt` BIGINT COMMENT '客户数，下单客户数量',
    `order_cnt` BIGINT COMMENT '订单数，总订单数量',
    `point_cnt` BIGINT COMMENT '点位数，服务点位总数',
    `day_point_cnt` BIGINT COMMENT '日均点位数，平均每日服务点位数量',
    `sku_cnt` BIGINT COMMENT 'SKU数量，商品SKU总数',
    `delivery_amt` DECIMAL(38,18) COMMENT '运费金额，配送费用总计',
    `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，已收货的售后金额总计',
    `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额，库存盘点亏损金额',
    `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额，库存盘点盈余金额',
    `damage_amt` DECIMAL(38,18) COMMENT '货损总金额，货物损坏损失金额',
    `damage_rate` DECIMAL(38,18) COMMENT '货损占比，货损金额占总金额的比例',
    `storage_amt` DECIMAL(38,18) COMMENT '仓储成本，仓储相关费用',
    `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，干线运输费用',
    `deliver_amt` DECIMAL(38,18) COMMENT '配送成本，末端配送费用',
    `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本，自提相关费用',
    `other_amt` DECIMAL(38,18) COMMENT '其他成本，其他未分类成本',
    `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，商品调拨相关费用'
)
COMMENT '履约口径KPI指标日汇总表(自营业务)，包含自营业务的各项履约相关KPI指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '自营业务履约KPI指标日度汇总表，用于监控和分析自营业务的履约表现',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```