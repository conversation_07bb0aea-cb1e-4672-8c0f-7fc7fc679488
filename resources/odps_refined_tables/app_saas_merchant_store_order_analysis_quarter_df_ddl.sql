```sql
CREATE TABLE IF NOT EXISTS app_saas_merchant_store_order_analysis_quarter_df(
    tenant_id BIGINT COMMENT '租户ID，取值范围：2-64',
    time_tag STRING COMMENT '时间标签，季度标识，格式：yyyyMMdd，取值范围：20220401、20220701、20221001、20230101、20230401、20230701、20231001、20240101等季度起始日期',
    store_id BIGINT COMMENT '门店ID，取值范围：1-396432',
    average_order_period DECIMAL(38,18) COMMENT '平均订货周期（天）',
    average_order_period_last_period DECIMAL(38,18) COMMENT '上周期平均订货周期（天）',
    average_order_period_upper_period DECIMAL(38,18) COMMENT '平均订货周期环比（百分比）',
    order_amount BIGINT COMMENT '订货数量，取值范围：1-86643',
    order_amount_last_period BIGINT COMMENT '上周期订货数量，取值范围：0-86643',
    order_amount_upper_period DECIMAL(38,18) COMMENT '订货数量环比（百分比）',
    order_price DECIMAL(38,18) COMMENT '订货金额（元）',
    order_price_last_period DECIMAL(38,18) COMMENT '上周期订货金额（元）',
    order_price_upper_period DECIMAL(38,18) COMMENT '订货金额环比（百分比）',
    last_order_time STRING COMMENT '最后订货日期，格式：yyyy-MM-dd，表示年月日',
    last_order_amount BIGINT COMMENT '最后订货数量，取值范围：1-12902',
    last_order_price DECIMAL(38,18) COMMENT '最后订货金额（元）'
)
COMMENT 'SaaS门店订货分析季度表：统计各门店季度订货情况，包括订货周期、订货数量、订货金额等指标及其环比变化'
PARTITIONED BY (ds STRING COMMENT '分区字段，数据仓库处理日期，格式：yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='SaaS门店季度订货分析结果表，用于分析门店订货行为和趋势')
LIFECYCLE 30;
```