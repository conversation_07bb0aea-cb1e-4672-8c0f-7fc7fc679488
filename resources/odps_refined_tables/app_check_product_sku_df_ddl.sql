CREATE TABLE IF NOT EXISTS app_check_product_sku_df(
	sku STRING COMMENT '货品SKU编码，唯一标识一个具体的货品规格，取值范围：1387105807456、1387120002003、1387155704702、1387208140085、1387371281711、1387516687614、1387586841020、1387607250433、1387607250500、1387882653420、1387882653583、1387882877200等'
) 
COMMENT '业务数据校验——货品sku校验表，用于校验货品SKU数据的完整性和准确性'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='业务数据校验——货品sku校验表') 
LIFECYCLE 30;