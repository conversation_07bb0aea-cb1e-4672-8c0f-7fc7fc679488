```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_area_city_cust_sku_timing_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
  `large_area_id` BIGINT COMMENT '运营服务大区ID，唯一标识一个大区',
  `large_area_name` STRING COMMENT '运营服务大区名称，如：杭州大区、上海大区等',
  `city_id` BIGINT COMMENT '运营服务区ID，唯一标识一个城市服务区',
  `city_name` STRING COMMENT '运营服务区名称，如：杭州、上海、苏州等',
  `cust_id` BIGINT COMMENT '客户ID，唯一标识一个客户',
  `cust_name` STRING COMMENT '客户名称，如：晓陆、金枝手工烘培等',
  `cust_type` STRING COMMENT '客户类型，枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、其他',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位标识',
  `spu_id` BIGINT COMMENT 'SPU ID，标准产品单位标识',
  `spu_name` STRING COMMENT '商品名称，如：安佳淡奶油、总统淡奶油等',
  `sku_disc` STRING COMMENT '商品描述，包含规格信息，如：1L*12盒、25KG*1包等',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送交易应付GMV，历史截止当日累计值',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送交易实付GMV，历史截止当日累计值',
  `timing_order_cnt` BIGINT COMMENT '省心送交易订单数，历史截止当日累计值',
  `timing_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV，历史截止当日累计值',
  `timing_dlv_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付GMV，历史截止当日累计值',
  `timing_dlv_order_cnt` BIGINT COMMENT '省心送履约订单数，历史截止当日累计值',
  `timing_no_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '省心送未履约应付GMV，历史截止当日累计值',
  `timing_no_dlv_real_total_amt` DECIMAL(38,18) COMMENT '省心送未履约实付GMV，历史截止当日累计值',
  `timing_no_dlv_sku_cnt` BIGINT COMMENT '省心送未履约商品数量，历史截止当日累计值',
  `timing_dlv_7_day_sku_cnt` BIGINT COMMENT '省心送未来7天预约配送商品数量',
  `timing_dlv_14_day_sku_cnt` BIGINT COMMENT '省心送未来14天预约配送商品数量',
  `timing_last_date` DATETIME COMMENT '省心送最后下单时间，格式为yyyy-MM-dd HH:mm:ss',
  `last_date` DATETIME COMMENT '最后下单时间（全渠道），格式为yyyy-MM-dd HH:mm:ss'
) 
COMMENT '运营服务区+客户+库存仓 SKU 省心送交易明细表，包含省心送业务的交易、履约、未履约等明细数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '省心送业务交易明细事实表，按运营大区-城市-客户-SKU维度统计交易和履约数据',
  'lifecycle' = '30'
)
```