CREATE TABLE IF NOT EXISTS app_crm_bd_consign_performance_di(
    `date` STRING COMMENT '日期，格式：yyyyMMdd，表示业务发生日期',
    `bd_id` BIGINT COMMENT '销售ID，唯一标识一个销售人员',
    `bd_name` STRING COMMENT '销售姓名',
    `administrative_city` STRING COMMENT '销售所属行政城市，如：北京市、上海市、广州市等',
    `zone_name` STRING COMMENT '区域名称，如：杭州湾、东莞、浙南、深圳等',
    `m1` STRING COMMENT '城市负责人（M1），即销售的直接上级管理者',
    `m2` STRING COMMENT '区域负责人（M2），即M1的直接上级管理者',
    `m3` STRING COMMENT '部门负责人（M3），即M2的直接上级管理者',
    `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、糖水/水果捞、蛋糕店、其他',
    `order_cust_cnt` BIGINT COMMENT '下单客户数，统计当日下单的客户数量',
    `order_sku_cnt` BIGINT COMMENT '销量，统计当日销售的商品SKU数量',
    `order_cnt` BIGINT COMMENT '订单数，统计当日产生的订单数量',
    `real_total_amt` DECIMAL(38,18) COMMENT '订单实付金额，统计当日订单的实际支付金额',
    `drop_in_visit_cust_cnt` BIGINT COMMENT '上门拜访客户数（上门/有效），统计当日实际上门拜访的有效客户数量',
    `visit_cust_cnt` BIGINT COMMENT '总拜访客户数，统计当日所有拜访的客户数量'
)
COMMENT 'BD粒度代售业绩报表日汇总表，按销售人员维度统计每日的代售业绩数据，包括订单、拜访等相关指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据所属日期'
)
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='BD代售业绩日报表，包含销售人员每日的业绩指标和拜访数据')
LIFECYCLE 30;