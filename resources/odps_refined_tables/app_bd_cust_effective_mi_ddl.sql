CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_bd_cust_effective_mi`(
	`month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	`zone_name` STRING COMMENT '运营区域，枚举值包括：东莞、佛山、厦门、四川、大粤西、广州、广西、徽京、无锡、昆明、杭州、杭州湾、武汉、江西、济南、浙南、浦东、浦西、深圳、福泉、苏北、苏州、贵阳、重庆、长沙、青岛',
	`bd_id` BIGINT COMMENT 'BD ID，唯一标识一个BD',
	`bd_name` STRING COMMENT 'BD姓名',
	`m1_name` STRING COMMENT 'M1管理者（销售主管）姓名，即BD的直接上级',
	`m2_name` STRING COMMENT 'M2管理者（销售经理）姓名，即M1的直接上级',
	`m3_name` STRING COMMENT 'M3管理者（销售总监）姓名，即M2的直接上级',
	`cust_m1` STRING COMMENT '客户区域M1管理者姓名',
	`is_same_city` STRING COMMENT '是否同城，枚举值：是/否',
	`dlv_cust_cnt` BIGINT COMMENT '履约客户数，统计周期内完成履约的客户数量',
	`effect_cust_cnt` BIGINT COMMENT '月活客户数，统计周期内的活跃客户数量',
	`time_scheduling` DECIMAL(38,18) COMMENT '时间进度，表示时间完成比例，取值范围0-1'
)
COMMENT 'BD履约有效月活客户数统计表，记录各BD的履约客户数和月活客户数等关键指标'
PARTITIONED BY (
	`ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示年月日，如20250922表示2025年9月22日'
)
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	'comment'='BD履约有效月活客户数统计表，用于分析BD的客户服务效果和活跃度')
LIFECYCLE 30;