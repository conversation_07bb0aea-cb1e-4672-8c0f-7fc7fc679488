CREATE TABLE IF NOT EXISTS app_service_warehouse_cost_wi(
    `year` STRING COMMENT '年份，格式：yyyy',
    `week_of_year` STRING COMMENT '周数，取值范围：1-53',
    `monday` STRING COMMENT '周一日期，格式：yyyyMMdd（年月日）',
    `sunday` STRING COMMENT '周日日期，格式：yyyyMMdd（年月日）',
    `service_area` STRING COMMENT '服务区域，取值范围：华中、华西、华东、广西、华南、福建、贵阳、华北、昆明、None',
    `warehouse_no` BIGINT COMMENT '库存仓编号',
    `warehouse_name` STRING COMMENT '库存仓名称，取值范围：嘉兴海盐总仓、长沙总仓、华西总仓、昆明总仓、嘉兴水果批发总仓、东莞冷冻总仓、济南总仓、苏州总仓、南宁总仓、嘉兴总仓、东莞总仓、福州总仓、美团虚拟代下单总仓、南京总仓、贵阳总仓、上海总仓、多多买菜温州仓、广州总仓、重庆总仓、青岛总仓、武汉总仓、杭州总仓',
    `total_storage_amt` DECIMAL(38,18) COMMENT '总仓储费用（元）',
    `self_storage_amt` DECIMAL(38,18) COMMENT '自营总仓储费用（元）',
    `heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶总仓储费用（元）',
    `warehouse_fixed_amt` DECIMAL(38,18) COMMENT '仓储固资折旧费用（元）',
    `total_out_sku_cnt` BIGINT COMMENT '总出库商品件数',
    `self_out_sku_cnt` BIGINT COMMENT '自营出库商品件数',
    `heytea_out_sku_cnt` BIGINT COMMENT '喜茶出库商品件数',
    `hdl_out_sku_cnt` BIGINT COMMENT '海底捞出库商品件数',
    `hdl_out_sku_wei_cnt` DECIMAL(38,18) COMMENT '海底捞出库商品件数（重量折算）',
    `product_loss_amt` DECIMAL(38,18) COMMENT '产品损耗费用（元）',
    `self_person_amt` DECIMAL(38,18) COMMENT '自营人工费用（元）',
    `heytea_person_amt` DECIMAL(38,18) COMMENT '喜茶人工费用（元）',
    `self_rental_amt` DECIMAL(38,18) COMMENT '自营租赁费用（元）',
    `heytea_rental_amt` DECIMAL(38,18) COMMENT '喜茶租赁费用（元）',
    `self_handling_amt` DECIMAL(38,18) COMMENT '自营装卸费用（元）',
    `heytea_handling_amt` DECIMAL(38,18) COMMENT '喜茶装卸费用（元）',
    `self_consumable_amt` DECIMAL(38,18) COMMENT '自营耗材费用（元）',
    `heytea_consumable_amt` DECIMAL(38,18) COMMENT '喜茶耗材费用（元）',
    `self_office_amt` DECIMAL(38,18) COMMENT '自营办公用品费用（元）',
    `heytea_office_amt` DECIMAL(38,18) COMMENT '喜茶办公用品费用（元）',
    `self_utilities_amt` DECIMAL(38,18) COMMENT '自营水电费用（元）',
    `heytea_utilities_amt` DECIMAL(38,18) COMMENT '喜茶水电费用（元）',
    `self_equipment_amt` DECIMAL(38,18) COMMENT '自营设备租赁费用（元）',
    `heytea_equipment_amt` DECIMAL(38,18) COMMENT '喜茶设备租赁费用（元）',
    `self_other_amt` DECIMAL(38,18) COMMENT '自营其他费用（元）',
    `heytea_other_amt` DECIMAL(38,18) COMMENT '喜茶其他费用（元）'
)
COMMENT '区域库存仓仓储费用明细表，按周统计各仓库的仓储费用和出库情况'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）')
STORED AS ALIORC
TBLPROPERTIES ('comment'='区域库存仓仓储费用明细表，包含各仓库的仓储费用、出库件数和各项费用明细')
LIFECYCLE 30;