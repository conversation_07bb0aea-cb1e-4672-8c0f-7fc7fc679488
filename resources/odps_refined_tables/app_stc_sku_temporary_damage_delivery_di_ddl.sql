CREATE TABLE IF NOT EXISTS app_stc_sku_temporary_damage_delivery_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`sku_id` STRING COMMENT 'SKU编号，商品最小库存单位的唯一标识',
	`spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
	`temporary_store_cnt` BIGINT COMMENT '转入批次临保库存数量，表示转入临保品仓库的商品数量',
	`temporary_store_amt` DECIMAL(38,18) COMMENT '转入批次临保库存金额，表示转入临保品仓库的商品金额',
	`temporary_sale_cnt` BIGINT COMMENT 'SKU性质为临保的当日销售数量，临保品当日销售的商品数量',
	`temporary_sale_amt` DECIMAL(38,18) COMMENT 'SKU性质为临保的当日销售金额，临保品当日销售的商品金额',
	`temporary_damage_cnt` BIGINT COMMENT 'SKU性质为临保的当日货损数量，临保品当日发生货损的商品数量',
	`temporary_damage_amt` DECIMAL(38,18) COMMENT 'SKU性质为临保的当日货损金额，临保品当日发生货损的商品金额',
	`temporary_deliver_amt` DECIMAL(38,18) COMMENT 'SKU性质为临保的当日配送GMV（除批发），临保品当日配送的GMV金额',
	`deliver_total_amt` DECIMAL(38,18) COMMENT '整体当日配送GMV（除批发），所有商品当日配送的GMV总金额'
) 
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据的分区日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='临保品仓库数据信息表，记录临保品的库存、销售、货损和配送等相关数据') 
LIFECYCLE 30;