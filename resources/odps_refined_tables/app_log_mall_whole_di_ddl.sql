CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_mall_whole_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `cust_type` STRING COMMENT '客户行业类型，枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
  `life_cycle` STRING COMMENT '生命周期标签（粗），枚举值：成长期、稳定期、适应期、准流失期、导入期、沉默期、已流失期、新人期',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举值：A1、A2、S1、S2、W、L2、N0、L1、L3、A3、B1、B2、N1、N2',
  `register_province` STRING COMMENT '注册时省份名称',
  `register_city` STRING COMMENT '注册时城市名称',
  `register_area` STRING COMMENT '注册时区县名称',
  `city_id` BIGINT COMMENT '运营服务区ID',
  `city_name` STRING COMMENT '运营服务区名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID',
  `large_area_name` STRING COMMENT '运营服务大区名称，枚举值：南宁大区、苏州大区、苏南大区、杭州大区、上海大区、重庆大区、广州大区、成都大区、青岛大区、长沙大区、武汉大区、福州大区、昆明大区、昆明快递大区、可可快递服务区、柠季快递大区、贵阳大区、广东一点点快递区域',
  `login_pv` BIGINT COMMENT '登陆页面浏览量',
  `login_uv` BIGINT COMMENT '登陆独立访客数',
  `search_pv` BIGINT COMMENT '搜索页面浏览量',
  `search_uv` BIGINT COMMENT '搜索独立访客数',
  `classify_pv` BIGINT COMMENT '首页分类页面浏览量',
  `classify_uv` BIGINT COMMENT '首页分类独立访客数',
  `sku_click_pv` BIGINT COMMENT '商品点击页面浏览量',
  `sku_click_uv` BIGINT COMMENT '商品点击独立访客数',
  `sku_exposure_pv` BIGINT COMMENT '商品详情页曝光页面浏览量',
  `sku_exposure_uv` BIGINT COMMENT '商品详情页曝光独立访客数',
  `sku_cart_pv` BIGINT COMMENT '商品购物车拉起曝光页面浏览量',
  `sku_cart_uv` BIGINT COMMENT '商品购物车拉起曝光独立访客数',
  `sku_cart_buy_pv` BIGINT COMMENT '商品加购页面浏览量',
  `sku_cart_buy_uv` BIGINT COMMENT '商品加购独立访客数',
  `sku_order_order_cnt` BIGINT COMMENT '购买次数',
  `sku_order_cust_cnt` BIGINT COMMENT '购买人数',
  `home_activity_pv` BIGINT COMMENT '首页活动页面浏览量',
  `home_activity_uv` BIGINT COMMENT '首页活动独立访客数',
  `large_activity_pv` BIGINT COMMENT '大banner页面浏览量',
  `large_activity_uv` BIGINT COMMENT '大banner独立访客数',
  `medium_activity_pv` BIGINT COMMENT '中banner页面浏览量',
  `medium_activity_uv` BIGINT COMMENT '中banner独立访客数',
  `home_banner_pv` BIGINT COMMENT 'homeBanner页面浏览量',
  `home_banner_uv` BIGINT COMMENT 'homeBanner独立访客数',
  `timing_pv` BIGINT COMMENT '省心精选页面浏览量',
  `timing_uv` BIGINT COMMENT '省心精选独立访客数',
  `activity_pv` BIGINT COMMENT '特价专区页面浏览量',
  `activity_uv` BIGINT COMMENT '特价专区独立访客数',
  `recommend_pv` BIGINT COMMENT '常用推荐页面浏览量',
  `recommend_uv` BIGINT COMMENT '常用推荐独立访客数',
  `temporary_pv` BIGINT COMMENT '临保特价页面浏览量',
  `temporary_uv` BIGINT COMMENT '临保特价独立访客数',
  `self_pv` BIGINT COMMENT '自营专区页面浏览量',
  `self_uv` BIGINT COMMENT '自营专区独立访客数',
  `full_category_pv` BIGINT COMMENT '全品类页面浏览量',
  `full_category_uv` BIGINT COMMENT '全品类独立访客数',
  `else_source_pv` BIGINT COMMENT '其它source页面浏览量',
  `else_source_uv` BIGINT COMMENT '其它source独立访客数',
  `tabs_pv` BIGINT COMMENT 'tabs页面浏览量',
  `tabs_uv` BIGINT COMMENT 'tabs独立访客数',
  `tabs_1_pv` BIGINT COMMENT 'tabs位置1页面浏览量',
  `tabs_1_uv` BIGINT COMMENT 'tabs位置1独立访客数',
  `tabs_2_pv` BIGINT COMMENT 'tabs位置2页面浏览量',
  `tabs_2_uv` BIGINT COMMENT 'tabs位置2独立访客数',
  `tabs_3_pv` BIGINT COMMENT 'tabs位置3页面浏览量',
  `tabs_3_uv` BIGINT COMMENT 'tabs位置3独立访客数',
  `tabs_other_pv` BIGINT COMMENT '其余位置tabs页面浏览量',
  `tabs_other_uv` BIGINT COMMENT '其余位置tabs独立访客数',
  `home_category_1_pv` BIGINT COMMENT '首页分类位置1页面浏览量',
  `home_category_1_uv` BIGINT COMMENT '首页分类位置1独立访客数',
  `home_category_2_pv` BIGINT COMMENT '首页分类位置2页面浏览量',
  `home_category_2_uv` BIGINT COMMENT '首页分类位置2独立访客数',
  `home_category_3_pv` BIGINT COMMENT '首页分类位置3页面浏览量',
  `home_category_3_uv` BIGINT COMMENT '首页分类位置3独立访客数',
  `home_category_4_pv` BIGINT COMMENT '首页分类位置4页面浏览量',
  `home_category_4_uv` BIGINT COMMENT '首页分类位置4独立访客数',
  `home_category_5_pv` BIGINT COMMENT '首页分类位置5页面浏览量',
  `home_category_5_uv` BIGINT COMMENT '首页分类位置5独立访客数',
  `home_category_6_pv` BIGINT COMMENT '首页分类位置6页面浏览量',
  `home_category_6_uv` BIGINT COMMENT '首页分类位置6独立访客数',
  `home_category_7_pv` BIGINT COMMENT '首页分类位置7页面浏览量',
  `home_category_7_uv` BIGINT COMMENT '首页分类位置7独立访客数',
  `home_category_8_pv` BIGINT COMMENT '首页分类位置8页面浏览量',
  `home_category_8_uv` BIGINT COMMENT '首页分类位置8独立访客数',
  `home_category_9_pv` BIGINT COMMENT '首页分类位置9页面浏览量',
  `home_category_9_uv` BIGINT COMMENT '首页分类位置9独立访客数',
  `home_category_10_pv` BIGINT COMMENT '首页分类位置10页面浏览量',
  `home_category_10_uv` BIGINT COMMENT '首页分类位置10独立访客数',
  `view_time_cust_avg` DECIMAL(38,18) COMMENT '人均浏览时长，单位为秒',
  `view_time_avg` DECIMAL(38,18) COMMENT '单次平均浏览时长，单位为秒',
  `sku_instant_buy_pv` BIGINT COMMENT '商品立即购买页面浏览量',
  `sku_instant_buy_uv` BIGINT COMMENT '商品立即购买独立访客数'
) 
COMMENT '商城整体流量分析表，包含商城各维度的流量指标数据，用于分析用户行为和流量分布'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商城整体流量分析表，包含客户团队类型、行业类型、生命周期标签、地域信息和各类页面流量指标') 
LIFECYCLE 30;