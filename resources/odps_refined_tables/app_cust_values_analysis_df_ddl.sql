```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_values_analysis_df` (
  `cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
  `cust_name` STRING COMMENT '客户名称',
  `register_date` BIGINT COMMENT '注册时间，格式为yyyyMMdd，表示年月日',
  `register_province` STRING COMMENT '注册时省份',
  `register_city` STRING COMMENT '注册时城市',
  `register_area` STRING COMMENT '注册时区域',
  `area_name` STRING COMMENT '运营区域名称',
  `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、蛋糕店、糖水/水果捞、面包蛋糕点心、其他',
  `brand_name` STRING COMMENT '品牌名称',
  `cust_scale` STRING COMMENT '客户规模；枚举值：无、NKA、LKA、其他连锁',
  `sale_area` STRING COMMENT '销售区域',
  `bd_name` STRING COMMENT 'BD名称',
  `m1_name` STRING COMMENT 'BD对应M1管理者名称',
  `m2_name` STRING COMMENT 'BD对应M2管理者名称',
  `m3_name` STRING COMMENT 'BD对应M3管理者名称',
  `load_day_cnt` BIGINT COMMENT '近30天登录天数',
  `load_day_ratio` DECIMAL(38,18) COMMENT '近30天登录间隔（天）',
  `pv` DECIMAL(38,18) COMMENT '页面曝光PV（页面浏览量）',
  `view_time` DECIMAL(38,18) COMMENT '页面浏览时长（小时）',
  `view_spu_cnt` BIGINT COMMENT '曝光SPU数量',
  `click_spu_cnt` BIGINT COMMENT '点击SPU数量',
  `category1_top` STRING COMMENT '一级类目偏好TOP1',
  `category2_top` STRING COMMENT '二级类目偏好TOP1',
  `category3_top` STRING COMMENT '三级类目偏好TOP1',
  `category4_top` STRING COMMENT '四级类目偏好TOP1',
  `spu_name_top` STRING COMMENT 'SPU偏好TOP3，多个SPU用逗号分隔',
  `origin_total_amt` DECIMAL(38,18) COMMENT '自营应付GMV（元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '自营实付GMV（元）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '自营省心送应付GMV（元）',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付金额（元）',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付金额（元）',
  `dlv_cost_amt` DECIMAL(38,18) COMMENT '履约成本金额（元）',
  `fruit_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '鲜果应付金额（元）',
  `fruit_dlv_real_total_amt` DECIMAL(38,18) COMMENT '鲜果实付金额（元）',
  `fruit_dlv_cost_amt` DECIMAL(38,18) COMMENT '鲜果成本金额（元）',
  `dairy_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '乳制品应付金额（元）',
  `dairy_dlv_real_total_amt` DECIMAL(38,18) COMMENT '乳制品实付金额（元）',
  `dairy_dlv_cost_amt` DECIMAL(38,18) COMMENT '乳制品成本金额（元）',
  `other_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '其他品类应付金额（元）',
  `other_dlv_real_total_amt` DECIMAL(38,18) COMMENT '其他品类实付金额（元）',
  `other_dlv_cost_amt` DECIMAL(38,18) COMMENT '其他品类成本金额（元）',
  `fruit_dlv_spu_cnt` BIGINT COMMENT '鲜果SPU数量',
  `dairy_dlv_spu_cnt` BIGINT COMMENT '乳制品SPU数量',
  `other_dlv_spu_cnt` BIGINT COMMENT '其他品类SPU数量',
  `total_point` BIGINT COMMENT '履约点位数',
  `total_delivery_cnt` BIGINT COMMENT '履约频次（配送次数）',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '履约总费用（元）',
  `warehouse_total_amt` DECIMAL(38,18) COMMENT '仓储费用（元）',
  `trunk_total_amt` DECIMAL(38,18) COMMENT '干线费用（元）',
  `delivery_amt` DECIMAL(38,18) COMMENT '配送费用（元）',
  `activity_preference_amt` DECIMAL(38,18) COMMENT '特价营销费用（元）',
  `milk_preference_amt` DECIMAL(38,18) COMMENT '奶卡营销费用（元）',
  `sale_preference_amt` DECIMAL(38,18) COMMENT '销售营销费用（元）',
  `dlv_frequency` DECIMAL(38,18) COMMENT '近90天平均履约间隔（天）',
  `dlv_stdv` DECIMAL(38,18) COMMENT '近90天平均履约间隔标准差',
  `delivery_date` DATETIME COMMENT '最近一次履约时间，格式为yyyy-MM-dd HH:mm:ss，表示年月日时分秒',
  `cust_life_cycle` STRING COMMENT '客户生命周期标签；枚举值：新客、观察期、稳定、跳跃活跃、间隔活跃、风险、沉睡、流失',
  `cust_life_ltv` DECIMAL(38,18) COMMENT '月LTV价值（元）',
  `cust_life_ltv_values` DECIMAL(38,18) COMMENT '存活LTV价值（元）',
  `dlv_profit_label` STRING COMMENT '利润标签分层',
  `dlv_profit_group` STRING COMMENT '利润标签汇总',
  `spu_cnt` BIGINT COMMENT 'SPU总数'
) 
COMMENT '平台客户分析2.0表，包含客户基本信息、行为数据、交易数据、履约数据和生命周期分析等全方位客户价值分析指标'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '平台客户价值分析表，用于客户分层、生命周期管理和价值评估',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```