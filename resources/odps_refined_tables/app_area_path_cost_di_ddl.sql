```sql
CREATE TABLE IF NOT EXISTS app_area_path_cost_di(
    delivery_date STRING COMMENT '配送日期，格式：yyyyMMdd，表示年月日',
    area_no BIGINT COMMENT '配送仓编号，取值范围：1-86',
    area_name STRING COMMENT '配送仓名称，枚举值：杭州仓、温州仓、湛江茂名仓、宁波仓、金丽衢仓、成都仓、昆明仓、桂林仓',
    path STRING COMMENT '线路编号，枚举值：A-Z的单个字母',
    service_area STRING COMMENT '服务区域，目前数据均为None',
    carrier_name STRING COMMENT '承运商名称，目前数据均为None',
    car_id BIGINT COMMENT '车辆编号，目前数据均为None',
    driver STRING COMMENT '司机姓名，目前数据均为None',
    traffic_amt DECIMAL(38,18) COMMENT '打车费用，单位：元',
    purchase_amt DECIMAL(38,18) COMMENT '帮采费用，单位：元',
    extras_amt DECIMAL(38,18) COMMENT '杂项费用，单位：元',
    extras_remark STRING COMMENT '杂费备注说明'
)
COMMENT '路线维度配送成本表，记录各配送路线在不同日期的成本明细数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='路线维度配送成本明细表，包含配送日期、配送仓、线路、承运商、车辆、司机等维度的成本数据',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```