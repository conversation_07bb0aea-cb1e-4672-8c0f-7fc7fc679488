CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_external_product_df` (
  `goods_name` STRING COMMENT '货品名称，如：王婆牌宁夏无籽麒麟西瓜4头36~44斤B级',
  `baby_name` STRING COMMENT 'baby名称，通常与货品名称相同',
  `skucode` STRING COMMENT 'sku编码，商品唯一标识',
  `specification` STRING COMMENT '规格描述，如：约39斤、50-60、中果等',
  `unit` STRING COMMENT '单位，枚举值：PIECE-件、SINGLE-单件、BOX_CASE-箱、BAG-袋',
  `volume` STRING COMMENT '体积，单位：立方厘米',
  `net_weight` DECIMAL(38,18) COMMENT '净重，单位：斤',
  `gross_weight` DECIMAL(38,18) COMMENT '毛重，单位：斤',
  `status` STRING COMMENT '状态，枚举值：ALREADY_UP-已上架',
  `standard_price` DECIMAL(38,18) COMMENT '标准价格，单位：元',
  `activity_price` DECIMAL(38,18) COMMENT '活动价格，单位：元',
  `single_limited` STRING COMMENT '单次限购数量',
  `attalisticon` STRING COMMENT '商品图标URL地址',
  `seller_type` STRING COMMENT '售卖类型，枚举值：BUYER-买家、BUSINESS-商家',
  `length` DECIMAL(38,18) COMMENT '长，单位：厘米',
  `width` DECIMAL(38,18) COMMENT '宽，单位：厘米',
  `height` DECIMAL(38,18) COMMENT '高，单位：厘米',
  `back_category_id` BIGINT COMMENT '后台类目id',
  `back_category_code` STRING COMMENT '后台类目编码，层级结构如：/1/500/637/640/675/',
  `back_category_name` STRING COMMENT '后台类目名称，如：麒麟西瓜、蜜桔、油桃等',
  `goods_type` STRING COMMENT '货品类型，枚举值：ORDINARY-普通商品',
  `interval_status` STRING COMMENT '内部状态，枚举值：DISABLED-禁用、ENABLED-启用',
  `goods_prop_detail_list` STRING COMMENT '属性信息，JSON格式字符串',
  `competitor` STRING COMMENT '竞对信息，枚举值：标果-杭州',
  `category_name` STRING COMMENT '类目名称，枚举值：未分类',
  `goods_detail` STRING COMMENT '详情描述，HTML格式字符串'
)
COMMENT '外部平台商品表，存储来自外部平台的商品基础信息和属性数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='外部平台商品数据表，包含商品基础信息、价格、规格、类目等详细信息',
  'columnar.nested.type'='true'
)
LIFECYCLE 30;