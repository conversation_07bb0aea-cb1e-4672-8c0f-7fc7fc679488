```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_label_summary_detail_df` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd',
  `cust_team` STRING COMMENT '客户团队类型，枚举：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `invite_type` STRING COMMENT '来源类型，枚举：bd、driver、cust、自然注册',
  `life_cycle` STRING COMMENT '生命周期标签（粗），枚举：导入期、已流失期、准流失期、成长期、沉默期、稳定期、适应期、新人期',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举：N0、L3、L2、A1、B1、B2、L1、S1、S2、W、A2、A3、N1、N2',
  `cust_type` STRING COMMENT '客户类型，枚举：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他、蛋糕店、水果/果切/榨汁店、水果店、水果捞/果切店、社区生鲜店、菜市场水果摊、请选经营类型',
  `register_province` STRING COMMENT '注册时省份',
  `register_city` STRING COMMENT '注册时城市',
  `cust_value` STRING COMMENT '用户价值标签，枚举：无、低低低、低中低、低中中、低低中、高高中、高高高、中中中、中低低、高低中、低低高、低中高、低高中、中高中、低高高、中低中、低高低、高中中、高中低、高高低、高中高、中中高、中中低、中高低、高低低、中低高、中高高、高低高',
  `r_value` STRING COMMENT 'R价值标签，枚举：L、R1、R2、R3、R4',
  `f_value` STRING COMMENT 'F价值标签，枚举：L、F1、F2、F3、F4',
  `m_value` STRING COMMENT 'M价值标签，枚举：L、M1、M2、M3、M4',
  `is_first_register_30d` BIGINT COMMENT '是否在近30日是首次注册客户：0-否，1-是',
  `cust_cnt` BIGINT COMMENT '客户数',
  `private_cust_cnt` BIGINT COMMENT '私海客户数（分配但未离职）',
  `disbaled_private_cust_cnt` BIGINT COMMENT '私海锁定客户数（进行分配但离职）',
  `order_real_amt_60d` DECIMAL(38,18) COMMENT '近60天交易实付金额',
  `delivery_real_amt_60d` DECIMAL(38,18) COMMENT '近60天履约实付金额',
  `delivery_profit_60d` DECIMAL(38,18) COMMENT '近60天履约实付毛利润',
  `private_order_real_amt_60d` DECIMAL(38,18) COMMENT '近60天私海客户数（分配但未离职）交易实付金额',
  `private_delivery_real_amt_60d` DECIMAL(38,18) COMMENT '近60天私海客户数（分配但未离职）履约实付金额',
  `private_delivery_profit_60d` DECIMAL(38,18) COMMENT '近60天私海客户数（分配但未离职）履约实付毛利润',
  `open_order_real_amt_60d` DECIMAL(38,18) COMMENT '近60天公海交易实付金额',
  `open_delivery_real_amt_60d` DECIMAL(38,18) COMMENT '近60天公海履约实付金额',
  `open_delivery_profit_60d` DECIMAL(38,18) COMMENT '近60天公海履约实付毛利润',
  `order_dairy_real_amt_60d` DECIMAL(38,18) COMMENT '近60天乳制品交易实付金额',
  `order_fruit_real_amt_60d` DECIMAL(38,18) COMMENT '近60天鲜果交易实付金额',
  `order_other_real_amt_60d` DECIMAL(38,18) COMMENT '近60天其他交易实付金额',
  `order_real_amt_365d` DECIMAL(38,18) COMMENT '近365天交易实付金额',
  `delivery_real_amt_365d` DECIMAL(38,18) COMMENT '近365天履约实付金额',
  `delivery_profit_365d` DECIMAL(38,18) COMMENT '近365天履约实付毛利润',
  `register_audit_duration_avg` DECIMAL(38,18) COMMENT '平均审核通过时长（首次注册-首次审核）（小时）',
  `audit_relation_duration_avg` DECIMAL(38,18) COMMENT '平均审核分配时长（首次审核-首次分配）（小时）',
  `relation_follow_duration_avg` DECIMAL(38,18) COMMENT '平均拜访时长（首次分配-首次拜访）（小时）',
  `follow_order_duration_avg` DECIMAL(38,18) COMMENT '平均下单时长（首次拜访-首次下单）（小时）',
  `follow_efficient_cnt_30d_avg` DECIMAL(38,18) COMMENT '近30日平均有效拜访次数（有效与上门）',
  `login_cust_cnt_30d` BIGINT COMMENT '近30日登录客户数',
  `sku_cl_cust_cnt_30d` BIGINT COMMENT '近30日点击客户数',
  `login_private_cust_cnt_30d` BIGINT COMMENT '近30日登录私海客户数（分配但未离职）',
  `blacklist_back_cust_cnt` BIGINT COMMENT '拉黑回流门店数',
  `bd_cnt` BIGINT COMMENT '跟进bd数（非离职）',
  `order_real_amt_30d` DECIMAL(38,18) COMMENT '近30天交易实付金额'
)
COMMENT '客户标签汇总明细表，包含客户标签体系、价值评估、交易行为等多维度汇总数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户标签汇总明细表，用于客户分层分析和精细化运营',
  'lifecycle' = '30'
)
```