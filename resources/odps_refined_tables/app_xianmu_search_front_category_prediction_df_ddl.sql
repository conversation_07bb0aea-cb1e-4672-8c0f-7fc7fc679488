CREATE TABLE IF NOT EXISTS app_xianmu_search_front_category_prediction_df(
    query STRING COMMENT '搜索词，用户输入的搜索关键词',
    predicated_front_category_name STRING COMMENT '预测的前端类目名称，基于用户点击历史预测的类目归属，可能包含多个类目（用逗号分隔）'
)
COMMENT '基于用户点击历史记录的搜索词-前端类目预测结果表，数据来源于app_xianmu_search_category_prediction_df'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='搜索词前端类目预测表，通过机器学习算法预测搜索词最可能归属的前端类目',
    'columnar.nested.type'='true'
)
LIFECYCLE 180;