CREATE TABLE IF NOT EXISTS app_finance_cash_after_sale_details_di(
	after_sale_order_id STRING COMMENT '售后单编号',
	order_no STRING COMMENT '订单编号',
	order_item_id BIGINT COMMENT '订单项编号',
	delivery_path_id BIGINT COMMENT 'delivery_path的ID',
	service_area STRING COMMENT '大区，取值范围：未知、山东、华东、福建、西南、华中、贵州、广西',
	province STRING COMMENT '省，取值范围：广东、山东、浙江、上海、福建、四川、湖南、江苏、湖北、重庆、贵州、广西壮族自治区、江西、安徽',
	city STRING COMMENT '市，取值范围：佛山市、烟台市、温州市、上海市、莆田市、杭州市、成都市、株洲市、深圳市、湖州市、苏州市、武汉市、中山市、宁波市、泉州市、重庆市、绍兴市、南京市、贵阳市、汕头市、长沙市、泰州市、南宁市、南昌市、湘潭市、常州市、永州市、惠州市、萍乡市、绵阳市、益阳市、蚌埠市、嘉兴市、芜湖市、广州市、威海市、金华市、宜春市、合肥市、青岛市、新余市、肇庆市',
	sku STRING COMMENT 'SKU',
	pd_name STRING COMMENT '商品名',
	category1 STRING COMMENT '商品一级类目，取值范围：乳制品、鲜果、其他',
	tax_rate DECIMAL(38,18) COMMENT '税率',
	finish_time DATETIME COMMENT '售后完结时间，格式：年月日时分秒',
	after_sale_amt DECIMAL(38,18) COMMENT '售后退款金额（含税）',
	after_sale_amt_notax DECIMAL(38,18) COMMENT '售后退款金额（不含税）',
	after_sale_in_sku_cnt BIGINT COMMENT '售后入库数量',
	after_sale_in_cost DECIMAL(38,18) COMMENT '售后入库成本金额（含税）',
	after_sale_in_cost_notax DECIMAL(38,18) COMMENT '售后入库成本金额（不含税）',
	after_sale_add_sku_cnt BIGINT COMMENT '售后补发数量',
	after_sale_add_cost DECIMAL(38,18) COMMENT '售后补发成本金额（含税）',
	after_sale_add_cost_notax DECIMAL(38,18) COMMENT '售后补发成本金额（不含税）',
	cust_team STRING COMMENT '客户团队类型，取值范围：平台客户',
	handle_type STRING COMMENT '售后服务类型，取值范围：退款、退货退款、补发、拒收退款',
	after_sale_add_lack_sku_cnt BIGINT COMMENT '售后补发(缺货导致)商品数量',
	after_sale_add_lack_revenue_amt DECIMAL(38,18) COMMENT '售后补发(缺货导致)确认收入金额（含税）',
	after_sale_add_lack_revenue_amt_notax DECIMAL(38,18) COMMENT '售后补发(缺货导致)确认收入金额（不含税）',
	sub_type BIGINT COMMENT '商品二级性质，取值范围：1-自营-代销不入仓、2-自营-代销入仓、3-自营-经销、4-代仓-代仓',
	settle_type STRING COMMENT '结算类型，取值范围：成本结算、空值'
) 
COMMENT '财务口径现结收入明细表，记录售后相关的财务结算明细数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式的年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='财务口径现结收入明细表，包含售后订单的财务结算相关信息') 
LIFECYCLE 30;