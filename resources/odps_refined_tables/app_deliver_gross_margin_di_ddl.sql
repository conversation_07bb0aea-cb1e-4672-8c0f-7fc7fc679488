CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_deliver_gross_margin_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `cust_class` STRING COMMENT '客户大类，取值范围：大客户（茶百道）、大客户（非茶百道）、平台客户、批发客户、普通（非品牌）、普通（品牌）',
  `warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识',
  `warehouse_name` STRING COMMENT '库存仓名称，取值范围：上海总仓、嘉兴总仓、华西总仓、重庆总仓、福州总仓、长沙总仓、昆明总仓、青岛总仓、东莞总仓、南京总仓、武汉总仓、南宁总仓、苏州总仓、贵阳总仓、东莞冷冻总仓、嘉兴海盐总仓、济南总仓等',
  `spu_name` STRING COMMENT 'SPU名称，商品标准产品单元名称',
  `category_1` STRING COMMENT '一级类目，取值范围：乳制品、其他、鲜果',
  `sku_type` STRING COMMENT 'SKU类型，取值范围：自营、代仓',
  `origin_total_amt` DECIMAL(38,18) COMMENT '配送总金额，精确到小数点后18位',
  `cost_amt` DECIMAL(38,18) COMMENT '配送总成本，精确到小数点后18位',
  `gross_margin` DECIMAL(38,18) COMMENT '毛利润，精确到小数点后18位'
)
COMMENT '每日库存仓客户类型组合维度日汇总数据，按库存仓、客户类型、商品类目等维度汇总的配送金额、成本和毛利润数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '每日库存仓客户类型组合维度日汇总数据表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;