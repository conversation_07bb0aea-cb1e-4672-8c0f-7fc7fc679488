CREATE TABLE IF NOT EXISTS app_dlv_pop_supplier_final_di(
	`date` STRING COMMENT '业务日期，格式为yyyyMMdd，表示数据统计的日期',
	`supplier` STRING COMMENT '供应商名称，如：刘东、李强、杨志闯、汪高洋、龙飞果业等',
	`order_sku_cnt` BIGINT COMMENT '下单商品数量，统计周期内供应商下单的商品总数',
	`order_sku_weight` DECIMAL(38,18) COMMENT '下单商品总毛重，单位为斤，保留18位小数精度',
	`order_total_amt` DECIMAL(38,18) COMMENT '供货总金额，单位为元，统计周期内供应商供货的总金额',
	`take_total_amt` DECIMAL(38,18) COMMENT '提货费用，单位为元，供应商提货产生的费用',
	`after_sale_short_sku_cnt` BIGINT COMMENT '售后缺货商品数量，统计周期内因缺货导致的售后商品数量',
	`after_sale_short_sku_weight` DECIMAL(38,18) COMMENT '售后缺货商品总毛重，单位为斤，缺货售后商品的总重量',
	`after_sale_short_total_amt` DECIMAL(38,18) COMMENT '售后缺货总金额，单位为元，缺货售后产生的总金额',
	`after_sale_quality_sku_cnt` BIGINT COMMENT '售后质量商品数量，统计周期内因质量问题导致的售后商品数量',
	`after_sale_quality_sku_weight` DECIMAL(38,18) COMMENT '售后质量商品总毛重，单位为斤，质量售后商品的总重量',
	`after_sale_quality_total_amt` DECIMAL(38,18) COMMENT '售后质量总金额，单位为元，质量售后产生的总金额',
	`commission_after_sale_short_amt` DECIMAL(38,18) COMMENT '售后缺货金额/(1+佣金比例)，计算后的缺货售后佣金调整金额',
	`commission_after_sale_quality_amt` DECIMAL(38,18) COMMENT '售后质量金额/(1+佣金比例)，计算后的质量售后佣金调整金额'
) 
COMMENT '供应商对账汇总表，用于统计供应商的订单、售后、佣金等对账相关数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='供应商对账业务汇总表，包含订单统计、售后处理、佣金计算等核心对账指标') 
LIFECYCLE 30;