```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_goods_expiration_summary_di`(
  `time_tag` STRING COMMENT '日期，格式：yyyyMMdd',
  `tenant_id` BIGINT COMMENT '租户ID，取值范围：2-109',
  `sku_id` BIGINT COMMENT 'SaaS SKU ID，取值范围：100590-123566',
  `warehouse_no` BIGINT COMMENT '仓库编号，取值范围：2-173',
  `warehouse_name` STRING COMMENT '仓库名称，枚举值：总仓、普冷武汉仓、虚拟仓库、普冷长沙仓、上海总仓、上海莲谷仓、福州总仓、jj上海自营仓',
  `batch` STRING COMMENT '批次号，格式：数字和字母组合的批次标识',
  `expiration_date` DATETIME COMMENT '有效期，格式：年月日时分秒（yyyy-MM-dd HH:mm:ss）',
  `expiration_batch_stock` BIGINT COMMENT '过期时批次库存数量，取值范围：1-20000',
  `ending_batch_stock` BIGINT COMMENT '期末库存数量，取值范围：0-20000',
  `item_id` BIGINT COMMENT '商品ID，取值范围：591-41916',
  `sale_price` DECIMAL(38,18) COMMENT '销售价格，单位：元'
)
COMMENT 'SaaS近30天货品过期汇总表，统计各租户商品的过期库存情况'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS货品过期汇总表，用于分析商品过期趋势和库存管理',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```