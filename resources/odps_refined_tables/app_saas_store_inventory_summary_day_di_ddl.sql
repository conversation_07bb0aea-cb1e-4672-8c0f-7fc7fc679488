CREATE TABLE IF NOT EXISTS app_saas_store_inventory_summary_day_di(
	`summary_date` DATETIME COMMENT '汇总日期，格式为年月日时分秒',
	`tenant_id` BIGINT COMMENT '租户ID，取值范围：2-100',
	`store_id` BIGINT COMMENT '门店ID，取值范围：4-539049',
	`item_id` BIGINT COMMENT '商品SKU ID，取值范围：1-44932',
	`specification` STRING COMMENT '商品SKU规格，示例值：21斤*2盒、500G*12袋、0_10斤*1盒等',
	`main_picture` STRING COMMENT '商品主图URL路径，示例值：upload/4ktkrljz8kkkboxsz.jpg、https://azure.cosfo.cn/upload/6c224rp0v3sdml1pk.png等',
	`title` STRING COMMENT '商品SKU标题，示例值：帆台测试代仓商品、阿拉比卡咖啡豆、ljj自营货品5.6等',
	`store_inventory_unit` STRING COMMENT '商品库存单位，枚举值：盒、袋、箱、包、斤、G、个、罐、盆、KG、L、桶、组、瓶、卷、条、块、台、颗、捆、份、片、mL、只、张、套、件、顶',
	`initial_quantity` DECIMAL(38,18) COMMENT '期初数量',
	`initial_amount` DECIMAL(38,18) COMMENT '期初金额',
	`initial_unit_price` DECIMAL(38,18) COMMENT '期初库存单位单价',
	`other_in_quantity` DECIMAL(38,18) COMMENT '其他入库数量',
	`other_in_amount` DECIMAL(38,18) COMMENT '其他入库金额',
	`order_in_quantity` DECIMAL(38,18) COMMENT '订货入库数量',
	`order_in_amount` DECIMAL(38,18) COMMENT '订货入库金额',
	`stock_gain_in_quantity` DECIMAL(38,18) COMMENT '盘盈入库数量',
	`stock_gain_in_amount` DECIMAL(38,18) COMMENT '盘盈入库金额',
	`reship_in_quantity` DECIMAL(38,18) COMMENT '补发入库数量',
	`reship_in_amount` DECIMAL(38,18) COMMENT '补发入库金额',
	`total_in_quantity` DECIMAL(38,18) COMMENT '入库合计数量',
	`total_in_amount` DECIMAL(38,18) COMMENT '入库合计金额',
	`other_out_quantity` DECIMAL(38,18) COMMENT '其他出库数量',
	`other_out_amount` DECIMAL(38,18) COMMENT '其他出库金额',
	`sales_out_quantity` DECIMAL(38,18) COMMENT '销售出库数量',
	`sales_out_amount` DECIMAL(38,18) COMMENT '销售出库金额',
	`stock_loss_out_quantity` DECIMAL(38,18) COMMENT '盘亏出库数量',
	`stock_loss_out_amount` DECIMAL(38,18) COMMENT '盘亏出库金额',
	`return_out_quantity` DECIMAL(38,18) COMMENT '退货出库数量',
	`out_loss_amount` DECIMAL(38,18) COMMENT '报损出库金额',
	`out_loss_quantity` DECIMAL(38,18) COMMENT '报损出库数量',
	`return_out_amount` DECIMAL(38,18) COMMENT '退货出库金额',
	`total_out_quantity` DECIMAL(38,18) COMMENT '出库合计数量',
	`total_out_amount` DECIMAL(38,18) COMMENT '出库合计金额',
	`final_quantity` DECIMAL(38,18) COMMENT '期末数量',
	`final_amount` DECIMAL(38,18) COMMENT '期末金额',
	`final_unit_price` DECIMAL(38,18) COMMENT '期末库存单位单价'
) 
COMMENT '门店出入库汇总日表，记录门店商品每日的期初、入库、出库、期末等库存变动汇总信息'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='门店出入库汇总日表，用于分析门店库存变动情况和商品流转效率')
LIFECYCLE 30;