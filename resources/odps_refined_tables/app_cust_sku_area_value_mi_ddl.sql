```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_sku_area_value_mi` (
  `month` STRING COMMENT '月份，格式：yyyyMM',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位唯一标识',
  `spu_name` STRING COMMENT '商品名称，标准产品单元名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格、包装等信息',
  `cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
  `cust_name` STRING COMMENT '客户名称',
  `cust_type` STRING COMMENT '客户业态，取值范围：面包蛋糕、甜品冰淇淋、茶饮、咖啡、其他、西餐、水果/果切/榨汁店',
  `large_area` STRING COMMENT '运营大区，取值范围：杭州大区、上海大区、苏南大区、苏州大区、成都大区、广州大区、重庆大区、福州大区、长沙大区、武汉大区、青岛大区、南宁大区、昆明大区、贵阳大区',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV，原始交易总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV，实际支付交易金额',
  `sku_cnt` BIGINT COMMENT '交易销量，商品销售数量',
  `order_cnt` BIGINT COMMENT '交易频次（天），订单数量',
  `nearly_order_time` DATETIME COMMENT '最近一次下单时间，格式：yyyy-MM-dd HH:mm:ss',
  `dlv_total_origin_amt` DECIMAL(38,18) COMMENT '履约应付GMV，原始履约总金额',
  `dlv_total_real_amt` DECIMAL(38,18) COMMENT '履约实付GMV，实际支付履约金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `dlv_sku_cnt` BIGINT COMMENT '履约销量，履约商品数量',
  `dlv_order_cnt` BIGINT COMMENT '履约频次（天），履约订单数量',
  `nearly_deliver_time` DATETIME COMMENT '最近一次履约时间，格式：yyyy-MM-dd HH:mm:ss',
  `is_last_cust` STRING COMMENT '是否上月留存客户，取值范围：是、否',
  `cust_label` STRING COMMENT '新/老客标识，取值范围：新客、老客'
)
COMMENT '客户品类价值表，记录客户在不同品类商品上的交易和履约价值数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户品类价值分析表，用于分析客户在不同商品品类上的交易行为和履约表现',
  'lifecycle' = '30'
);
```