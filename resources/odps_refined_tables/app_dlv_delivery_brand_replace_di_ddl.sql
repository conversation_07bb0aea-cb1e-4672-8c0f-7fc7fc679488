CREATE TABLE IF NOT EXISTS app_dlv_delivery_brand_replace_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`brand_name` STRING COMMENT '品牌名称，即公司的正式注册名称',
	`brand_alias` STRING COMMENT '大客户别称，即品牌在业务中的常用简称或昵称',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，即订单原始应付金额总计',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，即订单实际支付金额总计',
	`deliver_total_weight` DECIMAL(38,18) COMMENT '配送总重量，单位为千克',
	`brand_cnt` BIGINT COMMENT '客户数（品牌），取值范围为1，表示每个品牌对应1个客户',
	`cust_cnt` BIGINT COMMENT '门店数，取值范围为1-102，表示每个品牌对应的门店数量',
	`order_cnt` BIGINT COMMENT '订单数，取值范围为1-193，表示每个品牌对应的订单数量',
	`data_source` STRING COMMENT '数据来源，枚举值：鲜沐、SaaS'
) 
COMMENT '履约代仓品牌汇总表，记录各品牌在代仓履约业务中的关键指标汇总数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='履约代仓品牌汇总表') 
LIFECYCLE 30;