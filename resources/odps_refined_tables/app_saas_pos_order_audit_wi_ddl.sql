```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_pos_order_audit_wi`(
    `channel_type` BIGINT COMMENT '渠道类型：1=美团，3=其他（根据数据样本显示当前只有3）',
    `tenant_id` BIGINT COMMENT '租户id，当前样本中固定为88',
    `report_week` STRING COMMENT '稽核自然周-周一日期，格式为yyyyMMdd，表示每周一的日期',
    `out_store_code` STRING COMMENT '外部系统门店编码，如：210002、210004等',
    `out_store_name` STRING COMMENT '外部系统门店名称，如：谷人说·中国谷饮（淮海中路店）',
    `merchant_store_id` BIGINT COMMENT '帆台门店id，数值型标识',
    `merchant_store_code` STRING COMMENT '帆台门店编码，如：0210002、0210004等',
    `out_item_code` STRING COMMENT '外部系统物料编码，如：28712、28452等',
    `out_item_name` STRING COMMENT '外部系统物料名称，如：90-520膜内贴注塑杯、90-注塑杯盖等',
    `market_item_id` BIGINT COMMENT '帆台商品id，与外部物料编码对应',
    `specification` STRING COMMENT '规格描述，如：20个*25条、50个*20条等',
    `specification_unit` STRING COMMENT '规格单位：箱、包',
    `use_count` DECIMAL(38,18) COMMENT '销用总量，小数精度18位',
    `need_buy_count` DECIMAL(38,18) COMMENT '应进货总量，小数精度18位',
    `real_buy_count` BIGINT COMMENT '实际帆台进货总量，整数值'
)
COMMENT 'SaaS门店进销稽核表(周)，用于统计门店每周的商品销售、进货稽核数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='SaaS门店进销稽核周表，包含门店商品销售和进货的稽核统计信息',
    'lifecycle'='30'
);
```