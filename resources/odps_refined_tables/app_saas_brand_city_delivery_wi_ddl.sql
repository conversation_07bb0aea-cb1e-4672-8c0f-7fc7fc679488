CREATE TABLE IF NOT EXISTS app_saas_brand_city_delivery_wi(
    `year` STRING COMMENT '年份，格式：YYYY',
    `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
    `monday` STRING COMMENT '周一日期，格式：YYYYMMDD，年月日',
    `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD，年月日',
    `brand_alias` STRING COMMENT '品牌名称，枚举值包括：GIGI LUCKY舒芙蕾、VQ、一只酸奶牛、乳果说茶饮、八街手作（口口椰）、咕鹿流心披萨、山山不夜、川町太郎、御贵人、怡满分杭州saas、新加坡斯味洛鲜奶茶、日尝、有堂古、椿风、榴莲嘟嘟、爆珠公·老红糖珍珠鲜奶茶、瑞杰斯、益禾堂、肯豆、艾炒酸奶、菟竹集、蔡小甜、裕蘭茶楼、谷人说订货小站、赵记鲜果、遇见村上订货、银座仁志川订货系统等',
    `province` STRING COMMENT '省份，枚举值包括：广东、江苏、浙江、四川、湖南、重庆、安徽、上海、福建、广西壮族自治区等',
    `city` STRING COMMENT '城市，枚举值包括：佛山市、广州市、深圳市、清远市、南京市、镇江市、杭州市、德阳市、成都市、绵阳市、宁波市、长沙市、重庆市、无锡市、蚌埠市、常州市、上海市、惠州市、珠海市、苏州市、金华市、泉州市、湖州市、台州市、嘉兴市、温州市、东莞市、中山市、徐州市、扬州市、泰州市、福州市、莆田市、张家界市、怀化市、永州市、湘潭市、湘西土家族苗族自治州、益阳市、衡阳市、邵阳市、郴州市、厦门市、南宁市、常德市等',
    `area` STRING COMMENT '区域/区县，包含顺德区、天河区、宝安区、清新区、秦淮区、京口区、上城区、广汉市、双流区、成华区等约178个枚举值',
    `point_cnt` BIGINT COMMENT '点位数，取值范围：1-18'
)
COMMENT 'SaaS履约网络可视化表，展示各品牌在不同城市区域的点位分布情况'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：YYYYMMDD，年月日')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='SaaS履约网络可视化表，按品牌-省份-城市-区域维度统计点位数分布')
LIFECYCLE 30;