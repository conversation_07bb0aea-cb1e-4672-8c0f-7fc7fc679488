CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_operate_category_delivery_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `category` STRING COMMENT '商品品类，取值范围：鲜果、乳制品、其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，原始应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，实际支付总金额',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用，营销活动产生的费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额，商品成本总额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润，原始应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润，实际支付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率，原始应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率，实际支付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数，完成履约的客户数量',
  `point_cnt` BIGINT COMMENT '点位数，服务网点数量',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价，原始应付平均客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价，实际支付平均客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV，省心送服务原始应付金额',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV，省心送服务实际支付金额',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV，代售服务原始应付金额',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV，代售服务实际支付金额',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用，代售服务营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润，代售服务原始应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润，代售服务实际支付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数，代售服务完成履约的客户数量',
  `turnover_day_cnt` DECIMAL(38,18) COMMENT '周转天数，库存周转天数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额，货物损坏损失金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费，仓储服务费用',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费，单点干线运输费用',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费，采购自提产生的费用',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费，货物调拨产生的费用',
  `other_amt` DECIMAL(38,18) COMMENT '其他费，其他杂项费用',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费，商品配送产生的费用'
) 
COMMENT '运营履约KPI指标表（平台客户），包含各品类商品的履约相关财务和运营指标数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '运营履约KPI指标表（平台客户），用于分析各品类商品的履约效率、成本和利润等关键指标',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;