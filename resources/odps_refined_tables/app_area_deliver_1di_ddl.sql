```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_area_deliver_1di` (
  `area_id` BIGINT COMMENT '仓库编号，唯一标识每个配送仓库',
  `area_name` STRING COMMENT '仓库名称，枚举值包括：杭州仓、上海仓、宁波仓、苏州仓、嘉兴仓、广州仓、南京仓、湖州仓、无锡仓、金丽衢仓、成都仓、温州仓、东莞仓、深圳仓、佛山仓、重庆仓、台州仓、合肥仓、扬州仓、淮安仓、中山仓、惠州仓、福州仓、泉州仓、厦门仓、长沙仓、南昌仓、武汉仓、莆田仓、荆门仓、常德仓、宜春仓、南宁仓、昆明仓、南通仓、贵阳仓、潮汕仓、青岛仓、襄阳仓、衡阳仓、杭州三仓、上海六仓、盐城仓、慈溪仓、徐州仓、株洲仓、烟台仓、秀洲仓、济南仓等',
  `car_cnt` BIGINT COMMENT '车次数，配送车辆出车次数',
  `short_car_cnt` BIGINT COMMENT '缺货车次数，因缺货导致无法完成配送的车次数量',
  `point_cnt` BIGINT COMMENT '点位数，需要配送的总点位数量',
  `point_cnt_b14h` BIGINT COMMENT '14点前完成点位数，在下午2点前完成配送的点位数量',
  `short_point_cnt` BIGINT COMMENT '缺货点位数，因缺货无法完成配送的点位数量',
  `order_cnt` BIGINT COMMENT '订单数，总订单数量',
  `short_order_cnt` BIGINT COMMENT '缺货订单数，因缺货无法完成的订单数量',
  `cust_cnt` BIGINT COMMENT '客户数，服务客户总数',
  `short_cust_cnt` BIGINT COMMENT '缺货客户数，因缺货受影响的客户数量',
  `sku_cnt` BIGINT COMMENT 'SKU数，库存商品品类总数',
  `short_sku_cnt` BIGINT COMMENT '缺货SKU数，缺货的商品品类数量',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总价，实际成交金额，保留18位小数精度',
  `short_real_total_amt` DECIMAL(38,18) COMMENT '缺货实际总价，因缺货损失的实际成交金额，保留18位小数精度',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总价，订单原始金额，保留18位小数精度',
  `short_origin_total_amt` DECIMAL(38,18) COMMENT '缺货原始总价，因缺货损失的原始订单金额，保留18位小数精度',
  `point_in_time` BIGINT COMMENT '及时完成点位数，按时完成配送的点位数量'
) 
COMMENT '配送仓库维度按天汇总表，统计各配送仓库每日的配送完成情况、缺货情况及相关业务指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '配送仓库业务指标日汇总表',
  'lifecycle' = '30'
);
```