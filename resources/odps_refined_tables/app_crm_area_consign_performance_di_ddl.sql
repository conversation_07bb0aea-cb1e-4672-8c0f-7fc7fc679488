CREATE TABLE IF NOT EXISTS app_crm_area_consign_performance_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`administrative_city` STRING COMMENT '行政城市名称，如：上海市',
	`area` STRING COMMENT '行政区名称，如：浦东新区、徐汇区等',
	`zone_name` STRING COMMENT '销售所属区域名称，枚举值：浦东、浦西',
	`m1` STRING COMMENT '城市负责人（M1管理者）姓名',
	`m2` STRING COMMENT '区域负责人（M2管理者）姓名',
	`m3` STRING COMMENT '部门负责人（M3管理者）姓名',
	`cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、其他',
	`order_cust_cnt` BIGINT COMMENT '下单客户数，统计当日下单的独立客户数量',
	`order_sku_cnt` BIGINT COMMENT '销量，统计当日销售的商品SKU总数量',
	`order_cnt` BIGINT COMMENT '订单数，统计当日产生的订单总数',
	`real_total_amt` DECIMAL(38,18) COMMENT '订单实付金额，统计当日订单的实际支付总金额',
	`drop_in_visit_cust_cnt` BIGINT COMMENT '上门拜访客户数（上门/有效），统计当日有效上门拜访的客户数量',
	`visit_cust_cnt` BIGINT COMMENT '总拜访客户数，统计当日所有拜访的客户总数'
) 
COMMENT '区域粒度代售业绩报表日汇总表，按行政区域和销售区域维度统计代售业务业绩指标（目前数据主要覆盖上海市）'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='区域代售业绩日汇总表，包含各区域销售指标和拜访数据统计') 
LIFECYCLE 30;