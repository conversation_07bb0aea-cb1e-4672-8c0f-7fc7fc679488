```sql
CREATE TABLE IF NOT EXISTS app_stc_damage_detail_report_df(
    outbound_date DATETIME COMMENT '出库日期，格式：年月日时分秒',
    damage_no STRING COMMENT '货损批次编号',
    warehouse_no BIGINT COMMENT '仓库编号',
    warehouse_name STRING COMMENT '仓库名称，枚举值：长沙总仓、上海总仓、福州总仓、苏州总仓、昆明总仓、杭州总仓、嘉兴总仓、广州总仓、东莞总仓、jj上海自营仓、贝塔余杭仓A、广州总部仓、重构测试仓、测试总仓仓、ljj杭州自营仓、自营仓库A、南宁总仓、湿地测试仓、ljj余杭自营仓、广东省内虚拟仓库、总仓、东莞冷冻总仓、虚拟仓库、重庆总仓、嘉兴海盐总仓、南京总仓、华西总仓、青岛总仓、自建仓库、kuddo、徐州联艺玻璃工艺品有限公司、椿风原料加工厂-清芳溪、虚拟仓-设备、红塔仓库、普冷武汉仓、绝配北京仓、上海莲谷仓、北京总仓、普冷长沙仓、普冷南京仓、武汉总仓、河南商丘等',
    sku_id BIGINT COMMENT '商品SKU ID',
    spu_id BIGINT COMMENT '商品SPU ID',
    xianmu_sku STRING COMMENT '鲜沐SKU编码',
    xianmu_spu_id BIGINT COMMENT '鲜沐SPU ID',
    name STRING COMMENT '商品名称',
    specification STRING COMMENT '商品规格',
    unit STRING COMMENT '计量单位，枚举值：筐、盒、包、箱、件、瓶、块、罐、组、袋、桶、份、台、个、套、卷、条等',
    purchaser STRING COMMENT '采购人姓名',
    damage_type STRING COMMENT '货损类型，枚举值：变质货损、过期货损、其他、不可抗力-客户原因、仓-破损、不可抗力-其它、仓-其它、运营部-滞销过期、干-破损、仓-质检、调拨货损、配-破损、配-失温、不可抗力-自然灾害、运营部-商品下架、干-失温、仓-过期、采购部-品质问题、干-其它、货检货损、退货货损、配-其它、仓-品质问题、品控部-在库切检等',
    credentials STRING COMMENT '货损凭证信息，包含图片路径或文字描述',
    damage_quantity BIGINT COMMENT '货损数量',
    damage_amount DECIMAL(38,18) COMMENT '货损金额',
    tenant_id BIGINT COMMENT '租户ID',
    category_id BIGINT COMMENT '一级类目ID',
    time_tag STRING COMMENT '时间标签，格式：yyyyMMdd',
    warehouse_service_provider STRING COMMENT '仓库服务商，枚举值：杭州鲜沐科技有限公司、广州肆捌城餐饮管理有限公司、客思服（杭州）科技有限公司、杭州五二兰餐饮管理有限公司、上海文雷餐饮有限公司等',
    price DECIMAL(38,18) COMMENT '采购单价'
)
COMMENT 'SaaS货损明细表，记录商品货损的详细信息，包括货损类型、数量、金额、凭证等'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='SaaS货损明细表，用于记录和分析商品货损情况',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```