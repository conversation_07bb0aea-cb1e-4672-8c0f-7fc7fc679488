```sql
CREATE TABLE IF NOT EXISTS app_replenishment_sku_cycle_statistics_df(
    `supplier_id` BIGINT COMMENT '供应商ID，数值型标识',
    `supplier_name` STRING COMMENT '供应商名称，文本描述',
    `warehouse_no` BIGINT COMMENT '仓库编号，数值型标识',
    `warehouse_name` STRING COMMENT '仓库名称，文本描述',
    `sku` STRING COMMENT '库存单位编码，商品唯一标识',
    `purchase_quantity` BIGINT COMMENT '采购数量，数值型，范围0-150000',
    `unsubscribe_quantity` BIGINT COMMENT '退订数量，数值型，范围0-553',
    `return_quantity` BIGINT COMMENT '退货数量，数值型，范围0-1075',
    `cycle_type` BIGINT COMMENT '周期类型：0-月度，1-季度，2-半年度，3-年度'
)
COMMENT '补货SKU周期统计表，记录各供应商、仓库、SKU在不同周期内的采购、退订、退货数量统计'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '补货SKU周期统计表，用于分析供应商、仓库、SKU的采购行为周期特征',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```