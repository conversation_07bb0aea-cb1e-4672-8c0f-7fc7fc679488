```sql
CREATE TABLE IF NOT EXISTS app_srm_supplier_consignment_warehouse_stock_df(
    supplier_id BIGINT COMMENT '供应商ID，数值型标识',
    supplier_name STRING COMMENT '供应商名称，文本类型，包含如"杭州萧山丰年贸易有限公司"等供应商全称',
    warehouse_no BIGINT COMMENT '仓库编号，数值型标识，取值范围10-155',
    warehouse_name STRING COMMENT '仓库名称，文本类型，枚举值包括：嘉兴总仓、华西总仓、南宁总仓、东莞总仓、东莞冷冻总仓、武汉总仓、嘉兴海盐总仓、重庆总仓、长沙总仓、青岛总仓、南京总仓、福州总仓、济南总仓、昆明总仓、贵阳总仓、重构测试仓',
    sku STRING COMMENT 'SKU编码，商品唯一标识，文本类型',
    spu_title STRING COMMENT '商品名称，文本类型，描述商品具体名称',
    sku_sub_type BIGINT COMMENT '商品二级性质，枚举类型：1-自营代销不入仓，2-自营代销入仓',
    stock_quantity BIGINT COMMENT '供应商库存量，数值型，记录当前库存数量',
    risk_quantity BIGINT COMMENT '临保风险量，数值型，标识临近保质期的库存数量',
    sales_14d DECIMAL(38,18) COMMENT '近14天销量均值（包含小规格销量），数值型，保留18位小数精度',
    date_flag STRING COMMENT '同步时间标记，格式为yyyyMMdd，表示年月日'
) 
COMMENT '供应商代销入仓库存表，记录供应商代销商品的入仓库存信息，包括库存量、风险量和销售数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='供应商代销入仓库存明细表，用于跟踪和管理供应商代销商品的库存状况') 
LIFECYCLE 30;
```