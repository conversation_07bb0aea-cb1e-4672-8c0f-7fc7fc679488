CREATE TABLE IF NOT EXISTS app_after_sale_rate_di(
	`city_id` BIGINT COMMENT '城市ID，数值型标识',
	`city_name` STRING COMMENT '城市名称，如：杭州、上海、广州等',
	`sku_id` STRING COMMENT '商品SKU编码，唯一商品标识',
	`sku_type` STRING COMMENT '商品类型：自营-平台自营商品，代仓-第三方仓库代发商品',
	`spu_name` STRING COMMENT '商品名称，标准产品单元名称',
	`sku_disc` STRING COMMENT 'SKU描述信息，包含规格、重量等描述，如：1L*12盒、400g*1包',
	`category1` STRING COMMENT '一级类目：鲜果-新鲜水果类，乳制品-奶制品类，其他-其他商品类别',
	`warehouse_no` BIGINT COMMENT '库存仓编号，数值型仓库标识',
	`warehouse_name` STRING COMMENT '库存仓名称，如：嘉兴总仓、上海总仓、东莞总仓等',
	`external_asl_amt` DECIMAL(38,18) COMMENT '客户原因或未配送售后金额，单位：元',
	`external_asl_suborder_cnt` BIGINT COMMENT '客户原因或未配送售后子订单数量',
	`short_asl_amt` DECIMAL(38,18) COMMENT '缺货售后金额，单位：元',
	`short_asl_suborder_cnt` BIGINT COMMENT '缺货售后子订单数量',
	`quality_asl_amt` DECIMAL(38,18) COMMENT '质量售后金额，单位：元',
	`quality_asl_suborder_cnt` BIGINT COMMENT '质量售后子订单数量',
	`less_asl_amt` DECIMAL(38,18) COMMENT '少称售后金额，单位：元',
	`less_asl_suborder_cnt` BIGINT COMMENT '少称售后子订单数量',
	`other_asl_amt` DECIMAL(38,18) COMMENT '其他售后金额，单位：元',
	`other_asl_suborder_cnt` BIGINT COMMENT '其他售后子订单数量',
	`asl_amt` DECIMAL(38,18) COMMENT '总售后金额，单位：元',
	`asl_suborder_cnt` BIGINT COMMENT '总售后子订单数量',
	`dlv_real_gmv_with_coupon` DECIMAL(38,18) COMMENT '配送实付金额+券优惠金额，单位：元',
	`dlv_sub_order_cnt` BIGINT COMMENT '配送子订单数量'
) 
COMMENT '售后率统计表，按城市、商品SKU、仓库等多维度统计各类售后情况的金额和订单数量'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='售后率分析表，用于监控和分析各维度下的售后情况，包括客户原因、缺货、质量、少称等不同类型的售后数据统计') 
LIFECYCLE 30;