CREATE TABLE IF NOT EXISTS app_sale_city_kpi_trade_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
	`administrative_city` STRING COMMENT '注册行政城市，枚举值包括：红河哈尼族彝族自治州、湖州市、临沧市、曲靖市、无锡市、自贡市、邵阳市、景德镇市、泰州市等全国城市',
	`zone_name` STRING COMMENT '销售区域名称，枚举值包括：无、昆明、杭州湾、无锡、四川、长沙、江西、苏北、广州、徽京、青岛、大粤西、福泉、武汉、泰山测试001、贵阳、泰山测试、厦门、佛山、广西、浙南、薄荷测试、东莞、虚拟区域、重庆、济南、苏州、测试权限数据、深圳、浦东、杭州',
	`m1` STRING COMMENT '城市负责人（M1），枚举值包括：无、蒋柳选、翟远方、李光远、陶京龙、陈锐石、习燕庆、葛世豪、林献、冯朝皇、陈忠良、陈俊生、张浩亮、唐宽、李钦、汪林俊、罗慧、骆婷婷、韦贵丰、王业鼎、李茂源、肖时煌、庄典、王金浩、陈洪亮、张茂权、徐晟昊、李钱程',
	`m2` STRING COMMENT '区域负责人（M2），枚举值包括：无、孙日达、翟远方、桂少达、姜浪、彭琨、陈欲豪、孙军杰、林金秋、李钦、李茂源、高偌桐、赵奎',
	`m3` STRING COMMENT '部门负责人（M3），枚举值包括：无、孙日达、吕建杰、李钦、李茂源、剑锋',
	`order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额，单位为元',
	`order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额，单位为元',
	`order_cust_cnt` BIGINT COMMENT '交易客户数，统计当日有交易行为的客户数量',
	`order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)，平均每个客户贡献的收入',
	`order_cnt` BIGINT COMMENT '交易订单数，统计当日完成的订单数量',
	`lose_cust_cnt` BIGINT COMMENT '交易流失客户数（90天内活跃用户近60天未下单客户数）',
	`lose_cust_ratio` DECIMAL(38,18) COMMENT '交易流失率，流失客户数占总客户数的比例',
	`delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位为元',
	`delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位为元',
	`delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数，统计当日有履约行为的客户数量',
	`delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润，单位为元',
	`delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润，单位为元',
	`delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润，单位为元',
	`delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次，平均履约天数',
	`delivery_point_cnt` BIGINT COMMENT '履约点位数，统计当日有履约行为的点位数量',
	`delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用，单位为元',
	`new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额，单位为元',
	`new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额，单位为元',
	`new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数，统计当日新客户的履约数量',
	`new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润，单位为元',
	`old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额，单位为元',
	`old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额，单位为元',
	`old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数，统计当日老客户的履约数量',
	`old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润，单位为元',
	`at_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约应付总金额，单位为元',
	`at_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付总金额，单位为元',
	`at_delivery_cust_cnt` BIGINT COMMENT '(乳品)安佳铁塔履约活跃客户数',
	`at_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付毛利润，单位为元',
	`noat_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约应付总金额，单位为元',
	`noat_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付总金额，单位为元',
	`noat_delivery_cust_cnt` BIGINT COMMENT '(乳品)非安佳铁塔履约活跃客户数',
	`noat_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付毛利润，单位为元',
	`timing_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付总金额，单位为元',
	`timing_delivery_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付总金额，单位为元',
	`timing_delivery_cust_cnt` BIGINT COMMENT '省心送履约活跃客户数',
	`timing_delivery_real_profit` DECIMAL(38,18) COMMENT '省心送履约实付毛利润，单位为元',
	`order_sku_cnt` BIGINT COMMENT '交易SKU款数，统计当日交易的SKU种类数量',
	`order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG），单位为千克',
	`delivery_sku_cnt` BIGINT COMMENT '履约SKU款数，统计当日履约的SKU种类数量',
	`delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG），单位为千克'
) 
COMMENT '销售KPI指标汇总表，按城市和销售区域维度统计交易和履约相关指标，包括订单金额、客户数、毛利润、SKU数量等核心业务指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='销售KPI指标汇总表，用于销售业绩分析和业务监控') 
LIFECYCLE 30;