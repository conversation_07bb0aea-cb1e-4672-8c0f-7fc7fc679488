```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_stc_timing_order_sku_bd_df` (
  `order_date` STRING COMMENT '下单时间，格式：yyyyMMdd，表示年月日',
  `order_time_tag` STRING COMMENT '订单时间打标，取值范围：一个月内订单、二个月内订单、三个月内订单、超过三个月订单',
  `large_area_id` BIGINT COMMENT '运营服务大区ID',
  `large_area_name` STRING COMMENT '运营服务大区名称，取值范围：杭州大区、长沙大区、南宁大区、上海大区、苏州大区、广州大区、福州大区、苏南大区、武汉大区、贵阳大区、成都大区、青岛大区、重庆大区、废区集散地、金华大区',
  `city_id` BIGINT COMMENT '城市ID',
  `city_name` STRING COMMENT '城市名称',
  `cust_id` BIGINT COMMENT '客户ID',
  `cust_name` STRING COMMENT '客户名称',
  `cust_phone` STRING COMMENT '客户电话',
  `bd_id` BIGINT COMMENT '销售ID，-1表示无销售',
  `bd_name` STRING COMMENT '销售名称',
  `m1_id` BIGINT COMMENT '销售主管ID（一级），-1表示无主管',
  `m1_name` STRING COMMENT '销售主管名称（一级）',
  `warehouse_no` BIGINT COMMENT '库存仓号',
  `warehouse_name` STRING COMMENT '库存仓名称，取值范围：-、杭州总仓、上海总仓、金华总仓、不是广东总仓、嘉兴总仓',
  `order_no` STRING COMMENT '订单编号',
  `sku_id` STRING COMMENT 'SKU编号',
  `sku_type` STRING COMMENT 'SKU类型，取值范围：0-自营、1-代仓、2-代售',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品描述',
  `total_sku_cnt` BIGINT COMMENT '下单数量',
  `origin_unit_amt` DECIMAL(38,18) COMMENT '应付单价',
  `sale_now_price` DECIMAL(38,18) COMMENT '最新单价',
  `diff_unit_amt` DECIMAL(38,18) COMMENT '单价差异（最新单价-实付均价）',
  `price_float` STRING COMMENT '价格浮动，取值范围：涨价、持平、降价',
  `timing_dlv_sku_cnt` BIGINT COMMENT '省心送已履约数量',
  `timing_no_dlv_sku_cnt` BIGINT COMMENT '省心送未履约数量',
  `timing_no_dlv_amt` DECIMAL(38,18) COMMENT '未配送部分差异金额',
  `timing_dlv_14_day_sku_cnt` BIGINT COMMENT '省心送未来14天预约配送数量',
  `total_timing_dlv_14_day_sku_cnt` BIGINT COMMENT '省心送未来14天预约配送数量(仓+SKU汇总)',
  `store_quantity` BIGINT COMMENT '在仓数量',
  `road_quantity` BIGINT COMMENT '在途数量',
  `estimated_sales` BIGINT COMMENT '预测销量',
  `remind_replenishment` STRING COMMENT '补货提醒，取值范围：None、-、异常、补货提醒'
) 
COMMENT '省心送补货预警表，用于监控省心送订单的补货需求和预警'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '省心送补货预警表，包含订单信息、商品信息、库存信息和补货提醒等数据',
  'columnar.nested.type' = 'true'
) 
LIFECYCLE 30;
```