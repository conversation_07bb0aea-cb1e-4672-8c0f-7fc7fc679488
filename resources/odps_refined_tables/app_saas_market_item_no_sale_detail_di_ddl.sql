```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_market_item_no_sale_detail_di` (
  `tenant_id` BIGINT COMMENT '租户ID，唯一标识一个租户，取值范围：2-123',
  `time_tag` STRING COMMENT '时间标签，格式为年月日yyyyMMdd，表示数据统计日期',
  `type` BIGINT COMMENT '滞销类型：1-7日滞销；2-30日滞销',
  `item_id` BIGINT COMMENT '商品ID，唯一标识一个商品，取值范围：1-45010',
  `sale_price` DECIMAL(38,18) COMMENT '商品售价，单位为元，支持小数点后18位精度'
)
COMMENT 'SaaS商品滞销明细表，记录各租户商品在指定时间范围内的滞销情况，包括7日和30日滞销商品信息'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为年月日yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS商品滞销明细分析表，用于商品滞销情况监控和分析',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```