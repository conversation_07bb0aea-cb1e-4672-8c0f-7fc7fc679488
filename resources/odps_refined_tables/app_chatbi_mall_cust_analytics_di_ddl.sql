```sql
CREATE TABLE IF NOT EXISTS app_chatbi_mall_cust_analytics_di(
  `cust_id` BIGINT COMMENT '客户ID，与埋点/订单里的cust_id一致',
  `cust_name` STRING COMMENT '客户名称/门店名称',
  `cust_phone` STRING COMMENT '客户电话',
  `cust_register_province` STRING COMMENT '客户注册省份，枚举值：浙江、江苏、上海、湖北、福建、江西、广东、湖南、山东、安徽、四川、重庆、广西壮族自治区、云南、贵州',
  `cust_register_city` STRING COMMENT '客户注册城市，枚举值：杭州市、南京市、绍兴市、苏州市、湖州市、上海市、武汉市、福州市、虹口区、金华市、上饶市、广州市、嘉兴市、温州市、深圳市、长沙市、宁波市、青岛市、常州市、无锡市、合肥市、宜春市、南通市、台州市、成都市、盐城市、泰州市、佛山市、东莞市、中山市、揭阳市、重庆市、湛江市、惠州市、扬州市、镇江市、乐山市、江门市、厦门市、泉州市、宁德市、珠海市、永州市、莆田市、汕头市、德阳市、绵阳市、南昌市、马鞍山市、宜昌市等',
  `cust_register_area` STRING COMMENT '客户注册行政区/区县，枚举值：西湖区、余杭区、拱墅区、钱塘区、秦淮区、临平区、上城区、滨江区、萧山区、下城区、柯桥区、吴中区、富阳区、越城区、德清县、绍兴县、普陀区、浦东新区、长宁区、徐汇区、江岸区、连江县、闵行区、黄浦区、城区、嘉定区、宝山区、金东区、信州区、杨浦区、松江区、婺城区、上虞区、静安区、越秀区、虹口区、海宁市、瓯海区、临安区、福田区、龙岗区、岳麓区、鄞州区、慈溪市、余姚市、姑苏区、海曙区、市南区、李沧区、江北区等',
  `cust_register_date` STRING COMMENT '客户注册日期，格式：yyyyMMdd，表示年月日',
  `is_new_register_cust` BIGINT COMMENT '是否为当日新注册客户，枚举值：1-是，0-否；用于衡量新增',
  `cust_type` STRING COMMENT '客户类型，枚举值：面包蛋糕、茶饮、咖啡、其他、甜品冰淇淋、西餐、蛋糕店、水果/果切/榨汁店',
  `area_name` STRING COMMENT '运营区域名称，枚举值：杭州、南京、温州本部、绍兴、苏州、德清、临安、上海、武汉普冷、福州、金华自营、茶南昌外区、上虞、广州、海宁、茶温州市、深圳、长沙普冷、宁波、余姚、青岛、常州、昆山、嘉兴、无锡、桐乡、常熟、合肥、慈溪、太仓、江阴、义乌、宜春、张家港、安吉县、湖州、宜兴、长兴县、南通、茶金婺永兰磐武、台州本部、诸暨、成都、南浔区、平湖、茶盐城、东阳、泰州、茶百道川二十运费、南宁等',
  `area_no` BIGINT COMMENT '运营区域编号',
  `admin_name` STRING COMMENT '大客户名称；为空则代表平台客户/单店，枚举值：无、富世餐饮管理（上海）有限公司、杭州周四晚餐饮管理有限公司、上海和翼商贸有限公司、杭州临安九头牛面包店、丹尼爱特（宁波）国际贸易有限公司、上海市浦东新区金杨新村街道偶得蛋糕店、上海安上餐饮管理有限公司、澳煜（上海）智能科技有限公司、苏州觉晓文化有限公司、苏州默腾咖啡餐饮管理有限公司、益禾堂（湖北）餐饮管理有限公司、上海安禧餐饮管理有限公司、湖北异想天开生物科技有限公司、南京亿发贸易有限公司、四川蜀信致远企业管理咨询有限公司、四川蜀味茶韵供应链有限公司、悦享麦合（苏州）酒店管理有限公司、梁溪区金金饮品店、深圳市华伟达贸易有限公司、杭州鮨浅草君餐饮管理有限公司江干分公司、南京千可商贸有限公司、深圳茶姬企业管理有限公司、湖州星麦供应链管理有限公司、小福茶（江西）餐饮管理有限公司、昆山语涵食品有限公司、广东书亦餐饮管理有限公司、上海咖嘉电子商务有限公司、武汉茶馨饮品有限公司、武汉与山商贸有限公司、堡仕康餐饮管理（上海）有限公司、苏州肇亿可餐饮管理有限公司、悸动（上海）餐饮管理有限公司、南岸区三样子餐饮店、成都茶百道餐饮管理有限公司、红谷滩区尊品牛排世茂店、嘉兴市晨烨浩食品有限公司、苏州天翼风餐饮管理有限公司、杭州密巢食品贸易有限公司、本本供应链管理（苏州）有限公司、珠海市厚米餐饮管理有限公司、中山市其利断金餐饮店、昆明春耕餐饮管理有限公司、云南味知觉餐饮管理有限公司、南京甜荟餐饮管理有限公司、南京金之玉餐饮管理有限公司、上海函樾供应链有限公司、湖州大野文旅发展有限公司、生根餐饮管理（上海）有限公司、深圳市友谊书城有限公司等',
  `admin_id` STRING COMMENT '大客户ID；保持字符串避免跨系统类型不一致，枚举值：-1、477、600810、324、155、267、1125830、1083225、1028、1860、1133397、1525、459、1529、1824、647、646、470、1174407、1786、1360、576、499、1492、1184516、1170921、648、821、895、738、816、819、1102839、947、1089545、1034、1029216、803、1712、1260、1038190、216、357、600571、1158255、1131、1388、1721、1501、1667等',
  `bd_id` BIGINT COMMENT '客户所属BD ID（仅对私海客户有值），-1表示无BD',
  `bd_name` STRING COMMENT '客户所属BD姓名（仅对私海客户有值），"无"表示无BD，枚举值：无、李钱程、袁自超、杨春福、徐俊杰、宋丹丹、许萧余、严红坤、宋懿航、邵金龙、向邓一、谈敏良、董元鸣、蒋万君、刘宝翠、袁绪钊、许洲城、王云、韩宥予、杜强胜、周通、魏俊杰、任凤珠、白津源、阿晖、吴灿东、曾斌斌、方桂林、汪敏、张继元、廖旭斌、黄柯军、许文烨、周倩倩、冯柠柠、苏煜杰、王恬恬、朱俊宸、黄仁有、李树生、姚晨、张凯彬、杨子荟、翟远方、笪文杰、刘莹莹、梁杰、李梦婷、陈默、肖峰等',
  `cust_sku_viewed_cnt` BIGINT COMMENT '当日该客户浏览过的SKU去重数量，用于衡量浏览活跃度',
  `cust_sku_clicked_cnt` BIGINT COMMENT '当日该客户点击过的SKU去重数量，事件类型=cl',
  `cust_stay_time_minutes` DECIMAL(18,2) COMMENT '当日累计停留时长(分钟)',
  `cust_searched_cnt` BIGINT COMMENT '当日搜索行为次数',
  `cust_first_order_date` STRING COMMENT '客户首单日期，格式：yyyyMMdd，表示年月日',
  `is_cust_first_order_date` BIGINT COMMENT '当日是否为客户首单日，枚举值：1-是，0-否',
  `cust_order_cnt` BIGINT COMMENT '当日下单的去重订单数',
  `cust_order_real_total_amt` DECIMAL(38,18) COMMENT '当日订单实际总金额(优惠后)',
  `cust_order_origin_total_amt` DECIMAL(38,18) COMMENT '当日订单原始总金额(优惠前)',
  `cust_order_sku_ids` BIGINT COMMENT '当日下单涉及的去重SKU数量',
  `cust_order_sku_cnt` BIGINT COMMENT '当日下单SKU件数合计'
) 
COMMENT '商城客户行为与订单分析按日明细表：聚合了埋点日志(dwd_log_mall_di)与订单(dwd_trd_order_df)的关键客户维度指标，用于新增、活跃、转化等分析'
PARTITIONED BY (
  `ds` STRING COMMENT '分区日期，格式：yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
   'comment'='商城客户行为与订单分析按日明细表：聚合了埋点日志与订单的关键客户维度指标，用于新增、活跃、转化等分析')
LIFECYCLE 1000
```