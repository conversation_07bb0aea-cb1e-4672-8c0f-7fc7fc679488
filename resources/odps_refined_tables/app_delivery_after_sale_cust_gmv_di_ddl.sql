```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_delivery_after_sale_cust_gmv_di` (
  `date` STRING COMMENT '业务日期，格式为yyyyMMdd',
  `province` STRING COMMENT '客户注册时所在省份，枚举值包括：无、上海、云南、四川、安徽、山东、广东、广西壮族自治区、江苏、江西、浙江等',
  `city` STRING COMMENT '客户注册时所在城市，枚举值包括：无、上海市、昆明市、德阳市、成都市、绵阳市、六安市、合肥市等全国主要城市',
  `area` STRING COMMENT '客户注册时所在区县，枚举值包括：无、嘉定区、奉贤区、宝山区、崇明区、徐汇区等全国各区县',
  `city_id` BIGINT COMMENT '运营服务区域ID，唯一标识运营服务区域',
  `city_name` STRING COMMENT '运营服务区域名称，枚举值包括：茶象宁奉、上海、上海闵行静安、茶崇明岛、杭州、昆明等',
  `large_area_id` BIGINT COMMENT '运营服务大区ID，唯一标识运营大区',
  `large_area_name` STRING COMMENT '运营服务大区名称，枚举值包括：杭州大区、上海大区、昆明大区、成都大区、苏州大区、青岛大区、广州大区、南宁大区、苏南大区、长沙大区',
  `cust_type` STRING COMMENT '客户类型，枚举值：大客户、普通',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：平台客户、批发、Mars大客户、集团大客户（茶百道）、集团大客户（喜茶）',
  `sku_type` STRING COMMENT '商品经营类型，枚举值：自营、代售、代仓、无',
  `sku_id` STRING COMMENT '商品SKU编码，唯一标识具体商品规格',
  `spu_id` BIGINT COMMENT '商品PD_ID，唯一标识商品品类',
  `spu_name` STRING COMMENT '商品名称，描述商品的具体名称',
  `sku_disc` STRING COMMENT '商品规格描述，包含重量、等级、包装等详细信息',
  `category1` STRING COMMENT '商品一级分类，枚举值：鲜果、乳制品、其他、无',
  `category2` STRING COMMENT '商品二级分类，枚举值：新鲜水果、乳制品、糕点丨面包、谷物制品、饮料、饼干丨糖果丨可可豆制品等',
  `category3` STRING COMMENT '商品三级分类，枚举值：柑果类、核果类、浆果类、瓠果类、黄油、液体乳、冷冻面团等',
  `category4` STRING COMMENT '商品四级分类，枚举值：柠檬、芒果、草莓、蜜瓜、凤梨丨菠萝、无盐黄油、常温牛奶等',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额，含优惠前的总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '配送实付GMV，扣除优惠后的实际支付金额',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额，使用的优惠券总额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，所有售后责任的总和',
  `store_no` BIGINT COMMENT '配送仓编号，唯一标识配送仓库',
  `store_name` STRING COMMENT '配送仓名称，枚举值包括：宁波仓、上海六仓、上海仓、杭州三仓、秀洲仓等全国各配送仓',
  `warehouse_no` BIGINT COMMENT '库存仓编号，唯一标识库存仓库',
  `warehouse_name` STRING COMMENT '库存仓名称，枚举值包括：嘉兴总仓、嘉兴海盐总仓、上海总仓、昆明总仓、华西总仓等全国各总仓',
  `brand_alias` STRING COMMENT '品牌别名，枚举值包括：茉莉奶白、missblue、无、茉沏、上海一点点等各合作品牌',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '配送应付GMV，原始应付金额',
  `delivery_after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额(仓配责)，仓配责任导致的售后金额',
  `purchase_after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额(采购责)，采购责任导致的售后金额',
  `quality_after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额(品控责)，品控责任导致的售后金额',
  `purchase_quality_after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额(采购品控责)，采购和品控共同责任导致的售后金额',
  `other_after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额(其余责任)，其他责任方导致的售后金额',
  `normal_after_sale_amt` DECIMAL(38,18) COMMENT '正常售后金额，常规售后处理的金额',
  `special_after_sale_amt` DECIMAL(38,18) COMMENT '特殊售后金额，特殊情况下售后处理的金额'
)
COMMENT '配送售后GMV明细表，记录配送业务的销售金额、实付金额、优惠金额以及按不同责任方划分的售后金额明细数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '配送售后GMV明细表，用于分析配送业务的销售表现和售后责任划分',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```