CREATE TABLE IF NOT EXISTS app_chatbi_after_sale_summary_df(
    order_no STRING COMMENT '原订单号，关联app_chatbi_cust_orders_df.order_no',
    sku_id STRING COMMENT 'SKU编码，关联app_chatbi_cust_orders_df.sku_id',
    cust_id STRING COMMENT '客户ID，关联app_chatbi_cust_orders_df.cust_id',
    first_after_sale_handle_time STRING COMMENT '首次售后处理时间，格式：年月日时分秒，如：2022-08-11 16:42:11',
    last_after_sale_handle_time STRING COMMENT '最近一次售后处理时间，格式：年月日时分秒，如：2022-08-11 16:42:11',
    after_sale_amount DECIMAL(38,18) COMMENT '售后总金额',
    deliveryed_after_sale_amount DECIMAL(38,18) COMMENT '已配送售后金额（deliveryed=1的售后金额）',
    after_sale_quantity BIGINT COMMENT '售后总数量',
    after_sale_unit STRING COMMENT '售后单位，取值范围：g、盒、包、件、桶、瓶、个、箱、袋、条、罐、G、块、g 、块 、 袋、1、套、片、KG、g/盒、保、张、颗、包1、支、斤、g/个、天等',
    after_sales_times BIGINT COMMENT '此SKU售后次数',
    first_after_sale_handle_date STRING COMMENT '首次售后处理日期，格式：年月日，如：20220811',
    last_after_sale_handle_date STRING COMMENT '最近一次售后处理日期，格式：年月日，如：20220811'
)
COMMENT '售后订单汇总分析表，包含售后订单的汇总统计信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，数据处理日期，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='售后订单汇总分析表，用于分析售后订单的处理情况、金额统计和次数统计')
LIFECYCLE 7;