CREATE TABLE IF NOT EXISTS app_crm_merchant_future_day_gmv_di(
	cust_id BIGINT COMMENT '商户ID，唯一标识商户',
	contact_id BIGINT COMMENT '联系地址ID，关联商户的联系地址',
	bd_id BIGINT COMMENT '归属BD ID，标识负责该商户的商务拓展人员，公海为0',
	delivery_time DATETIME COMMENT '计划配送时间，格式为年月日时分秒',
	area_no BIGINT COMMENT '商户所在运营区域编号',
	distribution_gmv DECIMAL(38,18) COMMENT '配送GMV，总配送金额',
	fruit_gmv DECIMAL(38,18) COMMENT '鲜果类GMV，鲜果产品配送金额',
	dairy_gmv DECIMAL(38,18) COMMENT '乳制品GMV，乳制品配送金额',
	non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品GMV，非乳制品配送金额',
	brand_gmv DECIMAL(38,18) COMMENT '自营品牌GMV，自营品牌产品配送金额',
	reward_gmv DECIMAL(38,18) COMMENT '固定奖励SKU的GMV，奖励商品配送金额',
	spu_num BIGINT COMMENT 'SPU数（去重），商品品类数量',
	estimated_income DECIMAL(38,18) COMMENT '预估收益，预计收益金额',
	min_income_after DECIMAL(38,18) COMMENT '达标后预估最低收益，达标后最低收益金额',
	max_income_after DECIMAL(38,18) COMMENT '达标后预估最高收益，达标后最高收益金额',
	delivery_up_to_standard BIGINT COMMENT '是否达标：0-未达标，1-已达标',
	province STRING COMMENT '省份名称，取值范围包括：浙江、上海、福建、江西、广东、江苏、湖北、湖南、四川、重庆、安徽、山东、广西壮族自治区、云南、贵州、广西等',
	city STRING COMMENT '城市名称，包含全国多个城市',
	area STRING COMMENT '区域名称，包含全国多个区县'
) 
COMMENT '商户当日及未来配送GMV表，记录商户配送相关的GMV数据和收益预估信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商户配送GMV明细表，包含商户配送金额、商品分类GMV、收益预估及达标状态等信息') 
LIFECYCLE 30;