CREATE TABLE IF NOT EXISTS app_log_purchase_function_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`type` STRING COMMENT '实验分组：V3-实验组V3版本、V4-实验组V4版本、None-对照组',
	`cust_cnt` BIGINT COMMENT '客户数',
	`view_uv` BIGINT COMMENT '榜单曝光UV（独立访客数）',
	`click_uv` BIGINT COMMENT '榜单商品点击UV（独立访客数）',
	`click_buy_uv` BIGINT COMMENT '榜单唤起购买UV（独立访客数）',
	`frequently_shop_uv` BIGINT COMMENT '常购商品曝光UV（独立访客数）',
	`click_jump_uv` BIGINT COMMENT '商品点击跳转UV（独立访客数）',
	`buy_uv` BIGINT COMMENT '常购商品唤起购买UV（独立访客数）',
	`add_buy_uv` BIGINT COMMENT '加购点击UV（独立访客数）',
	`click_select_uv` BIGINT COMMENT '点击去选购UV（独立访客数）',
	`remove_buy_uv` BIGINT COMMENT '移除常购商品UV（独立访客数）'
) 
COMMENT '采购助手页面流量数据表，记录采购助手页面的用户行为指标和实验分组数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='采购助手页面流量数据表，包含页面曝光、点击、购买转化等用户行为指标') 
LIFECYCLE 30;