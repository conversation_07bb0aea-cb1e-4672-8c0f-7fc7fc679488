```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_order_item_detail_summary_di`(
  `tenant_id` BIGINT COMMENT '租户ID',
  `order_no` STRING COMMENT '订单编号',
  `order_item_id` STRING COMMENT '订单项编号',
  `store_name` STRING COMMENT '门店名称',
  `store_type` BIGINT COMMENT '门店类型：0-直营店，1-加盟店，2-托管店',
  `group_name` STRING COMMENT '门店分组名称',
  `province` STRING COMMENT '省份',
  `city` STRING COMMENT '城市',
  `area` STRING COMMENT '区域',
  `address` STRING COMMENT '详细收货地址',
  `contact_phone` STRING COMMENT '收货人联系电话',
  `order_time` DATETIME COMMENT '下单时间，格式：年月日时分秒',
  `pay_time` DATETIME COMMENT '支付时间，格式：年月日时分秒',
  `pay_type` BIGINT COMMENT '支付方式：1-线上支付，2-账期，3-余额支付，4-其他支付方式',
  `delivery_time` DATETIME COMMENT '配送时间，格式：年月日时分秒',
  `finished_time` DATETIME COMMENT '确认收货时间，格式：年月日时分秒',
  `item_id` BIGINT COMMENT '商品ID',
  `item_code` STRING COMMENT '商品自有编码',
  `item_title` STRING COMMENT '商品标题',
  `item_specification` STRING COMMENT '商品规格描述',
  `item_pricing_type` BIGINT COMMENT '定价方式：0-百分比上浮，1-定额上浮，2-固定价',
  `item_pricing_number` DECIMAL(38,18) COMMENT '定价数值',
  `payable_price` DECIMAL(38,18) COMMENT '商城售卖单价',
  `amount` BIGINT COMMENT '商城售卖数量',
  `total_price` DECIMAL(38,18) COMMENT '商城售卖总价',
  `delivery_fee` DECIMAL(38,18) COMMENT '运费金额',
  `remark` STRING COMMENT '订单备注信息',
  `sales_and_supply_difference` DECIMAL(38,18) COMMENT '销售与采购差额',
  `goods_sku` STRING COMMENT '货品SKU编码',
  `goods_title` STRING COMMENT '货品名称',
  `goods_specification` STRING COMMENT '货品规格描述',
  `goods_supplier_name` STRING COMMENT '货品供应商名称',
  `goods_type` BIGINT COMMENT '货品类型：0-鲜沐直供，1-代仓，2-其他类型',
  `goods_agent_rule` STRING COMMENT '货品代仓计费规则',
  `goods_agent_fee` DECIMAL(38,18) COMMENT '货品代仓费用',
  `goods_supply_price` DECIMAL(38,18) COMMENT '货品供应单价',
  `goods_supply_total_price` DECIMAL(38,18) COMMENT '货品供应总价',
  `goods_delivery_fee` DECIMAL(38,18) COMMENT '货品配送费用',
  `time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd',
  `store_no` STRING COMMENT '门店编号',
  `contact_name` STRING COMMENT '收货人姓名',
  `first_classification` STRING COMMENT '商品一级分类',
  `second_classification` STRING COMMENT '商品二级分类',
  `supplier_id` BIGINT COMMENT '供应商ID',
  `order_finished_node` STRING COMMENT '订单完成节点：确认收货/送达门店、整单退款/关单',
  `supplier_sku` STRING COMMENT '鲜沐SKU编码',
  `store_id` BIGINT COMMENT '店铺ID',
  `after_sale_unit` STRING COMMENT '售后单位：卷、套、盒、箱、g、个、件、瓶、袋、条、G、罐、包、桶、斤、份、克、筐、块等',
  `supplier_tenant_id` BIGINT COMMENT '原始供应商ID'
) 
COMMENT 'SAAS对账单-订单明细汇总表，包含订单商品明细、货品信息、供应商信息等完整数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SAAS对账单订单明细汇总表，用于财务对账和业务分析') 
LIFECYCLE 30;
```