CREATE TABLE IF NOT EXISTS app_stc_warehouse_kpi_di(
	`date` STRING COMMENT '业务日期，格式：yyyyMMdd',
	`warehouse_no` BIGINT COMMENT '库存仓号，取值范围：2-155',
	`warehouse_name` STRING COMMENT '库存仓名称，枚举值：南宁总仓、南京总仓、东莞总仓、东莞冷冻总仓、嘉兴海盐总仓、上海总仓、长沙总仓、重庆总仓、青岛总仓、苏州总仓、武汉总仓、昆明总仓、福州总仓、贵阳总仓、济南总仓、嘉兴总仓、华西总仓',
	`check_sku_cnt` BIGINT COMMENT '抽检数量',
	`in_bound_sku_cnt` BIGINT COMMENT '入库数量',
	`back_order_cnt` BIGINT COMMENT '退货总单数',
	`finish_order_cnt` BIGINT COMMENT '已完成单数',
	`damage_amt` DECIMAL(38,18) COMMENT '货损金额',
	`damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额——仓配责',
	`sale_amt` DECIMAL(38,18) COMMENT '销售金额',
	`after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额',
	`after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责',
	`after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责',
	`after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责',
	`after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责',
	`after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责',
	`delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额',
	`coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付GMV',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
	`other_amt` DECIMAL(38,18) COMMENT '其他成本',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨成本'
) 
COMMENT '仓配KPI库存数据表，包含各仓库的库存管理关键绩效指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='仓配KPI库存数据表，记录各仓库的库存管理、销售、售后、成本等关键绩效指标数据') 
LIFECYCLE 30;