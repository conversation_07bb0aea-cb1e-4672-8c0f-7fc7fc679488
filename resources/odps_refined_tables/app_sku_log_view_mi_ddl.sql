```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_log_view_mi`(
    `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
    `sku_id` STRING COMMENT 'SKU ID，商品库存单位唯一标识',
    `spu_name` STRING COMMENT '商品名称，标准产品单元名称',
    `view_key` STRING COMMENT '浏览名称，用户浏览的具体商品或品类名称',
    `pv` BIGINT COMMENT '月PV值，页面浏览量，统计周期内大于等于100的访问次数'
)
COMMENT '人群浏览行为偏好分析表，记录用户对各类商品的浏览偏好数据'
PARTITIONED BY (
    `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '人群浏览行为偏好分析表，用于分析用户对不同商品的浏览偏好和热度趋势',
    'lifecycle' = '30'
);
```