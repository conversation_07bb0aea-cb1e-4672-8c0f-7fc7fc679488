CREATE TABLE IF NOT EXISTS app_largeg_area_sku_order_wi(
	year STRING COMMENT '年份，格式：yyyy',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式：yyyyMMdd（年月日）',
	sunday STRING COMMENT '周日日期，格式：yyyyMMdd（年月日）',
	sku_id STRING COMMENT 'SKU编号，商品唯一标识',
	spu_name STRING COMMENT '商品名称',
	sku_disc STRING COMMENT '商品描述，包含规格、包装等信息',
	large_area_id BIGINT COMMENT '运营大区ID，取值范围：1-93',
	large_area_name STRING COMMENT '运营大区名称，枚举值包括：杭州大区、上海大区、昆明快递大区、广州大区、成都大区、重庆大区、福州大区、长沙大区、南宁大区、昆明大区、苏州大区、贵阳大区、青岛大区、苏南大区、武汉大区、广东一点点快递区域等',
	cust_cnt BIGINT COMMENT '交易客户数，取值范围：1-185',
	large_area_cust_cnt BIGINT COMMENT '运营大区总客户数，取值范围：1-3830'
)
COMMENT '区域渗透数据表，记录各运营大区商品销售渗透情况，包含SKU级别的客户交易数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='区域商品订单渗透分析表，用于分析各运营大区商品销售覆盖情况')
LIFECYCLE 30;