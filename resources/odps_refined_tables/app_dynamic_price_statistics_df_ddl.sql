```sql
CREATE TABLE IF NOT EXISTS app_dynamic_price_statistics_df(
    sku STRING COMMENT '商品SKU编码，唯一标识一个商品',
    warehouse_no BIGINT COMMENT '库存仓编号，取值范围：2-178',
    click_uv BIGINT COMMENT '点击量(UV)，表示独立访客数量',
    click_conversion DECIMAL(38,18) COMMENT '点击转换率，表示点击转化为购买的比例，取值范围：-1.0到正数',
    sales_volume BIGINT COMMENT '销量(商品件数)，表示销售的商品数量',
    sell_out BIGINT COMMENT '是否有售罄过：0-否，1-是',
    sales_speed_hour STRING COMMENT '昨日小时销售速度，格式为key-value逗号分隔，如：0000:100,0100:88，表示各小时段的销售数量',
    sell_out_hour STRING COMMENT '昨日小时是否有售罄过，格式为key-value逗号分隔，如：0000:0,0100:1，表示各小时段的售罄状态(0-未售罄，1-售罄)',
    date_flag STRING COMMENT '日期标记，格式为yyyyMMdd，表示数据所属的业务日期'
)
COMMENT '动态定价-SKU指标统计表，包含商品销售、点击、售罄等关键指标的小时级数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期')
STORED AS ALIORC
TBLPROPERTIES (
    'columnar.nested.type'='true',
    'comment'='动态定价SKU指标统计表，用于商品销售行为分析和定价策略优化'
)
LIFECYCLE 30;
```