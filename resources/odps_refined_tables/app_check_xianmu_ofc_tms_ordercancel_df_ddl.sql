CREATE TABLE IF NOT EXISTS app_check_xianmu_ofc_tms_ordercancel_df(
	`order_no` STRING COMMENT '订单号，唯一标识一个订单，示例值：0125OWJCTJ0328142568',
	`sku` STRING COMMENT '商品SKU编码，唯一标识一个商品，示例值：81737321814'
)
COMMENT '业务数据校验——鲜沐_OFC_TMS逆向对比表，用于校验鲜沐业务中OFC和TMS系统在订单取消场景下的数据一致性'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，示例：20250328'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='业务数据校验表，用于对比OFC和TMS系统在订单取消场景的数据差异')
LIFECYCLE 30;