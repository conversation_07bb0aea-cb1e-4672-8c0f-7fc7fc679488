CREATE TABLE IF NOT EXISTS app_sku_cust_delivery_mi(
	`month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	`sku_id` STRING COMMENT 'SKU编号，商品最小库存单位标识',
	`category1` STRING COMMENT '一级类目，商品分类：乳制品、其他等',
	`spu_name` STRING COMMENT '商品名称，标准产品单元名称',
	`sku_disc` STRING COMMENT '商品描述，包含规格信息如10KG*1箱',
	`cust_cnt` BIGINT COMMENT '人群数量，偏好该商品的目标客户数量',
	`dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，商品实际履约产生的总金额',
	`other_spu_name` STRING COMMENT '连带商品名称，与该商品经常一起购买的其他商品名称',
	`other_cust_cnt` BIGINT COMMENT '连带客户数，同时购买该商品和连带商品的客户数量',
	`other_cdlv_origin_total_amt` DECIMAL(38,18) COMMENT '连带履约应付GMV，连带商品实际履约产生的总金额'
) 
COMMENT '人群偏好商品明细表，记录不同人群对商品的偏好程度和连带购买行为分析'
PARTITIONED BY (
	`ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = '人群偏好商品明细分析表，用于商品推荐和营销策略制定',
	'columnar.nested.type' = 'true'
)
LIFECYCLE 30;