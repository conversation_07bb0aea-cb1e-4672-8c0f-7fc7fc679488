CREATE TABLE IF NOT EXISTS app_crm_city_district_day_gmv_di(
    `city` STRING COMMENT '行政城市，枚举值：上海',
    `district` STRING COMMENT '区，枚举值：奉贤区、闵行区、浦东新区、崇明区、徐汇区、普陀区、嘉定区、静安区、长宁区、杨浦区、宝山区、虹口区、青浦区、黄浦区、松江区、金山区、城区、崇明县、闸北区、黄埔区',
    `day_tag` STRING COMMENT '数据所在日标记，格式：yyyyMMdd',
    `pull_new_amount` BIGINT COMMENT '拉新数',
    `ordinary_pull_new_amount` BIGINT COMMENT '普通拉新数，枚举值：0',
    `core_merchant_amount` BIGINT COMMENT '核心客户数，枚举值：0',
    `month_live_amount` BIGINT COMMENT '月活数',
    `open_merchant_month_live` BIGINT COMMENT '公海月活',
    `private_merchant_month_live` BIGINT COMMENT '私海月活',
    `open_merchant_effective_month_live` BIGINT COMMENT '公海有效月活',
    `private_merchant_effective_month_live` BIGINT COMMENT '私海有效月活',
    `open_merchant_amount` BIGINT COMMENT '公海客户数',
    `private_merchant_amount` BIGINT COMMENT '私海客户数',
    `operate_merchant_num` BIGINT COMMENT '倒闭客户数',
    `visit_num` BIGINT COMMENT '拜访数',
    `escort_num` BIGINT COMMENT '陪访数',
    `spu_average` DECIMAL(38,18) COMMENT 'spu均值',
    `total_gmv` DECIMAL(38,18) COMMENT '总gmv',
    `saas_total_gmv_amt` DECIMAL(38,18) COMMENT 'saas商家gmv',
    `saas_total_cust_cnt` BIGINT COMMENT 'saas月活',
    `brand_gmv` DECIMAL(38,18) COMMENT '自营品牌gmv',
    `brand_cust_cnt` BIGINT COMMENT '自营品牌下单客户数',
    `fruit_gmv` DECIMAL(38,18) COMMENT '鲜果gmv',
    `fruit_cust_cnt` BIGINT COMMENT '鲜果下单客户数',
    `dairy_gmv` DECIMAL(38,18) COMMENT '乳制品gmv',
    `dairy_cust_cnt` BIGINT COMMENT '乳制品下单客户数',
    `non_dairy_gmv` DECIMAL(38,18) COMMENT '非乳制品gmv',
    `non_dairy_cust_cnt` BIGINT COMMENT '非乳制品下单客户数',
    `agent_goods_gmv` DECIMAL(38,18) COMMENT '代售品gmv'
)
COMMENT '行政城市+区本月GMV表，按城市和区域统计的日度GMV及相关业务指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '行政城市+区本月GMV表，包含各区域拉新、月活、客户数、拜访数、各品类GMV等业务指标',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;