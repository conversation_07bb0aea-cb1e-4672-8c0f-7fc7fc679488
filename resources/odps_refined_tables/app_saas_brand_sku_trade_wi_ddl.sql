CREATE TABLE IF NOT EXISTS app_saas_brand_sku_trade_wi(
	year STRING COMMENT '年份，格式：YYYY',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
	sunday STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
	brand_alias STRING COMMENT '品牌名称',
	title STRING COMMENT '商品标题',
	specification STRING COMMENT '商品规格',
	category1 STRING COMMENT '后台一级类目，枚举值：新鲜水果、无、乳制品、其他、茶制品、成品原料、糖丨糖制品、新鲜蔬菜、饮品原料、包材、蛋丨蛋制品、饮料、坚果制品、帆台一级类目、水果制品、调味品、谷物制品、海鲜｜水产品｜制品、饼干丨糖果丨可可豆制品、糕点丨面包、食用油丨油脂及制品、蔬菜制品、肉丨肉制品、电器、测试一级类目T',
	sku_type STRING COMMENT '商品类型，枚举值：鲜沐自营、客户自营、代仓、代售、商城下单',
	total_gmv DECIMAL(38,18) COMMENT '总交易GMV',
	sku_cnt BIGINT COMMENT '商品销量，取值范围：1-300'
) 
COMMENT 'SaaS品牌品类结构数据表，包含品牌商品交易数据和品类结构信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：YYYYMMDD（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS品牌品类结构数据表，用于分析品牌商品销售情况和品类分布') 
LIFECYCLE 30;