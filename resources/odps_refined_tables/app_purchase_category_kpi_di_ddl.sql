CREATE TABLE IF NOT EXISTS app_purchase_category_kpi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`category` STRING COMMENT '商品品类：鲜果-新鲜水果类，乳制品-奶制品类，其他-其他商品类别',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，供应商应得的货款总额',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，实际支付给供应商的金额',
	`cost_amt` DECIMAL(38,18) COMMENT '商品成本，采购商品的成本金额',
	`store_cost_amt` DECIMAL(38,18) COMMENT '期末库存成本，期末库存商品的总成本',
	`sale_amt` DECIMAL(38,18) COMMENT '销售出库成本，销售出库商品的成本金额',
	`on_sale_sku_cnt` BIGINT COMMENT '有销售及自提出库的sku数，在售且有销售记录的SKU数量',
	`init_sku_cnt` BIGINT COMMENT '有期初库存的sku数，期初有库存的SKU数量',
	`sale_out_time` DECIMAL(38,18) COMMENT '售罄时长，商品从开始销售到售罄的时间长度（单位：小时）',
	`on_sale_time` DECIMAL(38,18) COMMENT '上架时长，商品从上架到当前的时间长度（单位：小时）',
	`check_sku_cnt` BIGINT COMMENT '抽检数量，被抽检的SKU数量',
	`qualified_cnt` BIGINT COMMENT '合格数量，抽检合格的SKU数量',
	`after_sale_amt` DECIMAL(38,18) COMMENT '采购责售后金额，采购责任导致的售后赔付金额'
) 
COMMENT '采购KPI指标表，包含各品类商品的采购、库存、销售、质检等关键绩效指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='采购业务KPI统计表，按日期和商品品类维度统计采购相关的各项绩效指标') 
LIFECYCLE 30;