CREATE TABLE IF NOT EXISTS app_cust_city_order_nextmonth_cohort_mi(
    `month` STRING COMMENT '月份，格式为yyyyMM，如202508表示2025年8月',
    `cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
    `cust_type` STRING COMMENT '客户行业类型，取值范围：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、面包蛋糕点心、其他',
    `register_province` STRING COMMENT '注册时省份名称',
    `register_city` STRING COMMENT '注册时城市名称',
    `first_order_cust_cnt` BIGINT COMMENT '当月首购客户数（首次下单的客户数量）',
    `first_order_cohort_cust_cnt` BIGINT COMMENT '当月首购客户中次月复购的客户数',
    `notfirst_order_cust_cnt` BIGINT COMMENT '当月非首购客户数（非首次下单的客户数量）',
    `notfirst_order_cohort_cust_cnt` BIGINT COMMENT '当月非首购客户中次月复购的客户数'
)
COMMENT '客户城市回购分析表，按月份、客户团队类型、客户行业类型、注册地域维度统计客户首购和非首购的次月复购情况，用于分析客户回购行为特征'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '客户城市回购分析表，用于客户行为分析和回购率监控',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;