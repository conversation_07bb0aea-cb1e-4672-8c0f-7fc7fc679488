```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_brand_trade_delivery_after_sale_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的业务日期',
  `order_source` STRING COMMENT '订单来源：ALL-全部，鲜沐-鲜沐平台，SaaS-SaaS平台，SAAS客户自营-SAAS客户自营',
  `category` STRING COMMENT '商品品类：ALL-全部，乳制品-乳制品类，鲜果-鲜果类，其他-其他品类，SAAS客户自营-SAAS客户自营',
  `sku_type` STRING COMMENT '商品类型：ALL-全部，自营-自营商品，全品类-全品类商品，代仓-代仓商品，SAAS客户自营-SAAS客户自营',
  `brand_id` BIGINT COMMENT '品牌ID，唯一标识一个品牌',
  `brand_alias` STRING COMMENT '品牌名称，品牌的别名或常用名称',
  `is_self_owned_brand` STRING COMMENT '是否自营品牌：否-非自营品牌，SAAS客户自营-SAAS客户自营品牌',
  `business_name` STRING COMMENT '企业工商名称，品牌所属企业的正式注册名称',
  `cust_group` STRING COMMENT '客户分组类型：大客户-大客户，批发客户-批发客户，普通-普通客户',
  `brand_grade` STRING COMMENT '品牌等级：普通-普通品牌，KA1-KA1级别，KA2-KA2级别，KA3-KA3级别，区域销售-区域销售品牌',
  `sale_name` STRING COMMENT '所属销售，负责该品牌的销售人员姓名',
  `operate_name` STRING COMMENT '所属运营，负责该品牌的运营人员姓名',
  `collaboration_mode` STRING COMMENT '合作模式：账期-账期合作，现结-现结合作，账期&现结-混合合作模式',
  `cooperation_stage` STRING COMMENT '合作阶段：非ka客户-非KA客户，试样-试样阶段，报价-报价阶段，试配-试配阶段，合作稳定期-稳定合作期，合作困难期-困难合作期，流失-已流失客户，暂不合作-暂不合作',
  `trd_orign_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV，交易场景下应付的总金额',
  `trd_real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV，交易场景下实际支付的总金额',
  `trd_cust_cnt` BIGINT COMMENT '交易客户数，参与交易的客户数量',
  `trd_sku_cnt` BIGINT COMMENT '交易sku数，参与交易的商品SKU数量',
  `trd_sale_cnt` BIGINT COMMENT '交易销量，交易的商品总数量',
  `trd_sale_weight` DECIMAL(38,18) COMMENT '交易重量，交易的商品总重量',
  `trd_order_cnt` BIGINT COMMENT '交易订单数，交易产生的订单数量',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后金额，未到货售后产生的金额',
  `after_sale_noreceived_cnt` DECIMAL(38,18) COMMENT '未到货售后数量，未到货售后产生的数量',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '到货售后金额，到货后售后产生的金额',
  `after_sale_received_cnt` DECIMAL(38,18) COMMENT '到货售后数量，到货后售后产生的数量',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，履约场景下应付的总金额',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，履约场景下实际支付的总金额',
  `dlv_cost_amt` DECIMAL(38,18) COMMENT '履约成本，履约过程中产生的成本金额',
  `dlv_order_cnt` BIGINT COMMENT '履约订单数，履约产生的订单数量',
  `dlv_cust_cnt` BIGINT COMMENT '履约客户数，参与履约的客户数量',
  `dlv_sku_cnt` BIGINT COMMENT '履约sku数，参与履约的商品SKU数量',
  `dlv_sale_cnt` BIGINT COMMENT '履约销量，履约的商品总数量',
  `dlv_sale_weight` DECIMAL(38,18) COMMENT '履约重量，履约的商品总重量',
  `dlv_short_orign_total_amt` DECIMAL(38,18) COMMENT '履约缺货应付GMV，履约缺货场景下应付的总金额',
  `dlv_short_real_total_amt` DECIMAL(38,18) COMMENT '履约缺货实付GMV，履约缺货场景下实际支付的总金额',
  `dlv_short_cnt` BIGINT COMMENT '履约缺货数，履约缺货的次数',
  `dlv_point_cnt` BIGINT COMMENT '履约点位数，履约涉及的点位数量',
  `cust_team` STRING COMMENT '客户类型：ALL-全部，集团大客户（茶百道）-茶百道集团大客户，集团大客户（喜茶）-喜茶集团大客户，Mars大客户-Mars大客户，平台客户-平台客户，批发-批发客户，SAAS客户自营-SAAS客户自营',
  `after_sale_order_cnt` BIGINT COMMENT '已到货售后订单数，已到货售后产生的订单数量'
) 
COMMENT '品牌交易数据表，包含品牌维度的交易、履约、售后等全方位业务数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据处理的日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='品牌交易数据表，用于分析品牌维度的交易、履约、售后等业务指标',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```