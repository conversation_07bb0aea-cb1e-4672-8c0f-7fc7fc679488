```sql
CREATE TABLE IF NOT EXISTS app_saas_pos_order_item_detail_di(
    channel_type BIGINT COMMENT '渠道类型：1=美团，3=其他（根据数据样本显示存在值为3的情况）',
    tenant_id BIGINT COMMENT '租户ID',
    pos_order_id BIGINT COMMENT 'POS订单ID',
    pos_order_item_id BIGINT COMMENT 'POS子订单ID',
    out_store_code STRING COMMENT '外部系统门店编码，如：210003',
    merchant_store_code STRING COMMENT '帆台门店编码，如：0210003',
    out_item_code STRING COMMENT '外部系统物料编码，如：44091、44090、20446等',
    market_item_id BIGINT COMMENT '帆台商品ID（数据中存在None值，表示未匹配到帆台商品）',
    available_date DATETIME COMMENT '生效日期，格式为年月日时分秒，如：2025-09-22 00:00:00'
)
COMMENT 'POS订单物料明细日表，记录POS系统中订单物料的详细信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，如：20250922')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='POS订单物料明细日表，包含渠道类型、租户信息、订单ID、门店编码、物料编码等关键业务字段',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```