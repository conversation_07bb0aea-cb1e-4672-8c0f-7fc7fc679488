```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_ue_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
  `sku_type` STRING COMMENT '商品类型：自营-平台自营商品，代仓-第三方仓库代发商品，代售-第三方销售商品',
  `category_1` STRING COMMENT '一级类目：其他-未分类商品，乳制品-奶制品相关商品，鲜果-新鲜水果类商品',
  `spu_name` STRING COMMENT '商品名称，标准产品单元名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格、包装等信息',
  `origin_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额，订单原始应付金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '配送实付总金额，实际支付金额',
  `cost_amt` DECIMAL(38,18) COMMENT '总成本，商品采购和生产成本',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费，物流配送费用',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额，商品损坏造成的损失金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额，库存盘点盈余金额',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额，库存盘点亏损金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本，商品存储相关费用',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，主要运输线路费用',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本，末端配送费用',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，仓库间调拨产生的费用',
  `self_picked_amt` DECIMAL(38,18) COMMENT '自提成本，用户自提产生的相关成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本，未分类的其他费用',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后金额，售后服务产生的费用'
)
COMMENT 'UE监控SKU商品维度表，用于监控商品级别的用户体验和成本效益指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SKU商品维度用户体验监控表',
  'lifecycle' = '30'
);
```