CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_task_cust_status_df` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
  `job_id` BIGINT COMMENT '任务ID，唯一标识一个任务',
  `job_name` STRING COMMENT '任务名称，描述任务的具体内容',
  `job_type` BIGINT COMMENT '任务类型：2-常规任务，3-特殊任务',
  `cust_id` BIGINT COMMENT '客户ID，唯一标识一个客户',
  `status` BIGINT COMMENT '完成状态：0-未完成，1-已完成',
  `complete_bd_id` BIGINT COMMENT '任务完成时对应BD的ID，-1表示系统自动完成',
  `complete_bd_name` STRING COMMENT '任务完成时对应BD的名称'
)
COMMENT '任务客户完成状态判定表，记录CRM系统中任务与客户的完成状态关系'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES ('comment' = '任务客户完成状态判定表，用于分析任务完成情况和BD绩效')
LIFECYCLE 30;