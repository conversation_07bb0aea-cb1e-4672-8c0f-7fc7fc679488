```sql
CREATE TABLE IF NOT EXISTS app_sku_area_cust_category_gross_margin_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
    `large_area_name` STRING COMMENT '服务大区名称，枚举值包括：None、广州大区、上海大区、重庆大区、成都大区、杭州大区、武汉大区、苏州大区、长沙大区、青岛大区、苏南大区、昆明大区、福州大区、南宁大区、贵阳大区等',
    `warehouse_name` STRING COMMENT '库存仓名称，枚举值包括：东莞总仓、嘉兴总仓、重庆总仓、上海总仓、华西总仓、武汉总仓、南京总仓、长沙总仓、青岛总仓、昆明总仓、福州总仓、济南总仓、南宁总仓、苏州总仓、贵阳总仓、东莞冷冻总仓、嘉兴海盐总仓等',
    `cust_class` STRING COMMENT '客户大类，枚举值包括：大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
    `sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
    `sku_type` STRING COMMENT 'SKU类型，枚举值包括：自营、代售',
    `spu_name` STRING COMMENT '商品名称',
    `category_1` STRING COMMENT '一级类目，枚举值包括：乳制品、其他、鲜果等',
    `category_2` STRING COMMENT '二级类目，枚举值包括：乳制品、包材、成品原料、糖丨糖制品、饮料、新鲜水果、新鲜蔬菜、其他、坚果制品、水果制品、糕点丨面包、肉丨肉制品、茶制品、蔬菜制品、蛋丨蛋制品、调味品、谷物制品、酒、食品添加剂、食用油丨油脂及制品、饮品原料、饼干丨糖果丨可可豆制品等',
    `category_3` STRING COMMENT '三级类目，枚举值包括：奶酪丨芝士、液体乳、炼乳、稀奶油、饮品杯丨瓶、配料（小料）类、砂糖、植物蛋白饮料、碳酸饮料、仁果类、其他新鲜水果、柑果类、核果类、浆果类、瓠果类、根茎菜类、绿叶菜类、奶粉、黄油、礼品、保鲜膜丨袋、其他耗材、厨具、裱花耗材、烘炒类、果冻类配料、烘焙类原料、粉圆类配料、冷冻水果、水果风味制品、罐头、冷冻面团、冷加工糕点、糖浆、糖粉、混合肉类、猪肉、茶叶、茶汤、茶粉、冷冻蔬菜、蛋类、半固体(酱)、五谷杂粮米粉、谷物罐头、面粉丨小麦粉、蒸馏酒、配制酒、凝固剂、膨松剂等',
    `category_4` STRING COMMENT '四级类目，枚举值包括：奶油奶酪、马斯卡彭、常温牛奶、罐装炼乳、搅打型稀奶油、塑料制杯丨瓶、配料（小料）、白砂糖、其他植物蛋白饮料、其他型碳酸饮料、梨、苹果、无花果、柚、柠檬、桔子、橙、金桔、椰子、牛油果、芒果、凤梨丨菠萝、奇异果丨猕猴桃、提子、树莓、水果番茄丨圣女果｜西红柿、火龙果、百香果、石榴、芭乐、草莓、菇娘果、葡萄、蓝莓、香蕉、蜜瓜、西瓜、黄瓜、红薯、甘蓝、薄荷、全脂乳粉、切达再制干酪、马苏里拉、发酵乳、鲜牛奶、乳酸黄油、无盐黄油、有盐黄油、淡味黄油等',
    `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，订单原始总金额',
    `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，客户实际支付金额',
    `cost_amt` DECIMAL(38,18) COMMENT '总成本，商品总成本',
    `gross_margin` DECIMAL(38,18) COMMENT '毛利润，实付总金额减去总成本',
    `sku_cnt` BIGINT COMMENT '配送数量，实际配送的商品数量',
    `origin_amt_befored` DECIMAL(38,18) COMMENT '昨日应付金额，前一天的应付总金额',
    `real_amt_befored` DECIMAL(38,18) COMMENT '昨日实付金额，前一天的实付总金额',
    `cost_amt_befored` DECIMAL(38,18) COMMENT '昨日总成本，前一天的商品总成本',
    `gross_margin_befored` DECIMAL(38,18) COMMENT '昨日毛利润，前一天的毛利润',
    `sku_cnt_befored` DECIMAL(38,18) COMMENT '昨日配送数量，前一天的配送数量',
    `origin_amt_beforew` DECIMAL(38,18) COMMENT '上周应付金额，上周同期的应付总金额',
    `real_amt_beforew` DECIMAL(38,18) COMMENT '上周实付金额，上周同期的实付总金额',
    `cost_amt_beforew` DECIMAL(38,18) COMMENT '上周总成本，上周同期的商品总成本',
    `gross_margin_beforew` DECIMAL(38,18) COMMENT '上周毛利润，上周同期的毛利润',
    `sku_cnt_beforew` DECIMAL(38,18) COMMENT '上周配送数量，上周同期的配送数量',
    `sku_disc` STRING COMMENT '商品描述，包含规格、包装等信息'
)
COMMENT '毛利数据SKU维度日表，按SKU、区域、客户类别等多维度统计的每日毛利数据，包含当日数据以及与昨日、上周同期的对比数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SKU维度毛利分析表，用于商品毛利分析和业务决策支持',
    'lifecycle' = '30'
);
```