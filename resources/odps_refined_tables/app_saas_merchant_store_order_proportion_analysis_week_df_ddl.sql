```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_merchant_store_order_proportion_analysis_week_df`(
  `tenant_id` BIGINT COMMENT '租户ID，取值范围：2-8',
  `time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd（年月日），表示统计周的开始日期',
  `item_id` BIGINT COMMENT '商品ID，取值范围：1-398',
  `store_id` BIGINT COMMENT '门店ID，取值范围：1-419',
  `order_amount` BIGINT COMMENT '订货数量，单位：件，取值范围：1-250',
  `order_amount_proportion` DECIMAL(38,18) COMMENT '订货数量占比，小数形式表示该商品在该门店订货数量占总订货数量的比例',
  `order_amount_proportion_upper_period` DECIMAL(38,18) COMMENT '订货数量占比环比，与上一统计周期相比的订货数量占比变化率',
  `order_price` DECIMAL(38,18) COMMENT '订货金额，单位：元，表示该商品在该门店的订货总金额',
  `order_price_proportion` DECIMAL(38,18) COMMENT '订货金额占比，小数形式表示该商品在该门店订货金额占总订货金额的比例',
  `order_price_proportion_upper_period` DECIMAL(38,18) COMMENT '订货金额占比环比，与上一统计周期相比的订货金额占比变化率',
  `total_order_amount` BIGINT COMMENT '总订货数量，单位：件，表示该门店在统计周期内的所有商品订货数量总和，取值范围：1-4281',
  `total_order_price` DECIMAL(38,18) COMMENT '总订货金额，单位：元，表示该门店在统计周期内的所有商品订货金额总和',
  `order_amount_upper_period` BIGINT COMMENT '上周期订货数量，单位：件，表示上一统计周期该商品在该门店的订货数量，取值范围：1-70',
  `order_price_upper_period` DECIMAL(38,18) COMMENT '上周期订货金额，单位：元，表示上一统计周期该商品在该门店的订货金额',
  `total_order_amount_upper_period` BIGINT COMMENT '上周期总订货数量，单位：件，表示上一统计周期该门店的所有商品订货数量总和，取值范围：6-4281',
  `total_order_price_upper_period` DECIMAL(38,18) COMMENT '上周期总订货金额，单位：元，表示上一统计周期该门店的所有商品订货金额总和'
) 
COMMENT 'SaaS门店订货占比分析周报表，用于分析各门店商品订货数量金额的占比情况及环比变化'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日），表示数据计算日期'
)
STORED AS ALIORC  
TBLPROPERTIES (
  'comment'='SaaS门店订货占比分析周度报表，包含门店商品订货的占比分析和环比变化数据',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```