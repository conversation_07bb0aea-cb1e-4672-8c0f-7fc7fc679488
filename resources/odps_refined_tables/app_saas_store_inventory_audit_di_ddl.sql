CREATE TABLE IF NOT EXISTS app_saas_store_inventory_audit_di(
	report_day DATETIME COMMENT '稽核自然日，格式为年月日时分秒',
	channel_type BIGINT COMMENT '渠道类型：1=美团，3=其他（根据数据样本推断）',
	tenant_id BIGINT COMMENT '租户id',
	out_store_code STRING COMMENT '外部系统门店编码',
	out_store_name STRING COMMENT '外部系统门店名称',
	merchant_store_id BIGINT COMMENT '帆台门店id',
	merchant_store_code STRING COMMENT '帆台门店编码',
	out_item_code STRING COMMENT '外部系统物料编码',
	out_item_name STRING COMMENT '外部系统物料名称',
	market_item_id BIGINT COMMENT '帆台商品id',
	specification STRING COMMENT '规格',
	store_ordering_inventory_unit STRING COMMENT '门店订货单位',
	store_inventory_unit STRING COMMENT '门店库存单位',
	use_count DECIMAL(38,18) COMMENT '销用总量',
	need_buy_count DECIMAL(38,18) COMMENT '应进货总量',
	real_buy_count DECIMAL(38,18) COMMENT '实际帆台进货总量',
	inventory_check_before_count DECIMAL(38,18) COMMENT '盘前数量',
	inventory_check_after_count DECIMAL(38,18) COMMENT '盘后数量'
) 
COMMENT '门店库存进销稽核日表，用于记录门店库存的进销存稽核数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式的年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='门店库存进销稽核日表，包含门店库存的进销存稽核相关数据') 
LIFECYCLE 30;