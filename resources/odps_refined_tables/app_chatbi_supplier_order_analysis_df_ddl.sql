CREATE TABLE IF NOT EXISTS app_chatbi_supplier_order_analysis_df(
	`order_no` STRING COMMENT '出库订单号，来源于WMS库存任务处理明细表，唯一标识出库订单',
	`tenant_id` BIGINT COMMENT '租户ID，用于多租户数据隔离，标识数据所属的租户。取值范围：tenant_id = 1说明是鲜沐或者顺鹿达的数据',
	`warehouse_no` BIGINT COMMENT '仓库编号，标识商品所在的具体仓库，用于仓库维度分析',
	`warehouse_name` STRING COMMENT '仓库名称，仓库的中文名称，便于业务人员理解和报表展示。枚举值包括：嘉兴总仓、上海总仓、青岛总仓、长沙总仓、东莞冷冻总仓、东莞总仓、武汉总仓、南京总仓、嘉兴海盐总仓、华西总仓、昆明总仓、福州总仓、贵阳总仓、南宁总仓、济南总仓、嘉兴水果批发总仓、重庆总仓、苏州总仓等',
	`sku_id` STRING COMMENT 'SKU商品编码，商品的唯一标识码，用于商品维度分析和库存管理',
	`batch` STRING COMMENT '批次号，商品的批次信息，用于追溯商品来源和质量管理',
	`order_time` DATETIME COMMENT '订单创建时间，记录订单的下单时间，格式为年月日时分秒（yyyy-MM-dd HH:mm:ss），用于时间维度分析',
	`sku_price` DECIMAL(38,18) COMMENT 'SKU单价，商品的单位价格，单位为元，保留18位小数',
	`sku_subtotal_price` DECIMAL(38,18) COMMENT 'SKU小计金额，等于数量乘以单价，单位为元，用于计算订单总价值',
	`quantity` BIGINT COMMENT 'SKU数量，该SKU在订单中的出库数量，支持大数值存储。取值范围：1-114',
	`sku_sub_type` BIGINT COMMENT 'SKU子类型。枚举值：1-代销不入仓(俗称"全品类")、2-代销入仓(俗称"全品类")、3-自营(经销)(我司自行采购、入库、销售)、5-顺鹿达商品(鲜果POP)',
	`pd_name` STRING COMMENT '商品名称，商品的中文名称描述，便于业务识别和报表展示',
	`weight` STRING COMMENT 'SKU规格，商品重量规格描述，如"125G*2盒/一级/小果果径约12-14mm"等',
	`supplier_id` STRING COMMENT '供应商ID，支持多供应商场景，多个供应商ID用逗号分隔，用于供应商维度分析',
	`supplier_name` STRING COMMENT '供应商名称，供应商的企业名称，便于供应商管理和分析',
	`supplier_manager` STRING COMMENT '供应商负责人，供应商的业务对接人员姓名，用于供应商关系管理',
	`after_sale_amount` DECIMAL(38,18) COMMENT '售后总金额，单位为元',
	`deliveryed_after_sale_amount` DECIMAL(38,18) COMMENT '已配送售后金额，小于等于售后总金额，单位为元',
	`after_sales_times` BIGINT COMMENT '此SKU售后次数统计',
	`first_after_sale_handle_date` STRING COMMENT '首次售后处理日期，格式为年月日（yyyyMMdd），如：20220811',
	`last_after_sale_handle_date` STRING COMMENT '最近一次售后处理日期，格式为年月日（yyyyMMdd），如：20220811',
	`sku_real_subtotal_price` DECIMAL(38,18) COMMENT 'SKU最终金额（减去了售后金额），计算公式：sku_subtotal_price - after_sale_amount。统计GMV时，如果没有特别说明，请优先使用该字段，单位为元'
)
COMMENT '订单SKU供应商分析结果表，整合订单、商品、仓库、供应商等维度信息，支持多维度业务分析。包含订单基本信息、商品信息、供应商信息、售后信息等完整业务数据'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为年月日（yyyyMMdd）'
)
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='订单SKU供应商分析结果表，整合订单、商品、仓库、供应商等维度信息，支持多维度业务分析')
LIFECYCLE 7;