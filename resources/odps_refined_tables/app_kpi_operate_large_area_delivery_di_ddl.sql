CREATE TABLE IF NOT EXISTS app_kpi_operate_large_area_delivery_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`large_area_name` STRING COMMENT '运营服务大区名称，取值范围：南宁大区、昆明大区、武汉大区、苏南大区、贵阳大区、长沙大区、青岛大区、上海大区、广州大区、成都大区、杭州大区、福州大区、苏州大区、重庆大区',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，原始应付总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，实际支付总金额',
	`marketing_amt` DECIMAL(38,18) COMMENT '营销费用，营销相关的费用支出',
	`cost_amt` DECIMAL(38,18) COMMENT '成本金额，业务运营成本',
	`origin_gross` DECIMAL(38,18) COMMENT '应付毛利润，原始应付金额计算的毛利润',
	`real_gross` DECIMAL(38,18) COMMENT '实付毛利润，实际支付金额计算的毛利润',
	`origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率，原始应付金额计算的毛利率',
	`real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率，实际支付金额计算的毛利率',
	`cust_cnt` BIGINT COMMENT '履约客户数，完成履约的客户数量',
	`point_cnt` BIGINT COMMENT '点位数，服务点位数量',
	`origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价，原始应付金额计算的客单价',
	`real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价，实际支付金额计算的客单价',
	`timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV，省心送服务的原始应付金额',
	`timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV，省心送服务的实际支付金额',
	`consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV，代售服务的原始应付金额',
	`consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV，代售服务的实际支付金额',
	`consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用，代售服务的营销费用',
	`consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润，代售服务的原始应付金额计算的毛利润',
	`consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润，代售服务的实际支付金额计算的毛利润',
	`consign_cust_cnt` BIGINT COMMENT '代售履约客户数，代售服务完成履约的客户数量',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储费，仓储相关的费用',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费，单点干线运输费用',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费，采购自提相关的费用',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨费，货物调拨相关的费用',
	`other_amt` DECIMAL(38,18) COMMENT '其他费，其他未分类的费用',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送费，配送服务相关的费用'
) 
COMMENT '运营履约KPI表（平台客户），包含各大区的运营履约关键绩效指标数据，如GMV、毛利率、客户数、各项费用等'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='运营履约KPI数据表，用于分析各大区的运营绩效和成本结构') 
LIFECYCLE 30;