CREATE TABLE IF NOT EXISTS app_crm_merchant_increment_label_di(
    `cust_id` BIGINT COMMENT '商户ID，唯一标识一个商户',
    `merchant_label` STRING COMMENT '客户标签，取值范围：月活老客户、活跃客户、月活新客户、羊场客户',
    `unionid` STRING COMMENT '微信unionid，用户的微信唯一标识',
    `type` BIGINT COMMENT '变动类型：0-新增标签；1-删除标签；2-其他类型（根据数据可能存在其他值）',
    `day_tag` STRING COMMENT '数据日期，格式为yyyyMMdd，表示年月日',
    `group_name` STRING COMMENT '分组名称，当前取值为"企微标签组"'
)
COMMENT '门店增量标签更新表，记录商户标签的变动情况，包括新增和删除标签的操作'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='门店增量标签更新表，用于跟踪商户标签的变更历史')
LIFECYCLE 30;