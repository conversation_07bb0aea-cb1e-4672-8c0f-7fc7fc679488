CREATE TABLE IF NOT EXISTS app_crm_merchant_sku_label_df(
	cust_id BIGINT COMMENT '商户ID，唯一标识一个商户',
	merchant_label STRING COMMENT '商品标签名称，如：ZILIULIU竹蔗冰糖糖浆、三麟苏打汽水、海南水仙芒等',
	group_name STRING COMMENT '标签组名称，枚举值：自营品牌热门商品、全平台热门商品(其他)、全平台热门商品(鲜果)、全平台热门商品(乳制品)、全品类热门商品'
) 
COMMENT 'CRM用户商品标签表，记录商户与商品标签的关联关系'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='CRM用户商品标签表，用于存储商户的商品偏好标签数据') 
LIFECYCLE 30;