CREATE TABLE IF NOT EXISTS app_cust_brand_category_performance_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的业务日期',
	`category` STRING COMMENT '商品品类：鲜果-新鲜水果类商品，乳制品-奶制品类商品，其他-其他类别商品',
	`order_origin_amt` DECIMAL(38,18) COMMENT '订单原始应付总金额，即交易应付总金额',
	`order_real_amt` DECIMAL(38,18) COMMENT '订单实际支付总金额，即交易实付总金额',
	`delivery_origin_amt` DECIMAL(38,18) COMMENT '履约原始应付总金额',
	`delivery_real_amt` DECIMAL(38,18) COMMENT '履约实际支付总金额',
	`delivery_cash_real_amt` DECIMAL(38,18) COMMENT '履约现金支付实付总金额',
	`delivery_bill_real_amt` DECIMAL(38,18) COMMENT '履约账期支付实付总金额',
	`delivery_cost_amt` DECIMAL(38,18) COMMENT '履约商品成本金额',
	`delivery_real_gross` DECIMAL(38,18) COMMENT '履约实付毛利润，计算公式：履约实付总金额-履约商品成本金额',
	`delivery_real_gross_rate` DECIMAL(38,18) COMMENT '履约实付毛利率，计算公式：履约实付毛利润/履约实付总金额',
	`delivery_cust_cnt` BIGINT COMMENT '履约客户数量，统计期内完成履约的客户数',
	`delivery_point_cnt` BIGINT COMMENT '履约累计点位数量，统计期内完成履约的点位总数',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，即已完成到货的售后金额',
	`after_sale_rate` DECIMAL(38,18) COMMENT '售后比例，计算公式：已到货售后金额/履约实付总金额'
) 
COMMENT '大客户品类粒度监控表，按商品品类维度统计大客户的交易、履约、售后等业务指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的技术日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='大客户品类粒度业务监控表，用于监控各品类大客户的经营表现') 
LIFECYCLE 30;