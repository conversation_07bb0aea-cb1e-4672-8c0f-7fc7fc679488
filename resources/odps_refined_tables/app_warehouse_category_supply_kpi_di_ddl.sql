```sql
CREATE TABLE IF NOT EXISTS app_warehouse_category_supply_kpi_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `warehouse_no` BIGINT COMMENT '仓库编号，取值范围：1-155，唯一标识每个仓库',
    `warehouse_name` STRING COMMENT '仓库名称，包括：山东总仓、昆明总仓、南京总仓、上海总仓、嘉兴总仓、福州总仓、长沙总仓、苏州总仓、青岛总仓、美团优选杭州一仓、美团优选上海仓、东莞冷冻总仓、贵阳总仓、自营奶虚拟仓、济南总仓、武汉总仓、华西总仓、东莞总仓、美团优选杭州二仓、美团虚拟代下单总仓、美团代加工虚拟仓、嘉兴海盐总仓、嘉兴水果批发总仓、重庆总仓、南宁总仓、杭州总仓、山西总仓、叮咚买菜昆山仓等',
    `category` STRING COMMENT '商品类别，枚举值：标品-标准化商品，鲜果-新鲜水果',
    `sale_out_time` DECIMAL(38,18) COMMENT '售罄时长，单位：小时，表示商品从入库到售罄的时间',
    `on_sale_time` DECIMAL(38,18) COMMENT '上架时长，单位：小时，表示商品从上架到售出的时间',
    `store_cost_amt` DECIMAL(38,18) COMMENT '期末库存成本，单位：元，表示统计期末的库存商品成本金额',
    `sale_amt` DECIMAL(38,18) COMMENT '销售出库成本，单位：元，表示统计期内销售出库的商品成本金额',
    `temporary_store_amt` DECIMAL(38,18) COMMENT '临保成本，单位：元，表示临近保质期商品的库存成本金额',
    `damage_amt` DECIMAL(38,18) COMMENT '滞销过期货损出库成本，单位：元，表示因滞销或过期造成的商品损失成本',
    `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，单位：元，表示原始总交易金额（Gross Merchandise Volume）'
) 
COMMENT '供应链KPI指标表，包含各仓库按商品类别统计的供应链关键绩效指标数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的业务日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '供应链KPI分析表，用于监控和分析各仓库的库存周转、销售效率、成本控制等关键指标',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```