```sql
CREATE TABLE IF NOT EXISTS app_stock_dashboard_future_df(
    view_date DATETIME COMMENT '视图日期，格式为年月日时分秒，表示数据查看的具体时间点',
    pd_id BIGINT COMMENT '产品ID，唯一标识一个产品',
    sku_id STRING COMMENT '库存单位ID，唯一标识一个具体的SKU，取值范围：包含约8100个唯一值，如1017884776162、10218等',
    warehouse_no BIGINT COMMENT '仓库编号，标识具体的仓库位置，取值范围：2（根据数据样本显示只有仓库2）',
    on_way_quantity BIGINT COMMENT '采购在途库存数量，表示已采购但尚未到达仓库的商品数量',
    transfer_in_quantity BIGINT COMMENT '调拨在途库存数量，表示从其他仓库调拨但尚未到达的商品数量',
    on_way_order_quantity BIGINT COMMENT '采购订单在途数量，表示已下单但尚未发货的采购订单数量',
    po_on_way_quantity BIGINT COMMENT '采购订单口径在途数量，按采购订单统计的在途数量',
    transfer_order_in_quantity BIGINT COMMENT '调拨单口径在途数量，按调拨单统计的调入在途数量',
    transfer_order_plan_in_quantity BIGINT COMMENT '调拨计划入在途数量，按调拨计划统计的预计调入数量'
)
COMMENT '罗盘未来数据表，存储库存相关的未来预测数据，包括各种在途库存和订单数量信息'
PARTITIONED BY (ds BIGINT COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='库存仪表板未来数据表，用于存储库存预测和计划数据')
LIFECYCLE 30;
```