```sql
CREATE TABLE IF NOT EXISTS app_saas_proxy_warehousing_order_batch_cost_data_df(
    tenant_id BIGINT COMMENT '租户ID，取值范围：1-95',
    create_time STRING COMMENT '出库任务创建时间，格式：yyyy-MM-dd HH:mm:ss（年月日时分秒）',
    out_order_no STRING COMMENT '订单编号，唯一标识订单的编号',
    warehouse_no STRING COMMENT '出库仓库编号，枚举值：10,24,38,48,59,63,69,96,117,121,125,142,145,155,162,175,176',
    sku STRING COMMENT 'SKU，商品唯一标识编码',
    batch STRING COMMENT '出库批次，批次编号',
    quantity_sum BIGINT COMMENT '总出库数量，取值范围：1-200，平均1.634，标准差2.703',
    unit_cost DECIMAL(38,18) COMMENT 'SKU该批次成本单价，单位：元',
    total_cost DECIMAL(38,18) COMMENT '总成本金额，单位：元'
)
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd（年月日）')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='SaaS代理仓储订单批次成本数据表，记录出库订单的成本明细信息')
LIFECYCLE 365;
```