```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_brand_cust_trade_delivery_after_sale_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd',
  `order_source` STRING COMMENT '订单来源，取值范围：ALL-全部,鲜沐-鲜沐平台,SaaS-SaaS平台,SAAS客户自营-SAAS客户自营',
  `category` STRING COMMENT '商品品类，取值范围：ALL-全部,乳制品-乳制品,鲜果-鲜果,其他-其他品类,SAAS客户自营-SAAS客户自营',
  `sku_type` STRING COMMENT '商品类型，取值范围：ALL-全部,自营-自营商品,全品类-全品类商品,代仓-代仓商品,SAAS客户自营-SAAS客户自营',
  `brand_id` BIGINT COMMENT '品牌ID，-1表示未知或无品牌',
  `brand_alias` STRING COMMENT '品牌名称',
  `is_self_owned_brand` STRING COMMENT '是否自营品牌，取值范围：否-非自营品牌,SAAS客户自营-SAAS客户自营',
  `business_name` STRING COMMENT '企业工商名称',
  `cust_group` STRING COMMENT '客户分组类型，取值范围：None-未分组,大客户-大客户,普通-普通客户,批发客户-批发客户',
  `brand_grade` STRING COMMENT '品牌等级，取值范围：None-未分级,普通-普通品牌,KA1-KA1级别,KA2-KA2级别,KA3-KA3级别,区域销售-区域销售品牌',
  `sale_name` STRING COMMENT '所属销售姓名',
  `operate_name` STRING COMMENT '所属运营姓名',
  `collaboration_mode` STRING COMMENT '合作模式，取值范围：账期-账期合作,现结-现结合作,账期&现结-混合合作模式',
  `cooperation_stage` STRING COMMENT '合作阶段，取值范围：None-未合作,非ka客户-非KA客户,试样-试样阶段,报价-报价阶段,试配-试配阶段,合作稳定期-合作稳定期,合作困难期-合作困难期,流失-流失客户,暂不合作-暂不合作',
  `cust_id` BIGINT COMMENT '客户ID',
  `cust_name` STRING COMMENT '客户名称',
  `register_province` STRING COMMENT '注册省份',
  `register_city` STRING COMMENT '注册城市',
  `register_area` STRING COMMENT '注册区域',
  `administrative_city` STRING COMMENT '客户所属行政省市区',
  `bd_id` BIGINT COMMENT 'BD ID',
  `bd_name` STRING COMMENT 'BD姓名',
  `m1_name` STRING COMMENT 'M1管理者姓名',
  `m2_name` STRING COMMENT 'M2管理者姓名',
  `m3_name` STRING COMMENT 'M3管理者姓名',
  `zone_name` STRING COMMENT '销售区域',
  `trd_orign_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV金额',
  `trd_real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV金额',
  `trd_cust_cnt` BIGINT COMMENT '交易客户数量',
  `trd_sku_cnt` BIGINT COMMENT '交易SKU数量',
  `trd_sale_cnt` BIGINT COMMENT '交易销量',
  `trd_sale_weight` DECIMAL(38,18) COMMENT '交易重量',
  `trd_order_cnt` BIGINT COMMENT '交易订单数量',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后金额',
  `after_sale_noreceived_cnt` DECIMAL(38,18) COMMENT '未到货售后数量',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '到货售后金额',
  `after_sale_received_cnt` DECIMAL(38,18) COMMENT '到货售后数量',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV金额',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV金额',
  `dlv_cost_amt` DECIMAL(38,18) COMMENT '履约成本金额',
  `dlv_order_cnt` BIGINT COMMENT '履约订单数量',
  `dlv_cust_cnt` BIGINT COMMENT '履约客户数量',
  `dlv_sku_cnt` BIGINT COMMENT '履约SKU数量',
  `dlv_sale_cnt` BIGINT COMMENT '履约销量',
  `dlv_sale_weight` DECIMAL(38,18) COMMENT '履约重量',
  `dlv_short_orign_total_amt` DECIMAL(38,18) COMMENT '履约缺货应付GMV金额',
  `dlv_short_real_total_amt` DECIMAL(38,18) COMMENT '履约缺货实付GMV金额',
  `dlv_short_cnt` BIGINT COMMENT '履约缺货数量',
  `dlv_point_cnt` BIGINT COMMENT '履约点位数'
) 
COMMENT '销售交易履约数据表，包含品牌客户交易、履约、售后等全链路业务数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '业务日期分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '品牌客户交易履约售后数据明细表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```