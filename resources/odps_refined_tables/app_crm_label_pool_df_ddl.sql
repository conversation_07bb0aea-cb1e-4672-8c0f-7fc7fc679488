CREATE TABLE IF NOT EXISTS app_crm_label_pool_df(
	merchant_label STRING COMMENT '商户标签名称，枚举值包括：月活老客户、月活新客户、活跃客户、羊场客户、新客N0、首单N1、复购N2、成长A1、成长A2、成长A3、优质S1、优质S2、下降B1、下降B2、复活W、睡眠L1、流失L3、7天下过单、15天下过单、30天下过单、60天下过单、60天下单1-2次、60天下单3-4次、60天下单5-7次、60天下单8次+、60天日均客单0-200、60天日均客单200-400、60天日均客单400-600、60天日均客单600+、近30天无登录、近30天有登录、2-6天未下单、7-14天未下单、15-21天未下单、22-30天未下单、31-45天未下单、45天以上未下单、本月有拜访、本月无拜访、广东粗皮香水柠檬、蒙特瑞草莓、越南大青芒、海南水仙芒、晴王青提(阳光玫瑰)、有籽青柠檬、即食秘鲁牛油果、红颜草莓、蓝莓盒装、丹东红颜草莓、美都无籽西瓜等',
	group_name STRING COMMENT '标签所属分组，枚举值包括：企微标签组、全平台热门商品(鲜果)、自营品牌热门商品、全平台热门商品(乳制品)、全品类热门商品、全平台热门商品(其他)',
	rank_id BIGINT COMMENT '标签在组内的排序编号，取值范围：1-39'
) 
COMMENT 'CRM用户标签池表，存储商户各类标签信息及其分组排序'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='CRM用户标签池，包含商户标签、标签分组和排序信息') 
LIFECYCLE 30;