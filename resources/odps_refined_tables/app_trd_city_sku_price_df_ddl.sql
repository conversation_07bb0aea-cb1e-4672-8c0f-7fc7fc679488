CREATE TABLE IF NOT EXISTS app_trd_city_sku_price_df(
	order_date STRING COMMENT '订单日期，格式为yyyyMMdd，表示年月日',
	area_no STRING COMMENT '运营服务区编号，取值范围：9585、44125',
	area_name STRING COMMENT '运营服务区名称，取值范围：苏州、武汉普冷',
	sku_id STRING COMMENT '商品SKU编号，唯一标识商品的最小库存单位',
	spu_name STRING COMMENT '商品SPU名称，表示标准产品单元名称',
	sku_disc STRING COMMENT '商品规格描述，如：1L*6盒、1L*12瓶、30KG*1包等',
	real_price DECIMAL(38,18) COMMENT '平均实付价格，单位为元，保留18位小数精度',
	sku_cnt BIGINT COMMENT '商品销量，统计周期内的销售数量',
	cost_price DECIMAL(38,18) COMMENT '平均成本价格，单位为元，保留18位小数精度',
	gross_profit DECIMAL(38,18) COMMENT '总毛利润，单位为元，保留18位小数精度',
	prediction_price DECIMAL(38,18) COMMENT '预测实付价格，定价模型预测的商品实付价格',
	prediction_price_max DECIMAL(38,18) COMMENT '预测实付价格上限，定价模型预测的价格区间上限',
	prediction_price_min DECIMAL(38,18) COMMENT '预测实付价格下限，定价模型预测的价格区间下限',
	prediction_sku_cnt DECIMAL(38,18) COMMENT '预测商品销量，定价模型预测的商品销售数量',
	prediction_sku_cnt_max DECIMAL(38,18) COMMENT '预测商品销量上限，定价模型预测的销量区间上限',
	prediction_sku_cnt_min DECIMAL(38,18) COMMENT '预测商品销量下限，定价模型预测的销量区间下限',
	prediction_profit DECIMAL(38,18) COMMENT '预测商品毛利润，定价模型预测的商品毛利润',
	prediction_profit_max DECIMAL(38,18) COMMENT '预测商品毛利润上限，定价模型预测的利润区间上限',
	prediction_profit_min DECIMAL(38,18) COMMENT '预测商品毛利润下限，定价模型预测的利润区间下限'
)
COMMENT '定价模型效果监控表，用于监控商品定价模型的预测效果，包含实际销售数据和模型预测数据的对比分析'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的业务日期（年月日）')
STORED AS ALIORC
TBLPROPERTIES ('comment'='定价模型效果监控表，监控商品在不同运营服务区的定价预测准确性')
LIFECYCLE 30;