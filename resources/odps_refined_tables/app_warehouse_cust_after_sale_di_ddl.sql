CREATE TABLE IF NOT EXISTS app_warehouse_cust_after_sale_di(
    `date` STRING COMMENT '日期，格式：yyyyMMdd',
    `register_province` STRING COMMENT '客户注册省份，枚举值：上海、四川、安徽、山东、广东、广西壮族自治区、江苏、江西、浙江、湖北、湖南、福建、贵州、重庆',
    `register_city` STRING COMMENT '客户注册城市，枚举值：上海市、成都市、绵阳市、合肥市、芜湖市、蚌埠市、威海市、济南市、烟台市、青岛市、东莞市、中山市、佛山市、广州市、惠州市、汕头市、江门市、深圳市、肇庆市、南宁市、南京市、常州市、无锡市、泰州市、盐城市、苏州市、南昌市、宜春市、新余市、萍乡市、台州市、嘉兴市、宁波市、杭州市、温州市、湖州市、绍兴市、金华市、武汉市、襄阳市、常德市、株洲市、永州市、湘潭市、长沙市、泉州市、福州市、莆田市、贵阳市、重庆市',
    `register_area` STRING COMMENT '客户注册区域',
    `cust_class` STRING COMMENT '客户类型，枚举值：普通（非品牌）、大客户（非茶百道）、批发',
    `brand_alias` STRING COMMENT '品牌别名，枚举值：无、楼下酸奶、周四晚、果呀呀、诺尔、茶大椰、麦合、桃花屋小酒馆、代数学家、BONJOUR本就茶饮、洪都大拇指奶茶连锁、杭州霸王茶姬、淘宝代下单账号、资溪面包、GOODSLOVE CAFE、湖南霸王茶姬、霸王茶姬香水柠檬、陈文鼎',
    `order_type` STRING COMMENT '订单类型，枚举值：其他、省心送',
    `warehouse_no` BIGINT COMMENT '库存仓编号，数值范围：10-155',
    `warehouse_name` STRING COMMENT '库存仓名称，枚举值：嘉兴总仓、华西总仓、南京总仓、青岛总仓、济南总仓、东莞总仓、东莞冷冻总仓、南宁总仓、嘉兴海盐总仓、长沙总仓、武汉总仓、福州总仓、贵阳总仓、重庆总仓',
    `sku_id` STRING COMMENT 'SKU编号',
    `sku_disc` STRING COMMENT '商品描述',
    `spu_name` STRING COMMENT '商品名称',
    `category_1` STRING COMMENT '一级类目，枚举值：其他、鲜果、乳制品',
    `category_4` STRING COMMENT '四级类目，枚举值：果汁、高筋面粉、芒果、橘子、桃子、常温牛奶、柿子、西瓜、柠檬、葡萄、柚、搅打型稀奶油、冻鸡蛋黄、蓝莓、牛油果、甘蓝、罐装炼乳、冷冻果肉、椰子、鲜牛奶、植脂奶油、提子、新鲜蛋类、无盐黄油、热加工熟混合肉制品、草莓、水果类馅料、奇异果丨猕猴桃、熟粉类糕点、木瓜、芋圆、金桔、无花果、橙、熟制冷冻面团、蜜瓜、水果番茄丨圣女果｜西红柿、巧克力、凤梨丨菠萝、冷冻熟蔬菜制品、低筋面粉、石榴',
    `after_sale_sku_cnt` BIGINT COMMENT '已到货售后数量，数值范围：0-26061',
    `after_sale_cnt` BIGINT COMMENT '已到货售后次数，数值范围：1-5',
    `after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额',
    `after_sale_short_amt` DECIMAL(38,18) COMMENT '售后缺货金额',
    `after_sale_reissue_amt` DECIMAL(38,18) COMMENT '售后补发金额',
    `deliver_total_amt` DECIMAL(38,18) COMMENT '配送实付总金额',
    `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额'
)
COMMENT '库存仓+客户+SKU售后数据汇总表，包含各仓库、客户类型、品牌、商品类别的售后数量、次数、金额等统计信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='库存仓客户SKU售后数据汇总表，用于分析各维度售后情况')
LIFECYCLE 30;