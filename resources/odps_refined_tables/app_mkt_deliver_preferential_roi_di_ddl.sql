CREATE TABLE IF NOT EXISTS app_mkt_deliver_preferential_roi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
	`city_id` BIGINT COMMENT '运营服务区ID，数值型标识，范围：1001-44269',
	`city_name` STRING COMMENT '运营服务区名称，枚举类型，包含：昆明、佛山、茶南昌外区、上海、茶长沙外区、深圳、烟台等约80个城市/区域名称',
	`large_area_id` BIGINT COMMENT '运营服务大区ID，数值型标识，范围：1-91',
	`large_area_name` STRING COMMENT '运营服务大区名称，枚举类型，包含：昆明大区、广州大区、长沙大区、上海大区、青岛大区、杭州大区、苏州大区、贵阳大区、福州大区、苏南大区、武汉大区、南宁大区、成都大区、重庆大区',
	`cust_team` STRING COMMENT '客户团队类型，枚举类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
	`life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举类型，取值范围：W、S2、A1、S1、B1、A2、A3、N2、B2、N1、L1、L2',
	`preferential_type` STRING COMMENT '营销活动类型，枚举类型，取值范围：特价活动、销售品类券、临保活动、售后补偿券、平台活动券、奶油卡、销售现货券、行业活动券、销售囤货券、区域拉新券、销售客情券、其他、销售月活券、红包、赠品、满减、换购',
	`preferential_amt` DECIMAL(38,18) COMMENT '营销金额，精确到小数点后18位',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付金额，精确到小数点后18位',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付金额，精确到小数点后18位',
	`cost_amt` DECIMAL(38,18) COMMENT '履约成本金额，精确到小数点后18位'
) 
COMMENT '履约维度营销活动ROI拆解报表，用于分析营销活动在履约环节的投资回报率'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='履约维度营销活动ROI拆解报表，包含城市、大区、客户团队、生命周期、活动类型等多维度的营销投入和产出分析') 
LIFECYCLE 30;