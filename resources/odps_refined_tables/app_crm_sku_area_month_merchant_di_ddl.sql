```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_sku_area_month_merchant_di`(
    `month_tag` STRING COMMENT '月份标记，格式为yyyyMM，表示年月，如202509表示2025年9月',
    `sku_id` STRING COMMENT '商品SKU编码，唯一标识商品规格',
    `area_no` BIGINT COMMENT '运营区域编号，数值型标识，取值范围从1001到44269',
    `merchant_id_text` STRING COMMENT '商户ID列表，使用英文逗号分隔的商户ID字符串，如"132673,139223,396530"'
)
COMMENT '每个SKU每月下单商户ID关联表，记录各商品在各运营区域每月的商户下单情况'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日，如20250922表示2025年9月22日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SKU-区域-月份-商户关联表，用于分析商品销售与商户的关联关系',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```