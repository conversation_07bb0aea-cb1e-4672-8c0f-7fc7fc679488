CREATE TABLE IF NOT EXISTS app_saas_merchant_store_order_proportion_analysis_df(
	tenant_id BIGINT COMMENT '租户ID',
	type BIGINT COMMENT '统计周期类型：1-周，2-月，3-季度',
	time_tag STRING COMMENT '时间标签，格式为yyyyMMdd，表示年月日',
	item_id BIGINT COMMENT '商品ID',
	title STRING COMMENT '商品名称',
	store_id BIGINT COMMENT '门店ID',
	store_type BIGINT COMMENT '门店类型：0-直营店，1-加盟店，2-托管店',
	store_group_name STRING COMMENT '门店分组名称',
	order_amount BIGINT COMMENT '订货数量',
	order_amount_proportion DECIMAL(38,18) COMMENT '订货数量占比，单位为百分数',
	order_amount_proportion_upper_period DECIMAL(38,18) COMMENT '订货数量占比环比，单位为百分数',
	order_price DECIMAL(38,18) COMMENT '订货金额',
	order_price_proportion DECIMAL(38,18) COMMENT '订货金额占比，单位为百分数',
	order_price_proportion_upper_period DECIMAL(38,18) COMMENT '订货金额占比环比，单位为百分数',
	total_order_amount BIGINT COMMENT '总订货数量',
	total_order_price DECIMAL(38,18) COMMENT '总订货金额',
	order_amount_upper_period BIGINT COMMENT '上周期订货数量',
	order_price_upper_period DECIMAL(38,18) COMMENT '上周期订货金额',
	total_order_amount_upper_period BIGINT COMMENT '上周期总订货数量',
	total_order_price_upper_period DECIMAL(38,18) COMMENT '上周期总订货金额'
) 
COMMENT 'SaaS门店订货占比分析表，用于分析各门店在不同统计周期内的订货数量、金额及其占比情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS门店订货占比分析结果表，包含门店订货的详细统计数据和环比分析') 
LIFECYCLE 30;