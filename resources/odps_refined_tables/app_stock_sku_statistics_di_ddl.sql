```sql
CREATE TABLE IF NOT EXISTS app_stock_sku_statistics_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `sku` STRING COMMENT '商品SKU编码，唯一标识商品，取值范围包括632460886535、5447758500、16738467474等数千个唯一值',
    `warehouse_no` BIGINT COMMENT '仓库编号，取值范围为2-175的整数，表示商品所在的仓库',
    `quantity` BIGINT COMMENT '销售量，表示商品的销售数量，取值范围为0-335的整数',
    `duration_rate_14d` DECIMAL(38,18) COMMENT '14天平均售罄率，取值范围为0-1的小数，表示商品在14天内的平均售罄比例',
    `turnover_rate_7d` DECIMAL(38,18) COMMENT '7天周转率，取值范围为0-1的小数或None，表示商品在7天内的周转效率，None表示数据缺失'
)
PARTITIONED BY (
    `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '商品库存SKU统计日增量表，包含商品的销售数据、售罄率和周转率等关键指标'
)
LIFECYCLE 30;
```