CREATE TABLE IF NOT EXISTS app_crm_merchant_mall_search_top_di(
    day_tag STRING COMMENT '日期标记，格式为yyyyMMdd，表示数据所属的日期',
    merchant_id BIGINT COMMENT '商户唯一标识ID',
    product_name STRING COMMENT '商品名称，包含各种食品和烘焙原料等',
    search_num BIGINT COMMENT '搜索次数，表示该商品在当天的搜索频次，最小值为1，最大值为23，平均值为1.56'
)
COMMENT '商户商城搜索记录表，存储近三天内搜索量排名前10的商品搜索数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据采集日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='商户商城搜索行为分析表，用于分析热门商品搜索趋势和商户搜索偏好') 
LIFECYCLE 30;