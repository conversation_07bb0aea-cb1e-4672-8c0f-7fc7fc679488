CREATE TABLE IF NOT EXISTS app_cust_lifecycle_order_mi(
  `month` STRING COMMENT '月份，格式：yyyyMM',
  `cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `cust_type` STRING COMMENT '客户行业类型，取值范围：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他,加盟店,水果/果切/榨汁店,水果店,水果捞/果切店,社区生鲜店,糖水/水果捞,菜市场水果摊,西式/法式甜品店',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细），取值范围：A1,A2,A3,B1,B2,L1,L2,L3,N0,S1,S2,W,N1,N2',
  `register_province` STRING COMMENT '注册时省份',
  `register_city` STRING COMMENT '注册时城市',
  `city_id` BIGINT COMMENT '运营服务区ID',
  `city_name` STRING COMMENT '运营服务区名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `cust_cnt` BIGINT COMMENT '客户数',
  `other_real_amt` DECIMAL(38,18) COMMENT '订单实付金额',
  `fruit_other_real_amt` DECIMAL(38,18) COMMENT '鲜果订单实付金额',
  `dairy_other_real_amt` DECIMAL(38,18) COMMENT '乳制品订单实付金额',
  `other_other_real_amt` DECIMAL(38,18) COMMENT '其他订单实付金额',
  `order_days_avg` DECIMAL(38,18) COMMENT '平均下单频次（天）',
  `fruit_order_days_avg` DECIMAL(38,18) COMMENT '鲜果平均下单频次（天）',
  `dairy_order_days_avg` DECIMAL(38,18) COMMENT '乳制品平均下单频次（天）',
  `other_order_days_avg` DECIMAL(38,18) COMMENT '其他平均下单频次（天）',
  `order_sku_cnt_avg` DECIMAL(38,18) COMMENT '平均去重SKU数',
  `order_spu_cnt_avg` DECIMAL(38,18) COMMENT '平均去重SPU数',
  `self_order_real_amt` DECIMAL(38,18) COMMENT '自营品牌订单实付金额',
  `self_order_days_avg` DECIMAL(38,18) COMMENT '自营品牌平均下单频次（天）'
) 
COMMENT '客户标签汇总表，包含客户生命周期、地域分布、订单行为等多维度标签数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='客户标签汇总表，用于客户生命周期分析和业务决策支持') 
LIFECYCLE 30;