CREATE TABLE IF NOT EXISTS app_saas_goods_near_deadline_summary_di(
	`time_tag` STRING COMMENT '日期，格式：yyyyMMdd',
	`tenant_id` BIGINT COMMENT 'sku租户id，取值范围：2-109',
	`sku_id` BIGINT COMMENT 'saas skuId，取值范围：100590-123064',
	`warehouse_no` BIGINT COMMENT '库存仓ID，取值范围：2-173',
	`warehouse_name` STRING COMMENT '仓库名称，枚举值：郑州自营仓、普冷长沙仓、上海总仓、总仓、福州总仓、上海莲谷仓、虚拟仓-设备、嘉兴海盐总仓、贝塔余杭仓A',
	`batch` STRING COMMENT '批次，批次编码字符串',
	`expiration_date` DATETIME COMMENT '有效期，格式：年月日时分秒',
	`enter_deadline_date` DATETIME COMMENT '进入临期的日期，格式：年月日时分秒',
	`enter_deadline_batch_stock` BIGINT COMMENT '进入临期批次库存，取值范围：1-20000',
	`ending_batch_stock` BIGINT COMMENT '期末库存，取值范围：0-20000',
	`item_id` BIGINT COMMENT '商品id，取值范围：591-40397',
	`sale_price` DECIMAL(38,18) COMMENT '售价'
) 
COMMENT 'SaaS近15天临期货品汇总表，记录商品临期状态和库存信息'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = 'SaaS近15天临期货品汇总表，用于分析商品临期状态和库存变化趋势',
	'columnar.nested.type' = 'true'
)
LIFECYCLE 30;