CREATE TABLE IF NOT EXISTS app_crm_sku_month_gmv_di(
	month_tag STRING COMMENT '月份标记，格式为yyyyMM，如202509表示2025年9月',
	sku_id STRING COMMENT '商品SKU编号，唯一标识一个商品规格',
	self_support BIGINT COMMENT '自营品标记：0-普通商品，1-自营商品',
	area_no BIGINT COMMENT '运营区域编号，取值范围1001-44269',
	gmv DECIMAL(38,18) COMMENT '下单GMV（商品交易总额），单位：元',
	sales_volume BIGINT COMMENT '销量，即销售数量',
	merchant_num BIGINT COMMENT '下单客户数，即购买该商品的商户数量',
	province STRING COMMENT '省份名称，如浙江、上海、江苏等',
	city STRING COMMENT '城市名称，如杭州市、上海市、苏州市等',
	area STRING COMMENT '区县名称，如余杭区、拱墅区、浦东新区等'
) 
COMMENT 'SKU每月下单GMV表，记录每个SKU在各地区的月度销售数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SKU月度GMV统计表，包含商品销售、地区分布等维度数据') 
LIFECYCLE 30;