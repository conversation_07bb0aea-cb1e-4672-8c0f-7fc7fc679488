CREATE TABLE IF NOT EXISTS app_city_delivery_selfowend_di(
    `date` STRING COMMENT '日期，格式：yyyyMMdd',
    `province` STRING COMMENT '省份名称，枚举值：上海、云南、四川、安徽、山东、广东、广西壮族自治区、江苏、江西、浙江、湖北、湖南、福建、贵州、重庆',
    `admin_city` STRING COMMENT '城市名称，如：上海市、昆明市、成都市等',
    `area` STRING COMMENT '区域/区县名称，如：嘉定区、奉贤区、宝山区等',
    `cust_type` STRING COMMENT '客户类型；枚举值：水果/果切/榨汁店、甜品冰淇淋、茶饮、面包蛋糕、咖啡、其他、西餐',
    `brand_type` STRING COMMENT '大客户类型；枚举值：普通、大客户、批发客户',
    `brand_name` STRING COMMENT '品牌名称，枚举值：C味、澄善、Protag蛋白标签、酷盖、ZILIULIU、沐清友、ZEROMIX艾诺兮、SUMMERFARM',
    `category1` STRING COMMENT '一级类目，枚举值：其他、乳制品',
    `category2_id` STRING COMMENT '二级类目ID，枚举值：405、417、401、421、416、409、420、418、415、404、410',
    `category2` STRING COMMENT '二级类目名称，枚举值：成品原料、水果制品、乳制品、饮品原料、蔬菜制品、谷物制品、饮料、糖丨糖制品、食用油丨油脂及制品、茶制品、坚果制品',
    `category3_id` STRING COMMENT '三级类目ID，枚举值：458、504、457、435、503、522、1111、500、470、521、510、506、501、498、455、474、523、428',
    `category3` STRING COMMENT '三级类目名称，枚举值：果冻类配料、冷冻水果、粉圆类配料、液体乳、罐头、果汁原料、配料（小料）类、谷物罐头、植物蛋白饮料、糖浆、水果风味制品、冷冻蔬菜、食用油脂制品、茶叶、烘炒类、咖啡豆及其制品、黄油',
    `category4_id` STRING COMMENT '四级类目ID，枚举值：666、780、665、663、607、779、830、671、1112、661、775、1096、646、713、1007、804、852、608、776、763、785、781、782、654、1107、722、655、832、664、653、1003、831、1009',
    `category4` STRING COMMENT '四级类目名称，枚举值：波波丨晶球、冷冻果肉、椰果、珍珠、常温牛奶、水果罐头、果汁原浆、爆爆珠、配料（小料）、芋圆、杂粮罐头、麻薯、果冻、其他植物蛋白饮料、蔗糖糖浆、水果类馅料、鲜牛奶、冷冻熟蔬菜制品、植脂奶油、果茶酱、冷冻整果、冷冻果泥、绿茶、非氢化基底奶、坚果、红茶、咖啡豆、糯米粉圆、乌龙茶、乳酸黄油、果汁浓浆、混合黄油',
    `origin_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额（原始金额）',
    `real_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额（实际金额，扣除优惠后）',
    `cost_amt` DECIMAL(38,18) COMMENT '总成本金额',
    `origin_pay_margin` DECIMAL(38,18) COMMENT '应付毛利润（原始金额计算）',
    `real_pay_margin` DECIMAL(38,18) COMMENT '实付毛利润（实际金额计算）',
    `preferential_amt` DECIMAL(38,18) COMMENT '营销优惠金额',
    `after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
    `deliver_total_amt` DECIMAL(38,18) COMMENT '配送GMV（商品交易总额）'
)
COMMENT '城市整体配送数据日表，记录各城市配送业务的详细数据，包括客户类型、品牌信息、类目结构、金额指标等'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '城市配送业务数据明细表',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;