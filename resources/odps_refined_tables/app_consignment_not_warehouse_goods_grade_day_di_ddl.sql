```sql
CREATE TABLE IF NOT EXISTS app_consignment_not_warehouse_goods_grade_day_di(
    `sku_id` STRING COMMENT 'SKU_ID，商品唯一标识',
    `spu_name` STRING COMMENT '商品名称',
    `disc` STRING COMMENT '商品规格描述',
    `grade` STRING COMMENT '近30天动销分层标签：A-GMV前0.75%，B-75%-95%，C-剩余部分',
    `outdated` BIGINT COMMENT 'SKU生命周期状态：-1-上新处理中，0-使用中，1-已删除',
    `item_label` STRING COMMENT '是否淘汰标识：0-正常，1-淘汰',
    `date_tag` STRING COMMENT '日期标签，格式为yyyyMMdd'
)
COMMENT '全品类代销不入仓近30天动销分层标签表，用于记录代销不入仓商品的动销分层情况'
PARTITIONED BY (
    `ds` STRING COMMENT '数据日期分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='全品类代销不入仓商品动销分层标签表，包含商品基础信息、动销分层标签和生命周期状态',
    'lifecycle'='30'
);
```