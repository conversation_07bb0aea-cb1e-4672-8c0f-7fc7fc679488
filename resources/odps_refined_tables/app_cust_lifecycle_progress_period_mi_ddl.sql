```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_lifecycle_progress_period_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，表示数据统计的月份',
  `cust_team` STRING COMMENT '客户团队类型，枚举：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `cust_type` STRING COMMENT '客户类型，枚举：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、水果店、水果捞/果切店、社区生鲜店、菜市场水果摊、面包蛋糕点心、请选经营类型、其他',
  `register_province` STRING COMMENT '客户注册时所在省份',
  `register_city` STRING COMMENT '客户注册时所在城市',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细分），枚举：A1、A2、A3、B1、B2、L1、L2、L3、N0、N1、N2、S1、S2、W',
  `cust_cnt` BIGINT COMMENT '总客户数',
  `close_cust_cnt` BIGINT COMMENT '当月闭店客户数',
  `order_days_1_cust_cnt` BIGINT COMMENT '当月下单0天客户数',
  `order_days_2_cust_cnt` BIGINT COMMENT '当月下单1天客户数',
  `order_days_3_cust_cnt` BIGINT COMMENT '当月下单2-3天客户数',
  `order_days_4_cust_cnt` BIGINT COMMENT '当月下单4天+客户数',
  `order_amt_avg_1_cust_cnt` BIGINT COMMENT '当月次均价0客户数',
  `order_amt_avg_2_cust_cnt` BIGINT COMMENT '当月次均价0-50客户数',
  `order_amt_avg_3_cust_cnt` BIGINT COMMENT '当月次均价50-200客户数',
  `order_amt_avg_4_cust_cnt` BIGINT COMMENT '当月次均价200-400客户数',
  `order_amt_avg_5_cust_cnt` BIGINT COMMENT '当月次均价400-600客户数',
  `order_amt_avg_6_cust_cnt` BIGINT COMMENT '当月次均价600+客户数',
  `fruit_amt` DECIMAL(38,18) COMMENT '当月鲜果实付GMV',
  `fruit_cust_cnt` BIGINT COMMENT '当月鲜果购买客户数',
  `dairy_amt` DECIMAL(38,18) COMMENT '当月乳品实付GMV',
  `dairy_cust_cnt` BIGINT COMMENT '当月乳品购买客户数',
  `other_amt` DECIMAL(38,18) COMMENT '当月其他实付GMV',
  `other_cust_cnt` BIGINT COMMENT '当月其他购买客户数',
  `self_amt` DECIMAL(38,18) COMMENT '当月自营品牌实付GMV',
  `self_cust_cnt` BIGINT COMMENT '当月自营品牌购买客户数',
  `spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均SPU数',
  `fruit_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均鲜果SPU数',
  `dairy_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均乳品SPU数',
  `other_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均标品SPU数',
  `lastmonth_order_days_1_cust_cnt` BIGINT COMMENT '上月同期下单0天客户数',
  `lastmonth_order_days_2_cust_cnt` BIGINT COMMENT '上月同期下单1天客户数',
  `lastmonth_order_days_3_cust_cnt` BIGINT COMMENT '上月同期下单2-3天客户数',
  `lastmonth_order_days_4_cust_cnt` BIGINT COMMENT '上月同期下单4天+客户数',
  `lastmonth_order_amt_avg_1_cust_cnt` BIGINT COMMENT '上月同期次均价0客户数',
  `lastmonth_order_amt_avg_2_cust_cnt` BIGINT COMMENT '上月同期次均价0-50客户数',
  `lastmonth_order_amt_avg_3_cust_cnt` BIGINT COMMENT '上月同期次均价50-200客户数',
  `lastmonth_order_amt_avg_4_cust_cnt` BIGINT COMMENT '上月同期次均价200-400客户数',
  `lastmonth_order_amt_avg_5_cust_cnt` BIGINT COMMENT '上月同期次均价400-600客户数',
  `lastmonth_order_amt_avg_6_cust_cnt` BIGINT COMMENT '上月同期次均价600+客户数',
  `lastmonth_fruit_amt` DECIMAL(38,18) COMMENT '上月同期鲜果实付GMV',
  `lastmonth_fruit_cust_cnt` BIGINT COMMENT '上月同期鲜果购买客户数',
  `lastmonth_dairy_amt` DECIMAL(38,18) COMMENT '上月同期乳品实付GMV',
  `lastmonth_dairy_cust_cnt` BIGINT COMMENT '上月同期乳品购买客户数',
  `lastmonth_other_amt` DECIMAL(38,18) COMMENT '上月同期其他实付GMV',
  `lastmonth_other_cust_cnt` BIGINT COMMENT '上月同期其他购买客户数',
  `lastmonth_self_amt` DECIMAL(38,18) COMMENT '上月同期自营品牌实付GMV',
  `lastmonth_self_cust_cnt` BIGINT COMMENT '上月同期自营品牌购买客户数',
  `lastmonth_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月同期平均SPU数',
  `lastmonth_fruit_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月同期平均鲜果SPU数',
  `lastmonth_dairy_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月同期平均乳品SPU数',
  `lastmonth_other_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月同期平均标品SPU数'
) 
COMMENT '客户进度月汇总表(同期)，用于统计客户生命周期进度、订单行为、商品购买等指标的月度汇总数据，支持同期对比分析'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='客户生命周期进度月汇总表，包含客户基础信息、订单行为、商品购买等维度的月度统计指标') 
LIFECYCLE 30;
```