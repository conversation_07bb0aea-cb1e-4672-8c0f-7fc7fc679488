```sql
CREATE TABLE IF NOT EXISTS app_crm_wecom_bd_summary_df(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `bd_id` BIGINT COMMENT '销售ID，唯一标识一个销售人员',
    `bd_name` STRING COMMENT '销售姓名',
    `m1_name` STRING COMMENT '城市负责人名称（M1管理者），即销售的直接上级',
    `m2_name` STRING COMMENT '区域负责人名称（M2管理者），即M1的直接上级',
    `m3_name` STRING COMMENT '部门负责人名称（M3管理者），即M2的直接上级',
    `region` STRING COMMENT '大区名称，取值范围：华南一区、上海大区、山东大区、苏皖大区、浙江大区、川渝大区、云贵桂大区、华南二区、福泉、华中大区、四川、虚拟区域',
    `private_sea_count` BIGINT COMMENT '私海客户数，表示销售人员拥有的私海客户数量',
    `effective_wecom_user_count` BIGINT COMMENT '有效企微客户数，表示与销售人员建立有效联系的企微客户数量',
    `wecom_user_count` BIGINT COMMENT '销售添加客户数量，表示销售人员主动添加的企微客户总数',
    `bd_delete_wecom_count` BIGINT COMMENT '销售删除客户数，表示销售人员主动删除的企微客户数量',
    `user_delete_wecom_count` BIGINT COMMENT '用户删除客户数，表示客户主动删除销售人员的数量',
    `delete_wecom_count` BIGINT COMMENT '互删客户数，表示双方互相删除的客户数量'
)
COMMENT '销售企微拉新统计表，记录销售人员通过企业微信进行客户拉新的各项指标数据，包括客户数量、添加删除情况等'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '销售企微拉新统计表',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```