CREATE TABLE IF NOT EXISTS app_sku_order_load_time_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
    `week` BIGINT COMMENT '星期几，取值范围：1-7（1表示周一，7表示周日）',
    `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位的唯一标识',
    `spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
    `sku_disc` STRING COMMENT '商品描述，包含规格、包装等详细信息',
    `cust_cnt` BIGINT COMMENT '交易客户数，下单购买该SKU的客户数量',
    `loading_cust_cnt` BIGINT COMMENT '登录客户数，浏览或访问该SKU的客户数量'
)
COMMENT '下单时间段偏好SKU数据，记录不同时间段内客户对SKU的下单偏好和访问行为'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='下单时间段偏好SKU数据分析表，用于分析客户在不同时间段的商品购买和浏览偏好')
LIFECYCLE 30;