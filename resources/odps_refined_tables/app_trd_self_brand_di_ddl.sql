```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_trd_self_brand_di` (
  `date` STRING COMMENT '业务日期，格式：yyyyMMdd',
  `brand_name` STRING COMMENT '品牌名称，枚举值：Protag蛋白标签、C味、酷盖、沐清友、ZEROMIX艾诺兮、澄善、ZILIULIU、SUMMERFARM',
  `large_area_name` STRING COMMENT '运营大区名称，枚举值：广州大区、福州大区、上海大区、杭州大区、苏州大区、成都大区、长沙大区、青岛大区、苏南大区、昆明大区、武汉大区、重庆大区、南宁大区、贵阳大区、昆明快递大区',
  `m3_name` STRING COMMENT 'M3管理者姓名，枚举值：孙日达、吕建杰、李茂源',
  `m2_name` STRING COMMENT 'M2管理者姓名，枚举值：陈欲豪、林金秋、赵奎、李茂源、桂少达、翟远方、姜浪、彭琨、孙军杰、孙日达',
  `m1_name` STRING COMMENT 'M1管理者姓名，枚举值：肖时煌、罗慧、徐晟昊、李茂源、张浩亮、葛世豪、陈俊生、翟远方、王业鼎、张茂权、林献、陈露露、陶京龙、骆婷婷、李钱程、陈锐石、李光远、陈忠良、王金浩、冯朝皇、蒋柳选、唐宽、汪林俊、习燕庆、韦贵丰',
  `cust_type` STRING COMMENT '客户业态类型，枚举值：咖啡、茶饮、其他、面包蛋糕、甜品冰淇淋、西餐、水果/果切/榨汁店、水果店',
  `cust_group` STRING COMMENT '客户类型分组，枚举值：平台客户、大客户、批发客户',
  `cust_id` BIGINT COMMENT '客户唯一标识ID，数值范围：143-576003',
  `first_buy_date` STRING COMMENT '商品维度首次购买时间，格式：yyyyMMdd',
  `category1` STRING COMMENT '商品一级类目，枚举值：乳制品、其他',
  `category2` STRING COMMENT '商品二级类目，枚举值：乳制品、饮料、成品原料、茶制品、谷物制品、坚果制品、食用油丨油脂及制品、蔬菜制品、水果制品、糖丨糖制品、饮品原料',
  `category3` STRING COMMENT '商品三级类目，枚举值：黄油、植物蛋白饮料、粉圆类配料、果冻类配料、液体乳、茶叶、袋泡茶、谷物罐头、烘炒类、食用油脂制品、罐头、冷冻蔬菜、配料（小料）类、冷冻水果、水果风味制品、糖浆、果汁原料、咖啡豆及其制品',
  `category4` STRING COMMENT '商品四级类目，枚举值：乳酸黄油、其他植物蛋白饮料、混合黄油、麻薯、波波丨晶球、常温牛奶、鲜牛奶、绿茶、红茶、绿茶包、芋圆、珍珠、椰果、爆爆珠、杂粮罐头、坚果、植脂奶油、非氢化基底奶、冷冻熟蔬菜制品、配料（小料）、水果罐头、冷冻果肉、冷冻整果、冷冻果泥、果茶酱、蔗糖糖浆、果汁原浆、果汁浓浆、水果类馅料、咖啡豆、果冻、乌龙茶、糯米粉圆、乌龙茶包、红茶茶包',
  `spu_name` STRING COMMENT '商品SPU名称',
  `sku_id` STRING COMMENT '商品SKU唯一标识',
  `sku_disc` STRING COMMENT '商品规格描述',
  `orgin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV金额，单位：元',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV金额，单位：元',
  `trade_sku_cnt` BIGINT COMMENT '交易SKU数量，数值范围：0-100',
  `dlv_orgin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV金额，单位：元',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV金额，单位：元',
  `dlv_sku_cnt` BIGINT COMMENT '履约SKU数量，数值范围：0-40',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本金额，单位：元'
) 
COMMENT '自营品牌交易数据表，包含品牌销售、客户、商品、交易和履约等维度的详细数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='自营品牌交易明细数据表，用于分析品牌销售表现、客户购买行为和商品交易情况',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```