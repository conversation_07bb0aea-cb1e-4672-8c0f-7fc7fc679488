CREATE TABLE IF NOT EXISTS app_cust_brand_performance_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`order_origin_amt` DECIMAL(38,18) COMMENT '交易应付总金额，单位为元',
	`order_real_amt` DECIMAL(38,18) COMMENT '交易实付总金额，单位为元',
	`delivery_origin_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位为元',
	`delivery_real_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位为元',
	`delivery_cash_real_amt` DECIMAL(38,18) COMMENT '履约现结实付总金额，单位为元',
	`delivery_bill_real_amt` DECIMAL(38,18) COMMENT '履约账期实付总金额，单位为元',
	`delivery_cost_amt` DECIMAL(38,18) COMMENT '履约商品成本金额，单位为元',
	`delivery_real_gross` DECIMAL(38,18) COMMENT '履约实付毛利润，单位为元',
	`delivery_real_gross_rate` DECIMAL(38,18) COMMENT '履约实付毛利率，取值范围为-1到1之间的小数',
	`delivery_cust_cnt` BIGINT COMMENT '履约客户数，非负整数',
	`delivery_point_cnt` BIGINT COMMENT '履约累计点位数，非负整数',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，单位为元',
	`after_sale_rate` DECIMAL(38,18) COMMENT '售后比例(已到货售后金额/履约实付GMV)，取值范围为0到1之间的小数'
) 
COMMENT '大客户整体监控表，用于监控大客户的交易、履约和售后等关键业务指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='大客户整体业务绩效监控表，包含交易、履约、售后等核心业务指标') 
LIFECYCLE 30;