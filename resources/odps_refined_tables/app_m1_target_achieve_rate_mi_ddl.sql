CREATE TABLE IF NOT EXISTS app_m1_target_achieve_rate_mi(
	months STRING COMMENT '月份，格式：yyyyMM',
	m3 STRING COMMENT '最新M3管理者姓名，取值范围：吕建杰、孙日达',
	m2 STRING COMMENT '最新M2管理者姓名，取值范围：赵奎、陈欲豪、桂少达、彭琨、林金秋、翟远方、孙军杰、姜浪、孙日达',
	m1 STRING COMMENT '最新M1管理者姓名，取值范围：赵奎、陈俊生、李光远、习燕庆、王金浩、罗慧、王业鼎、肖时煌、骆婷婷、林献、李钱程、陈忠良、姜浪、汪林俊、陶京龙、蒋柳选、翟远方、唐宽、张浩亮、韦贵丰、冯朝皇、陈露露、张茂权、葛世豪、刘大鹏',
	zone_name STRING COMMENT '销售区域名称，取值范围：浦东、大粤西、无锡、江西、苏州、厦门、浙南、东莞、佛山、广州、杭州、济南、贵阳、重庆、四川、昆明、杭州湾、武汉、福泉、广西、徽京、浦西、深圳、苏北、长沙、青岛',
	no_at_gmv_target DECIMAL(38,18) COMMENT '非ATGMV目标金额',
	no_at_gmv_achieve_amt DECIMAL(38,18) COMMENT '非ATGMV实际达成金额',
	no_at_gmv_achieve_rate DECIMAL(38,18) COMMENT '非ATGMV达成率',
	no_at_gmv_score_num DECIMAL(38,18) COMMENT '非ATGMV绩效得分',
	fruit_gmv_target DECIMAL(38,18) COMMENT '鲜果GMV目标金额',
	fruit_gmv_achieve_amt DECIMAL(38,18) COMMENT '鲜果GMV实际达成金额',
	fruit_gmv_achieve_rate DECIMAL(38,18) COMMENT '鲜果GMV达成率',
	fruit_gmv_score_num DECIMAL(38,18) COMMENT '鲜果GMV绩效得分',
	effective_cust_target DECIMAL(38,18) COMMENT '有效月活客户数目标',
	effect_cust_cnt DECIMAL(38,18) COMMENT '有效月活客户数实际达成',
	effect_cust_achieve_rate DECIMAL(38,18) COMMENT '有效月活客户数达成率',
	effect_cust_score_num DECIMAL(38,18) COMMENT '有效月活客户数绩效得分，取值范围：0、0.1',
	total_score_num DECIMAL(38,18) COMMENT '综合绩效总得分'
) 
COMMENT '销售M1维度目标达成绩效得分表，用于月度算薪，包含非ATGMV、鲜果GMV、有效月活客户数三个维度的目标达成情况和绩效得分'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true') 
LIFECYCLE 30;