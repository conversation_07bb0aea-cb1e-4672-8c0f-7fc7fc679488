CREATE TABLE IF NOT EXISTS app_sale_large_area_category_kpi_trade_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
    `large_area_name` STRING COMMENT '运营服务大区，取值范围：成都大区、南宁大区、武汉大区、广州大区、重庆大区、上海大区、昆明大区、青岛大区、长沙大区、苏州大区、苏南大区、广东一点点快递区域、福州大区、贵阳大区、杭州大区、昆明快递大区',
    `category` STRING COMMENT '品类，取值范围：鲜果、乳制品、其他',
    `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元）',
    `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元）',
    `order_cust_cnt` BIGINT COMMENT '交易客户数',
    `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（应付总金额/客户数，元/客户）',
    `order_cnt` BIGINT COMMENT '交易订单数',
    `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
    `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
    `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
    `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润（元）',
    `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润（元）',
    `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润（元）',
    `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次（天/次）',
    `delivery_point_cnt` BIGINT COMMENT '履约点位数',
    `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用（元）',
    `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额（元）',
    `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额（元）',
    `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
    `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润（元）',
    `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额（元）',
    `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额（元）',
    `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
    `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润（元）',
    `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
    `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
    `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
    `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总表，按大区和品类维度统计交易和履约相关指标，包括金额、客户数、订单数、利润等核心业务指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '销售KPI指标汇总表',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;