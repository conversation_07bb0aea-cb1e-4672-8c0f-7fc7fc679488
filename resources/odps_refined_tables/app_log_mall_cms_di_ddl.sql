CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_mall_cms_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `cust_type` STRING COMMENT '客户行业类型，枚举值包括：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、其他',
  `large_area_id` BIGINT COMMENT '运营服务大区ID，数值型标识',
  `large_area_name` STRING COMMENT '运营服务大区名称，枚举值包括：杭州大区、上海大区、广州大区、成都大区、重庆大区、长沙大区、南宁大区、昆明大区、苏州大区、贵阳大区、青岛大区、苏南大区、武汉大区、福州大区等',
  `cms_page_id` STRING COMMENT '活动页ID，字符串型标识符',
  `cms_page_name` STRING COMMENT '活动页名称，描述性名称',
  `enter_pv` BIGINT COMMENT '进入PV（页面访问量），统计用户进入活动页的次数',
  `enter_uv` BIGINT COMMENT '进入UV（独立访客数），统计访问活动页的独立用户数',
  `sku_click_pv` BIGINT COMMENT '商品点击PV，统计用户点击商品的次数',
  `sku_click_uv` BIGINT COMMENT '商品点击UV，统计点击商品的独立用户数',
  `sku_cart_buy_pv` BIGINT COMMENT '商品加购PV，统计用户将商品加入购物车的次数',
  `sku_cart_buy_uv` BIGINT COMMENT '商品加购UV，统计将商品加入购物车的独立用户数',
  `sku_instant_buy_pv` BIGINT COMMENT '商品立即购买PV，统计用户直接购买商品的次数',
  `sku_instant_buy_uv` BIGINT COMMENT '商品立即购买UV，统计直接购买商品的独立用户数',
  `sku_order_order_cnt` BIGINT COMMENT '购买次数，统计用户成功下单的次数',
  `sku_order_cust_cnt` BIGINT COMMENT '购买人数，统计成功下单的独立用户数'
) 
COMMENT '乐高活动页页流量分析表，用于分析各活动页的用户访问行为和转化效果'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '乐高活动页流量统计分析表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;