```sql
CREATE TABLE IF NOT EXISTS app_crm_follow_record_detail_df(
    `date` STRING COMMENT '拜访日期，格式：yyyyMMdd',
    `month` STRING COMMENT '拜访月份，格式：yyyyMM',
    `follow_up_record_id` BIGINT COMMENT '拜访记录ID，唯一标识每次拜访',
    `add_time` DATETIME COMMENT '拜访时间，格式：yyyy-MM-dd HH:mm:ss',
    `follow_up_way` STRING COMMENT '拜访方式，枚举值：有效拜访、普通上门拜访、普通拜访-电话&微信、普通拜访-企微',
    `visit_objective` STRING COMMENT '拜访目的，枚举值：客户维护、拓品、催月活、催省心送、售后处理、拉新',
    `bd_id` BIGINT COMMENT '销售ID，唯一标识销售人员',
    `bd_name` STRING COMMENT '销售姓名',
    `m1_name` STRING COMMENT 'M1管理者（销售主管）姓名，即BD的直接上级',
    `m2_name` STRING COMMENT 'M2管理者（销售经理）姓名，即M1的直接上级',
    `m3_name` STRING COMMENT 'M3管理者（销售总监）姓名，即M2的直接上级',
    `zone_name` STRING COMMENT '销售区域，枚举值：苏州、无锡、杭州湾、杭州、四川外区、广州、深圳二组、东莞、浙南、重庆、深圳、徽京、苏北、大粤西、浦西、江西、武汉,江西,长沙、贵阳,重庆、测试权限数据',
    `cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
    `cust_name` STRING COMMENT '客户名称',
    `register_city` STRING COMMENT '注册城市，客户注册所在地',
    `time_type` STRING COMMENT '拜访时间分段，枚举值：0-7点、8-12点、13-21点、22-23点',
    `is_compliant` STRING COMMENT '是否合规，枚举值：是、否',
    `is_abnormal` STRING COMMENT '是否异常，枚举值：是、否',
    `is_2hours_interval` STRING COMMENT '是否间隔2小时，枚举值：<120(小于120分钟)、>=120(大于等于120分钟)',
    `condition` STRING COMMENT '跟进情况描述，记录拜访过程中的具体情况',
    `expected_content` STRING COMMENT '期望内容，拜访前预期的沟通内容',
    `feedback` STRING COMMENT '客户反馈，客户对拜访的回应，枚举值：None、已登门拜访、未见到销售经理',
    `is_escort` STRING COMMENT '是否陪访，枚举值：是、否',
    `escort_name` STRING COMMENT '陪访人姓名',
    `rn` BIGINT COMMENT '拜访顺序，当天拜访的序号',
    `next_add_time` DATETIME COMMENT '下一个客户拜访时间，格式：yyyy-MM-dd HH:mm:ss',
    `min_add_time` DATETIME COMMENT '最早出勤时间，格式：yyyy-MM-dd HH:mm:ss',
    `max_add_time` DATETIME COMMENT '最晚出勤时间，格式：yyyy-MM-dd HH:mm:ss',
    `visit_cust_cnt` BIGINT COMMENT 'BD当天拜访客户数',
    `effective_visit_cust_cnt` BIGINT COMMENT 'BD当天上门拜访有效客户数',
    `cust_visit_cnt_7d` BIGINT COMMENT '该客户近7天被拜访次数',
    `is_paid` STRING COMMENT '该客户拜访后7天内是否购买，枚举值：是、否',
    `dlv_profit_group` STRING COMMENT '履约后利润分层(大类)，枚举值：无、A、B、B+、C'
)
COMMENT '销售拜访客户过程跟踪表，记录销售人员拜访客户的详细过程信息，包括拜访时间、方式、目的、客户反馈等'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '销售拜访客户过程跟踪表，用于分析销售拜访行为和客户关系管理',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```