```sql
CREATE TABLE IF NOT EXISTS app_finance_store_record_details_di(
    service_area STRING COMMENT '大区，取值范围：华东、未知、华中、西南、福建、山东、贵州、云南、广西',
    warehouse_no BIGINT COMMENT '库存仓ID，数值型标识',
    warehouse_name STRING COMMENT '库存仓名，取值范围：嘉兴总仓、东莞总仓、嘉兴海盐总仓、上海总仓、南京总仓、东莞冷冻总仓、武汉总仓、长沙总仓、华西总仓、福州总仓、重庆总仓、青岛总仓、贵阳总仓、昆明总仓、济南总仓、南宁总仓、苏州总仓',
    type STRING COMMENT '出入库类型，取值范围：销售出库、转换出库、出样出库、96、补货出库、新退货入库、货损出库、越库出库、转换入库、调拨入库、多出入库、采购入库、盘亏出库、调拨出库、采购退货出库、盘盈入库、销售自提、86、调拨异常回库、越库入库、缺货入库、其他入库、拒收回库、拦截入库',
    sku STRING COMMENT 'SKU，商品唯一编码',
    pd_name STRING COMMENT '商品名',
    sku_disc STRING COMMENT '规格描述',
    tax_rate DECIMAL(38,18) COMMENT '税率，小数精度18位',
    batch STRING COMMENT '批次号',
    production_date DATETIME COMMENT '生产日期，年月日时分秒格式',
    quality_date DATETIME COMMENT '保质期，年月日时分秒格式',
    supplier STRING COMMENT '供应商名称',
    quantity BIGINT COMMENT '数量，正数表示入库，负数表示出库',
    cost DECIMAL(38,18) COMMENT '单件成本，小数精度18位',
    cost_amt DECIMAL(38,18) COMMENT '总成本，小数精度18位',
    record_time DATETIME COMMENT '出入库时间，年月日时分秒格式',
    store_quantity BIGINT COMMENT '出入库后数量',
    date_flag STRING COMMENT '日期标识，yyyyMMdd格式',
    category1 STRING COMMENT '商品一级类目，取值范围：其他、鲜果、乳制品',
    cost_amt_notax DECIMAL(38,18) COMMENT '总成本(不含税)，小数精度18位',
    sub_type BIGINT COMMENT '业务子类型：1-自营代销不入仓、2-自营代销入仓、3-自营经销',
    settle_type STRING COMMENT '结算类型，取值范围：空字符串、成本结算',
    supplier_id BIGINT COMMENT '供应商id，数值型标识'
)
COMMENT '财务口径期末库存表，记录库存出入库明细及成本信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式')
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '财务口径期末库存明细表，包含商品出入库记录、成本核算及库存状态信息',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```