```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_cust_route_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
  `page_one` STRING COMMENT '页面一，用户访问的第一个页面URL或页面标识，取值范围包括：/home、/search、/self、加入购物车、/public-goods-details、/goods、/search/goods-new、/purchase-assistant、/cart、/goods/coupon、鲜沐农场、/timing/details、/goods/category、活动、/timing/single、/special-offer、banner、/activeMeetings、/new-meetings、AI采购、/self/coupon/list、/timing、/service/research、/self/order、/shop/invite、pages/b2b/b2b、/address、/self/order/details、/self/cards、/timing/list、/pay-success、/self/timing/order/details、为你推荐、/complete/info、门店认证、/afterSale/Details等',
  `page_two` STRING COMMENT '页面二，用户访问的第二个页面URL或页面标识，取值范围包括：/search、/home、/self、加入购物车、/public-goods-details、/goods、/search/goods-new、/purchase-assistant、/cart、/goods/coupon、鲜沐农场、/timing/details、/goods/category、活动、/timing/single、/special-offer、banner、/activeMeetings、/new-meetings、AI采购、/self/coupon/list、/timing、/service/research、/self/order、/shop/invite、pages/b2b/b2b、/address、/self/order/details、/self/cards、/timing/list、/pay-success、/self/timing/order/details、为你推荐、/complete/info、门店认证、/afterSale/Details等',
  `page_three` STRING COMMENT '页面三，用户访问的第三个页面URL或页面标识，取值范围包括：/search/goods-new、活动、/self/order、/cart、加入购物车、/search、/home、/order-confirm、/self/order/details、/coupon-list、/goods、/timing/list、/self、/xmAiChat、/self/problem、/address、/goods/category、/purchase-assistant、/public-goods-details、/special-offer、/timing/details、鲜沐农场、/self/coupon/list、/self/reimburse、/user-comment、/activeMeetings、/new-meetings、/navigation-details、门店认证、/afterSale/Details、banner、/self/cards、搜索发现、/afterSaleRule、/timing、/rate、/shop/details、/invoice-center、/self/selfVipDetails、/invoice-title、/timing/pay、/address/edit、/collect-order、横板竖版筛选、/shopMessage、/goods/coupon、pages/loading/loading、/merchant-info、/timing/single等',
  `page_four` STRING COMMENT '页面四，用户访问的第四个页面URL或页面标识，取值范围包括：/search/goods-new、/new-meetings、/home、/public-goods-details、加入购物车、/goods、/cart、/self/order、/self/order/details、/self、/timing/details、/search、横板竖版筛选、/select/address、/user-comment、/order-confirm、鲜沐农场、/goods/category、/timing/single、/afterSale/chouse、活动、/self/coupon/list、门店认证、/goods/coupon、/self/problem、/purchase-assistant、/timing/list、/activeMeetings、/self/coupon/list/history、/timing/pay、banner、/rate、/coupon-list、/special-offer、/shop/invite、/invoice-center、/afterSale/Details、/shop/account/details、/self/balance、/navigation-details、/address、/self/order/export-order、/service/research、/timing、/shopMessage、/invoice-title、/address-map、/shop/details、pages/b2b/b2b等',
  `page_five` STRING COMMENT '页面五，用户访问的第五个页面URL或页面标识，取值范围包括：/search、/home、/goods/category、/self/order/details、/public-goods-details、/order-confirm、/goods、/cart、加入购物车、/afterSale/chouse、/search/goods-new、/self、/timing、/self/order、鲜沐农场、/timing/list、/self/reimburse、/saleAfterSale、/self/coupon/list、/activeMeetings、/rate、/new-meetings、/coupon-list、/purchase-assistant、/timing/single、横板竖版筛选、/self/problem、/timing/details、优惠明细、活动、/user-comment、/special-offer、/afterSale/Details、/pay-success、/select/address、/invoice-title、banner、/navigation-details、/address、/marketing、/shop/details、/collect-order、/timing/pay、为你推荐、/invoice-center、/self/balance/details、/self/cards、/shopMessage、/xmAiChat等',
  `page_six` STRING COMMENT '页面六，用户访问的第六个页面URL或页面标识，取值范围包括：/public-goods-details、活动、/self、/self/order、/search/goods-new、/goods、加入购物车、/home、/order-confirm、/search、/self/order/details、/goods/coupon、/saleAfterSale、/payQRcode、/cart、鲜沐农场、/self/timing/order/details、/afterSale/chouse、横板竖版筛选、/self/problem、/user-comment、/timing/list、/purchase-assistant、/goods/category、/navigation-details、/timing/details、/new-meetings、/coupon-list、/timing/pay、/self/coupon/list、/self/cards、/activeMeetings、/pay-success、/afterSale/Details、/rate、banner、/special-offer、/timing/single、/address-map、/invoice-center、/timing、/user-comment-details、/shop/details、/invoice-detail、优惠明细、门店认证、/select/address、/self/detail、/address/edit等',
  `pv` BIGINT COMMENT '页面浏览量，统计用户访问页面的次数，取值范围：2-7757',
  `uv` BIGINT COMMENT '独立访客数，统计访问页面的独立用户数量，取值范围：1-421'
) 
COMMENT '用户路径数据表，记录用户在平台上的页面访问路径和行为数据，用于分析用户行为模式和优化用户体验'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
   'comment'='用户路径行为分析表，包含用户页面访问序列和访问量统计')
LIFECYCLE 30;
```