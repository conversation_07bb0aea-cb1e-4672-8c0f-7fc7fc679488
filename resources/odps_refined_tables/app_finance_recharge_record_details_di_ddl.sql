```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_finance_recharge_record_details_di` (
  `recharge_record_no` STRING COMMENT '变动记录编号，唯一标识每笔变动记录',
  `addtime` DATETIME COMMENT '记录时间，格式为年月日时分秒',
  `cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
  `cust_name` STRING COMMENT '客户名称',
  `city_id` BIGINT COMMENT '城市ID，唯一标识城市',
  `city_name` STRING COMMENT '城市名称',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：平台客户、大客户',
  `recharge_type` STRING COMMENT '余额变动类型，枚举值：消费、充值、订单退款',
  `recharge_amount` DECIMAL(38,18) COMMENT '变动金额，支持18位小数精度',
  `after_amount` DECIMAL(38,18) COMMENT '变动后剩余金额，支持18位小数精度',
  `record_no` STRING COMMENT '关联业务编号：订单号、售后单号、充值单号等',
  `account_id` BIGINT COMMENT '操作子账号ID，标识操作人员',
  `account_name` STRING COMMENT '操作人名称',
  `date_flag` STRING COMMENT '日期标识，格式为yyyyMMdd'
)
COMMENT '财务口径鲜沐卡变动明细表，记录客户余额变动明细信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '财务口径鲜沐卡变动明细表，包含客户余额变动的完整记录信息',
  'lifecycle' = '30'
);
```