CREATE TABLE IF NOT EXISTS app_stock_dashboard_history_di(
    `view_date` DATETIME COMMENT '视图日期，格式为年月日时分秒',
    `sku_id` STRING COMMENT 'SKU编号，商品库存单位唯一标识',
    `pd_id` BIGINT COMMENT '商品编号，商品唯一标识',
    `warehouse_no` BIGINT COMMENT '仓库编号，取值范围：2-176',
    `sales_quantity` BIGINT COMMENT '销量出库数量，实际销售出库的商品数量',
    `transfer_out_quantity` BIGINT COMMENT '调拨出库量，仓库间调拨出库的商品数量',
    `consumption` BIGINT COMMENT '消耗量，商品消耗数量',
    `enabled_quantity` BIGINT COMMENT '可用库存，当前可用的商品库存数量',
    `on_way_quantity` BIGINT COMMENT '采购在途库存，采购中但尚未到货的商品数量',
    `allocate_in_quantity` BIGINT COMMENT '调拨在途库存，调拨中但尚未到货的商品数量',
    `init_quantity` BIGINT COMMENT '期初库存，周期开始时的商品库存数量',
    `terminal_enabled_quantity` BIGINT COMMENT '期末可用库存，周期结束时的可用商品库存数量',
    `on_way_order_quantity` BIGINT COMMENT '采购订单在途数量，采购订单中尚未到货的商品数量',
    `order_sales_quantity` BIGINT COMMENT '订单销量，订单中的销售商品数量',
    `timing_delivery_out_quantity` BIGINT COMMENT '省心送计划出库量，定时配送计划的出库数量'
)
COMMENT '罗盘历史数据表，存储商品库存、销售、调拨等相关历史数据'
PARTITIONED BY (
    `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '罗盘历史数据表，包含商品库存、销售、调拨等维度的历史统计信息',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;