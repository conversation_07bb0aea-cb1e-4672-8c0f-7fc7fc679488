CREATE TABLE IF NOT EXISTS app_crm_merchant_day_label_df(
    cust_id BIGINT COMMENT '商户ID，唯一标识一个商户',
    merchant_label STRING COMMENT '客户标签，枚举值包括：月活老客户、海南水仙芒、活跃客户、三麟苏打汽水、ZILIULIU竹蔗冰糖糖浆、C味椰果果粒、kiri奶油奶酪、云南蓝莓、优诺4.0冷藏牛乳、即食智利牛油果等100+个标签值',
    area_no BIGINT COMMENT '地区编号，数值型编码，范围：1001-44264',
    size STRING COMMENT '客户规模分类，枚举值：大客户-1、大连锁-2、小连锁-3、单点-4（单店）',
    label_type BIGINT COMMENT '客户标签类型，枚举值：1-客户属性及行为标签，2-商品标签'
)
COMMENT '客户标签表，存储商户的各类标签信息，包括客户属性标签和商品标签'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='客户标签明细表，包含商户的基本属性标签和商品偏好标签')
LIFECYCLE 30;