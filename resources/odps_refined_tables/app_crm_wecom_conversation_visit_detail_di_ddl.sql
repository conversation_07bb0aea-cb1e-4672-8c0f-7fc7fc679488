```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_wecom_conversation_visit_detail_di`(
    `bd_id` BIGINT COMMENT 'BD ID，销售人员的唯一标识',
    `bd_user_id` STRING COMMENT 'BD用户ID，销售人员在系统内的登录账号',
    `bd_name` STRING COMMENT 'BD姓名，销售人员的真实姓名',
    `cust_external_user_id` STRING COMMENT '客户外部用户ID，企业微信客户的唯一标识',
    `m_id` BIGINT COMMENT '客户ID，客户在系统中的唯一标识',
    `cust_name` STRING COMMENT '客户名称，客户的店铺或企业名称',
    `conversation_list` STRING COMMENT '对话内容列表，JSON格式存储完整的聊天记录',
    `image_list` STRING COMMENT '图片链接列表，JSON格式存储对话中涉及的图片URL',
    `date` STRING COMMENT '对话日期，格式为yyyy-MM-dd',
    `conversation_start_time` DATETIME COMMENT '对话开始时间，格式为yyyy-MM-dd HH:mm:ss',
    `conversation_end_time` DATETIME COMMENT '对话结束时间，格式为yyyy-MM-dd HH:mm:ss',
    `is_valid_visit` BOOLEAN COMMENT '有效拜访标识：true-有效拜访（有实质性业务交流），false-无效拜访（如简单问候或测试消息）',
    `create_time` STRING COMMENT '记录创建时间，格式为yyyy-MM-dd HH:mm:ss'
)
PARTITIONED BY (
    `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '企业微信对话拜访明细表，记录销售人员与客户在企业微信中的完整对话内容和拜访有效性判断',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 365;
```