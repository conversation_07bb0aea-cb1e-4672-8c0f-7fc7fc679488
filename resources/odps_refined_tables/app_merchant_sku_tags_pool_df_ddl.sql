CREATE TABLE IF NOT EXISTS app_merchant_sku_tags_pool_df(
    cust_id BIGINT COMMENT '客户ID，数值型标识，取值范围：1-57',
    tag_value STRING COMMENT '未购买SKU ID，字符串类型，表示用户未购买的商品SKU编码，取值范围：包含大量唯一值（约8667个），如1012223826452、1017770023205等，建议作为开放枚举值处理'
)
COMMENT '圈人平台用户标签池表，用于存储用户未购买商品的SKU标签数据，支持精准营销和用户画像分析'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期（年月日）')
STORED AS ALIORC
TBLPROPERTIES ('comment'='圈人平台用户标签池表，存储用户未购买SKU标签数据')
LIFECYCLE 60;