CREATE TABLE IF NOT EXISTS app_log_recommend_detail_di(
	`time` DATETIME COMMENT '事件发生时间，格式为年月日时分秒',
	`scene` STRING COMMENT '场景：首页、商品详情页、购物车页、活动页、分类页、特价专区',
	`url` STRING COMMENT '页面地址URL',
	`envent_type` STRING COMMENT '事件类型：click-点击，impression-曝光，add_to_cart-加购，buy_now-立即购买，purchase-提交订单，checkout-发起支付，paid-支付完成',
	`idx` STRING COMMENT '推荐位位置下标',
	`sku_id` STRING COMMENT '商品SKU ID',
	`spu_id` BIGINT COMMENT '商品SPU ID（商品ID）',
	`sku_price` STRING COMMENT 'SKU价格，单位：元',
	`master_order_no` STRING COMMENT '主订单编号',
	`cid` STRING COMMENT '客户端唯一标识',
	`sid` STRING COMMENT '单次打开商城的会话唯一ID',
	`count` BIGINT COMMENT '单次打开页面的埋点计数',
	`user_agent` STRING COMMENT '用户设备信息',
	`cust_id` BIGINT COMMENT '客户ID',
	`cust_name` STRING COMMENT '客户名称',
	`cust_type` STRING COMMENT '客户类型：烘焙、茶饮、咖啡、面包蛋糕、甜品冰淇淋、西餐、其他',
	`default_address` STRING COMMENT '店铺默认地址信息（省市区）',
	`is_new` STRING COMMENT '是否当日注册：是/否',
	`abexperiments_experiment_id` STRING COMMENT 'AB实验解析字段-experiment_id',
	`abexperiments_experiment_place` STRING COMMENT 'AB实验解析字段-experiment_place',
	`abexperiments_variant_id` STRING COMMENT 'AB实验解析字段-variant_id'
) 
COMMENT '商城推荐流量分析明细表，记录推荐位各种用户行为事件的详细数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，yyyyMMdd格式，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商城推荐流量分析明细表，用于分析推荐位的曝光、点击、转化等用户行为数据') 
LIFECYCLE 30;