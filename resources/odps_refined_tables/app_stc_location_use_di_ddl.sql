CREATE TABLE IF NOT EXISTS app_stc_location_use_di(
    `warehouse_no` STRING COMMENT '库存仓编号，取值范围：10、69、117、125',
    `warehouse_name` STRING COMMENT '库存仓名称，取值范围：嘉兴总仓、东莞总仓、东莞冷冻总仓、南京总仓',
    `zone_type` STRING COMMENT '库区类型，取值范围：冷藏、常温',
    `cabinet_type` STRING COMMENT '库位类型，取值范围：地堆、高位货架、地位货架',
    `use_volume` DECIMAL(38,18) COMMENT '使用体积，单位：立方米',
    `use_code` BIGINT COMMENT '使用库位数，统计范围：0-1378',
    `logic_volume` DECIMAL(38,18) COMMENT '理论体积，单位：立方米',
    `total_volume` DECIMAL(38,18) COMMENT '实际体积，单位：立方米',
    `total_code` BIGINT COMMENT '总库位数，统计范围：1-3180'
)
COMMENT '仓配库位使用记录表，记录各仓库库位的使用情况和容量统计'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='仓配库位使用记录表，包含各仓库库区的使用体积、库位数量等统计信息')
LIFECYCLE 30;