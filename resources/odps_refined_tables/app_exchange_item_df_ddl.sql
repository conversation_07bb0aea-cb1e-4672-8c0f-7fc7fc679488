CREATE TABLE IF NOT EXISTS app_exchange_item_df(
    cust_id BIGINT COMMENT '客户ID，数值范围：12-25031',
    sku_id STRING COMMENT '商品SKU编码，包含3393个不同的商品编码，包括：699200206153、830016333885、undefined等',
    type BIGINT COMMENT '行为类型，枚举值：0-客户浏览未购买，1-同行购买客户未购买',
    order BIGINT COMMENT '推荐排序序号，数值范围：1-30'
)
COMMENT '换购活动推荐商品表，用于存储客户在换购活动中被推荐的商品信息，包含客户行为类型和推荐排序'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='换购活动推荐商品明细表，记录客户推荐商品及行为类型')
LIFECYCLE 100;