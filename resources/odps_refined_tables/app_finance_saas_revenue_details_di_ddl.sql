```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_finance_saas_revenue_details_di` (
  `tenant_id` BIGINT COMMENT '租户ID，唯一标识一个租户',
  `tenant_name` STRING COMMENT '租户名称，如：益禾堂、怡满分等',
  `company_name` STRING COMMENT '公司全称，如：益禾堂(湖北)餐饮管理有限公司',
  `order_no` STRING COMMENT '订单编号，唯一标识一个订单',
  `order_item_id` STRING COMMENT '订单项编号，唯一标识订单中的具体商品项',
  `store_id` BIGINT COMMENT '店铺ID，唯一标识一个门店',
  `store_name` STRING COMMENT '门店名称，如：吉首乾州步行街店',
  `store_type` STRING COMMENT '门店类型枚举：直营店、加盟店、托管店',
  `province` STRING COMMENT '省份名称，如：湖南、浙江、上海等',
  `city` STRING COMMENT '城市名称，如：湘西土家族苗族自治州、杭州市等',
  `area` STRING COMMENT '区县名称，如：吉首市、西湖区等',
  `pay_time` DATETIME COMMENT '支付时间，格式：年月日时分秒',
  `pay_type` STRING COMMENT '支付方式枚举：微信支付、账期、余额支付、支付宝支付',
  `delivery_time` DATETIME COMMENT '配送时间，格式：年月日时分秒',
  `finished_time` DATETIME COMMENT '确认收货时间，格式：年月日时分秒',
  `item_id` BIGINT COMMENT '商品item_id，唯一标识一个商品',
  `sku` STRING COMMENT '鲜沐SKU编码，商品库存单位唯一标识',
  `pd_name` STRING COMMENT '货品名称，如：薄荷叶、广东粗皮香水柠檬等',
  `category1` STRING COMMENT '商品一级类目枚举：鲜果、其他、乳制品',
  `tax_rate` DECIMAL(38,18) COMMENT '税率，小数表示，如0.09表示9%',
  `order_sku_cnt` BIGINT COMMENT '商城售卖数量，整数',
  `goods_supply_price` DECIMAL(38,18) COMMENT '货品采购单价（含税），单位：元',
  `goods_supply_total_price` DECIMAL(38,18) COMMENT '货品采购总价（含税），单位：元',
  `goods_delivery_fee` DECIMAL(38,18) COMMENT '供应商配送费，单位：元',
  `revenue_amt_notax` DECIMAL(38,18) COMMENT '确认收入金额（不含税），单位：元',
  `tax_amt` DECIMAL(38,18) COMMENT '税额，单位：元',
  `unit_cost` DECIMAL(38,18) COMMENT '成本单价（含税），单位：元',
  `unit_cost_notax` DECIMAL(38,18) COMMENT '成本单价（不含税），单位：元',
  `cost` DECIMAL(38,18) COMMENT '成本（含税），单位：元',
  `cost_notax` DECIMAL(38,18) COMMENT '成本（不含税），单位：元',
  `cust_group` STRING COMMENT '客户团队类型枚举：平台客户、大客户',
  `sub_type` STRING COMMENT '商品二级性质枚举：代销不入仓、代销入仓、经销',
  `store_no` BIGINT COMMENT '配送仓ID，唯一标识一个配送仓库',
  `warehouse_no` BIGINT COMMENT '库存仓ID，唯一标识一个库存仓库',
  `service_area` STRING COMMENT '大区枚举：华中、华东、西南、广西、福建、未知',
  `settle_type` STRING COMMENT '结算类型枚举：空字符串、成本结算'
)
COMMENT 'SaaS业财一体化收入明细表，包含订单、商品、财务结算等详细信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS业财一体化收入明细事实表，用于收入分析和财务对账',
  'lifecycle' = '30'
)
```