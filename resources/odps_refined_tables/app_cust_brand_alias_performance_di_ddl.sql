CREATE TABLE IF NOT EXISTS app_cust_brand_alias_performance_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`brand_alias` STRING COMMENT '品牌别名，枚举值包括：GIGI LUCKY舒芙蕾、GOODSLOVE CAFE、Keke可可同学订货商城、VQ、一只酸奶牛、一鸣、三出山、上海一点点、上海茶百道、乐乐茶等品牌名称',
	`order_origin_amt` DECIMAL(38,18) COMMENT '交易应付总金额，单位为元',
	`order_real_amt` DECIMAL(38,18) COMMENT '交易实付总金额，单位为元',
	`delivery_origin_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位为元',
	`delivery_real_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位为元',
	`delivery_cash_real_amt` DECIMAL(38,18) COMMENT '履约现结实付总金额，单位为元',
	`delivery_bill_real_amt` DECIMAL(38,18) COMMENT '履约账期实付总金额，单位为元',
	`delivery_cost_amt` DECIMAL(38,18) COMMENT '履约商品成本金额，单位为元',
	`delivery_real_gross` DECIMAL(38,18) COMMENT '履约实付毛利润，单位为元',
	`delivery_real_gross_rate` DECIMAL(38,18) COMMENT '履约实付毛利率，小数形式表示',
	`delivery_cust_cnt` BIGINT COMMENT '履约客户数，统计履约服务的客户数量',
	`delivery_point_cnt` BIGINT COMMENT '履约累计点位数，统计履约服务的点位数量',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，单位为元',
	`after_sale_rate` DECIMAL(38,18) COMMENT '售后比例，计算公式：已到货售后金额/履约实付GMV，小数形式表示'
) 
COMMENT '大客户品牌粒度监控表，按品牌维度统计交易、履约、售后等关键业务指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='大客户品牌粒度业务监控表，用于品牌维度的业务分析和监控') 
LIFECYCLE 30;