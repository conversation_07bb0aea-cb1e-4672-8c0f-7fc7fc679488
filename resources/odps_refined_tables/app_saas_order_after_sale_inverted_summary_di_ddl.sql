CREATE TABLE IF NOT EXISTS app_saas_order_after_sale_inverted_summary_di(
	`tenant_id` BIGINT COMMENT '租户ID，唯一标识一个租户',
	`time_tag` STRING COMMENT '时间标签，格式为yyyyMMdd，表示数据所属的业务日期',
	`after_sale_order_no` STRING COMMENT '售后单号，唯一标识一个售后订单',
	`order_no` STRING COMMENT '订单编号，唯一标识一个销售订单',
	`order_item_id` STRING COMMENT '订单项编号，标识订单中的具体商品项',
	`apply_time` DATETIME COMMENT '售后申请时间，格式为yyyy-MM-dd HH:mm:ss，精确到秒',
	`audit_time` DATETIME COMMENT '审核时间，格式为yyyy-MM-dd HH:mm:ss，精确到秒',
	`refund_total_amount` DECIMAL(38,18) COMMENT '退款总额，包含所有退款项目的总金额',
	`responsibility_type` BIGINT COMMENT '售后责任方类型：0-供应商，1-品牌方，2-门店',
	`brand_refundable_amout` DECIMAL(38,18) COMMENT '品牌应退金额，品牌方理论上应该退还的金额',
	`brand_actual_amount` DECIMAL(38,18) COMMENT '品牌实退金额，品牌方实际退还的金额',
	`supplier_refundable_amount` DECIMAL(38,18) COMMENT '供应商应退金额，供应商理论上应该退还的金额',
	`supplier_actual_amount` DECIMAL(38,18) COMMENT '供应商实退金额，供应商实际退还的金额',
	`inverted_amount` DECIMAL(38,18) COMMENT '品牌应付给供应商的差额，计算公式：供应商实退金额 - 供应商应退金额',
	`supplier_id` BIGINT COMMENT '供应商ID，唯一标识一个供应商'
) 
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据采集日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SAAS对账单-售后单倒挂明细表，记录售后订单的退款明细和责任方分配信息，用于财务对账和倒挂金额分析') 
LIFECYCLE 30;