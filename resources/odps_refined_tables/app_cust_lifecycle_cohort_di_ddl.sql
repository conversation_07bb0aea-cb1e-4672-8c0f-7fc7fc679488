CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_lifecycle_cohort_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示统计日期',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `register_city` STRING COMMENT '注册时市，客户注册时所在的城市名称',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举值：A1、A2、A3、B1、B2、L1、L2、L3、N0、N1、N2、S1、S2、W',
  `cust_cnt` BIGINT COMMENT '当日客户数，统计当日的客户数量',
  `cohort_cust_cnt_1` BIGINT COMMENT 't1下单人数，第1天（注册后第1天）的下单客户数',
  `cohort_cust_cnt_2` BIGINT COMMENT 't2下单人数，第2天（注册后第2天）的下单客户数',
  `cohort_cust_cnt_3` BIGINT COMMENT 't3下单人数，第3天（注册后第3天）的下单客户数',
  `cohort_cust_cnt_4` BIGINT COMMENT 't4下单人数，第4天（注册后第4天）的下单客户数',
  `cohort_cust_cnt_5` BIGINT COMMENT 't5下单人数，第5天（注册后第5天）的下单客户数',
  `cohort_cust_cnt_6` BIGINT COMMENT 't6下单人数，第6天（注册后第6天）的下单客户数',
  `cohort_cust_cnt_7` BIGINT COMMENT 't7下单人数，第7天（注册后第7天）的下单客户数',
  `cohort_cust_cnt_8` BIGINT COMMENT 't8下单人数，第8天（注册后第8天）的下单客户数',
  `cohort_cust_cnt_9` BIGINT COMMENT 't9下单人数，第9天（注册后第9天）的下单客户数',
  `cohort_cust_cnt_10` BIGINT COMMENT 't10下单人数，第10天（注册后第10天）的下单客户数',
  `cohort_cust_cnt_11` BIGINT COMMENT 't11下单人数，第11天（注册后第11天）的下单客户数',
  `cohort_cust_cnt_12` BIGINT COMMENT 't12下单人数，第12天（注册后第12天）的下单客户数',
  `cohort_cust_cnt_13` BIGINT COMMENT 't13下单人数，第13天（注册后第13天）的下单客户数',
  `cohort_cust_cnt_14` BIGINT COMMENT 't14下单人数，第14天（注册后第14天）的下单客户数',
  `cohort_cust_cnt_15` BIGINT COMMENT 't15下单人数，第15天（注册后第15天）的下单客户数',
  `cohort_cust_cnt_16` BIGINT COMMENT 't16下单人数，第16天（注册后第16天）的下单客户数',
  `cohort_cust_cnt_17` BIGINT COMMENT 't17下单人数，第17天（注册后第17天）的下单客户数',
  `cohort_cust_cnt_18` BIGINT COMMENT 't18下单人数，第18天（注册后第18天）的下单客户数',
  `cohort_cust_cnt_19` BIGINT COMMENT 't19下单人数，第19天（注册后第19天）的下单客户数',
  `cohort_cust_cnt_20` BIGINT COMMENT 't20下单人数，第20天（注册后第20天）的下单客户数',
  `cohort_cust_cnt_21` BIGINT COMMENT 't21下单人数，第21天（注册后第21天）的下单客户数',
  `cohort_cust_cnt_22` BIGINT COMMENT 't22下单人数，第22天（注册后第22天）的下单客户数',
  `cohort_cust_cnt_23` BIGINT COMMENT 't23下单人数，第23天（注册后第23天）的下单客户数',
  `cohort_cust_cnt_24` BIGINT COMMENT 't24下单人数，第24天（注册后第24天）的下单客户数',
  `cohort_cust_cnt_25` BIGINT COMMENT 't25下单人数，第25天（注册后第25天）的下单客户数',
  `cohort_cust_cnt_26` BIGINT COMMENT 't26下单人数，第26天（注册后第26天）的下单客户数',
  `cohort_cust_cnt_27` BIGINT COMMENT 't27下单人数，第27天（注册后第27天）的下单客户数',
  `cohort_cust_cnt_28` BIGINT COMMENT 't28下单人数，第28天（注册后第28天）的下单客户数',
  `cohort_cust_cnt_29` BIGINT COMMENT 't29下单人数，第29天（注册后第29天）的下单客户数',
  `cohort_cust_cnt_30` BIGINT COMMENT 't30下单人数，第30天（注册后第30天）的下单客户数',
  `cohort_cust_cnt_31` BIGINT COMMENT 't31下单人数，第31天（注册后第31天）的下单客户数',
  `cohort_real_amt_1` DECIMAL(38,18) COMMENT 't1实付金额，第1天（注册后第1天）的实付金额',
  `cohort_real_amt_2` DECIMAL(38,18) COMMENT 't2实付金额，第2天（注册后第2天）的实付金额',
  `cohort_real_amt_3` DECIMAL(38,18) COMMENT 't3实付金额，第3天（注册后第3天）的实付金额',
  `cohort_real_amt_4` DECIMAL(38,18) COMMENT 't4实付金额，第4天（注册后第4天）的实付金额',
  `cohort_real_amt_5` DECIMAL(38,18) COMMENT 't5实付金额，第5天（注册后第5天）的实付金额',
  `cohort_real_amt_6` DECIMAL(38,18) COMMENT 't6实付金额，第6天（注册后第6天）的实付金额',
  `cohort_real_amt_7` DECIMAL(38,18) COMMENT 't7实付金额，第7天（注册后第7天）的实付金额',
  `cohort_real_amt_8` DECIMAL(38,18) COMMENT 't8实付金额，第8天（注册后第8天）的实付金额',
  `cohort_real_amt_9` DECIMAL(38,18) COMMENT 't9实付金额，第9天（注册后第9天）的实付金额',
  `cohort_real_amt_10` DECIMAL(38,18) COMMENT 't10实付金额，第10天（注册后第10天）的实付金额',
  `cohort_real_amt_11` DECIMAL(38,18) COMMENT 't11实付金额，第11天（注册后第11天）的实付金额',
  `cohort_real_amt_12` DECIMAL(38,18) COMMENT 't12实付金额，第12天（注册后第12天）的实付金额',
  `cohort_real_amt_13` DECIMAL(38,18) COMMENT 't13实付金额，第13天（注册后第13天）的实付金额',
  `cohort_real_amt_14` DECIMAL(38,18) COMMENT 't14实付金额，第14天（注册后第14天）的实付金额',
  `cohort_real_amt_15` DECIMAL(38,18) COMMENT 't15实付金额，第15天（注册后第15天）的实付金额',
  `cohort_real_amt_16` DECIMAL(38,18) COMMENT 't16实付金额，第16天（注册后第16天）的实付金额',
  `cohort_real_amt_17` DECIMAL(38,18) COMMENT 't17实付金额，第17天（注册后第17天）的实付金额',
  `cohort_real_amt_18` DECIMAL(38,18) COMMENT 't18实付金额，第18天（注册后第18天）的实付金额',
  `cohort_real_amt_19` DECIMAL(38,18) COMMENT 't19实付金额，第19天（注册后第19天）的实付金额',
  `cohort_real_amt_20` DECIMAL(38,18) COMMENT 't20实付金额，第20天（注册后第20天）的实付金额',
  `cohort_real_amt_21` DECIMAL(38,18) COMMENT 't21实付金额，第21天（注册后第21天）的实付金额',
  `cohort_real_amt_22` DECIMAL(38,18) COMMENT 't22实付金额，第22天（注册后第22天）的实付金额',
  `cohort_real_amt_23` DECIMAL(38,18) COMMENT 't23实付金额，第23天（注册后第23天）的实付金额',
  `cohort_real_amt_24` DECIMAL(38,18) COMMENT 't24实付金额，第24天（注册后第24天）的实付金额',
  `cohort_real_amt_25` DECIMAL(38,18) COMMENT 't25实付金额，第25天（注册后第25天）的实付金额',
  `cohort_real_amt_26` DECIMAL(38,18) COMMENT 't26实付金额，第26天（注册后第26天）的实付金额',
  `cohort_real_amt_27` DECIMAL(38,18) COMMENT 't27实付金额，第27天（注册后第27天）的实付金额',
  `cohort_real_amt_28` DECIMAL(38,18) COMMENT 't28实付金额，第28天（注册后第28天）的实付金额',
  `cohort_real_amt_29` DECIMAL(38,18) COMMENT 't29实付金额，第29天（注册后第29天）的实付金额',
  `cohort_real_amt_30` DECIMAL(38,18) COMMENT 't30实付金额，第30天（注册后第30天）的实付金额',
  `cohort_real_amt_31` DECIMAL(38,18) COMMENT 't31实付金额，第31天（注册后第31天）的实付金额'
) 
COMMENT '客户生命周期cohort表，用于分析客户注册后31天内的下单行为和消费金额变化趋势'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='客户生命周期cohort分析表，按客户团队、注册城市、生命周期标签等多维度统计客户注册后31天内的下单转化和消费情况')
LIFECYCLE 30;