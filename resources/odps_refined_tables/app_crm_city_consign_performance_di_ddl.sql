CREATE TABLE IF NOT EXISTS app_crm_city_consign_performance_di(
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `administrative_city` STRING COMMENT '销售所属行政城市名称',
  `zone_name` STRING COMMENT '区域名称，如：浦东、江西、东莞等',
  `m1` STRING COMMENT '城市负责人（M1）姓名',
  `m2` STRING COMMENT '区域负责人（M2）姓名',
  `m3` STRING COMMENT '部门负责人（M3）姓名',
  `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、水果/果切/榨汁店、糖水/水果捞、蛋糕店、其他',
  `order_cust_cnt` BIGINT COMMENT '下单客户数',
  `order_sku_cnt` BIGINT COMMENT '销量（商品件数）',
  `order_cnt` BIGINT COMMENT '订单数',
  `real_total_amt` DECIMAL(38,18) COMMENT '订单实付金额',
  `drop_in_visit_cust_cnt` BIGINT COMMENT '上门拜访客户数（上门/有效）',
  `visit_cust_cnt` BIGINT COMMENT '总拜访客户数'
) 
COMMENT '行政城市粒度代售业绩报表日汇总表，按行政城市维度统计代售业务的各项业绩指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
   'comment'='行政城市粒度代售业绩报表日汇总表，包含各城市销售团队的业绩指标和拜访数据') 
LIFECYCLE 30;