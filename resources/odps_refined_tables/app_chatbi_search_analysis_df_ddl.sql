```sql
CREATE TABLE IF NOT EXISTS app_chatbi_search_analysis_df(
    cust_id STRING COMMENT '客户ID，唯一标识客户',
    cust_name STRING COMMENT '客户姓名或店铺名称',
    cust_type STRING COMMENT '客户类型，枚举值：其他、面包蛋糕、咖啡、甜品冰淇淋、茶饮、西餐',
    query_list STRING COMMENT '客户搜索词列表，以英文逗号分隔，末尾一定以逗号结尾',
    sku_viewed BIGINT COMMENT '客户总计搜索的SKU数量，统计客户所有搜索行为中涉及的SKU总数',
    sku_clicked BIGINT COMMENT '客户总计点击的SKU数量，统计客户所有点击行为中涉及的SKU总数',
    searched_date_range STRING COMMENT '客户有搜索行为的日期时间段，格式为yyyyMMdd~yyyyMMdd',
    last_searched_date STRING COMMENT '客户最后一次搜索日期，格式为yyyyMMdd，表示年月日'
)
COMMENT '客户的搜索分析汇总表，以客户ID作为维度，记录客户的搜索行为统计信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='客户的搜索分析汇总表，包含客户基本信息、搜索行为统计和搜索时间范围等数据')
LIFECYCLE 7;
```