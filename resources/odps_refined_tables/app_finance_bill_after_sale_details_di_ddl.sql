```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_finance_bill_after_sale_details_di` (
  `after_sale_order_id` STRING COMMENT '售后单编号，唯一标识每个售后订单',
  `order_no` STRING COMMENT '原始订单编号，关联到销售订单表',
  `order_item_id` BIGINT COMMENT '订单项编号，标识订单中的具体商品项',
  `delivery_path_id` BIGINT COMMENT '配送路径ID，关联配送路径信息表',
  `service_area` STRING COMMENT '大区：华东、西南、华中、福建、未知',
  `province` STRING COMMENT '省份：浙江、重庆、上海、山东、四川、福建、湖南、江苏、湖北、广东',
  `city` STRING COMMENT '城市：台州市、重庆市、嘉兴市、上海市、绍兴市、济南市、成都市、福州市、长沙市、湖州市、苏州市、杭州市、常州市、武汉市、东莞市、无锡市、常德市、温州市',
  `sku` STRING COMMENT '商品SKU编码，唯一标识商品',
  `pd_name` STRING COMMENT '商品名称，描述商品的具体名称',
  `category1` STRING COMMENT '商品一级类目：鲜果、乳制品、其他',
  `tax_rate` DECIMAL(38,18) COMMENT '税率，小数形式表示的商品税率',
  `finish_time` DATETIME COMMENT '售后完结时间，格式为年月日时分秒',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后退款金额（含税），包含税款的退款金额',
  `after_sale_amt_notax` DECIMAL(38,18) COMMENT '售后退款金额（不含税），扣除税款后的退款金额',
  `after_sale_in_sku_cnt` BIGINT COMMENT '售后入库数量，退回仓库的商品数量',
  `after_sale_in_cost` DECIMAL(38,18) COMMENT '售后入库成本金额（含税），退回商品的成本金额（含税）',
  `after_sale_in_cost_notax` DECIMAL(38,18) COMMENT '售后入库成本金额（不含税），退回商品的成本金额（不含税）',
  `after_sale_add_sku_cnt` BIGINT COMMENT '售后补发数量，需要补发给客户的商品数量',
  `after_sale_add_cost` DECIMAL(38,18) COMMENT '售后补发成本金额（含税），补发商品的成本金额（含税）',
  `after_sale_add_cost_notax` DECIMAL(38,18) COMMENT '售后补发成本金额（不含税），补发商品的成本金额（不含税）',
  `cust_team` STRING COMMENT '客户团队类型：平台客户、大客户',
  `handle_type` STRING COMMENT '售后服务类型：退货退款录入账单、退款录入账单、补发',
  `after_sale_add_lack_sku_cnt` BIGINT COMMENT '售后补发(缺货导致)商品数量，因缺货需要补发的商品数量',
  `after_sale_add_lack_revenue_amt` DECIMAL(38,18) COMMENT '售后补发(缺货导致)确认收入金额（含税），缺货补发产生的收入金额（含税）',
  `after_sale_add_lack_revenue_amt_notax` DECIMAL(38,18) COMMENT '售后补发(缺货导致)确认收入金额（不含税），缺货补发产生的收入金额（不含税）',
  `m_id` BIGINT COMMENT '客户ID，唯一标识客户',
  `mname` STRING COMMENT '客户名称，客户的店铺或公司名称',
  `realname` STRING COMMENT '品牌的工商注册名称，法定的工商登记名称',
  `name_remakes` STRING COMMENT '品牌的品牌名称，市场上使用的品牌标识',
  `agent_sale_flag` BIGINT COMMENT '代售商品标志：0-是代售商品，1-不是代售商品',
  `sub_type` BIGINT COMMENT '商品二级性质：1-自营-代销不入仓，2-自营-代销入仓，3-自营-经销，4-代仓-代仓',
  `settle_type` STRING COMMENT '结算方式，目前为空字符串'
) 
COMMENT '财务口径账期售后明细表，记录财务核算所需的售后相关数据，包括退款金额、成本、补发等信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '财务口径售后明细数据表，用于财务对账和成本核算',
  'lifecycle' = '30'
)
```