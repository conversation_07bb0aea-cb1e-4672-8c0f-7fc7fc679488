```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_service_area_trunk_cost_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` STRING COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
  `service_area` STRING COMMENT '服务区域，枚举值：华东、华南、华西、华中、福建、广西、华北',
  `end_place_no` BIGINT COMMENT '目的仓编号，数值型标识',
  `end_place_name` STRING COMMENT '目的仓名称，具体仓库地址描述',
  `path_name` STRING COMMENT '运输线路名称，描述完整的运输路径',
  `trunk_fixed_amt` DECIMAL(38,18) COMMENT '干线固资折旧费，单位：元',
  `self_amt` DECIMAL(38,18) COMMENT '自提费用，单位：元',
  `allocate_amt` DECIMAL(38,18) COMMENT '调拨费用，单位：元',
  `nodelivery_amt` DECIMAL(38,18) COMMENT '非履约费用，单位：元',
  `trunk_total_amt` DECIMAL(38,18) COMMENT '干线费用总额，单位：元',
  `self_trunk_amt` DECIMAL(38,18) COMMENT '自营干线费用，单位：元',
  `heytea_trunk_amt` DECIMAL(38,18) COMMENT '喜茶干线费用，单位：元',
  `self_trunk_km_cnt` DECIMAL(38,18) COMMENT '自营干线总公里数，单位：公里',
  `big_cust_total_amt` DECIMAL(38,18) COMMENT '大客户用车金额，单位：元'
) 
COMMENT '服务区域目的仓干线费用明细表，记录各服务区域到目的仓的干线运输费用明细数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '服务区域目的仓干线费用明细表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```