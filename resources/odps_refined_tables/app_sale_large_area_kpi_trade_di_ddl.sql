```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sale_large_area_kpi_trade_di` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd，表示年月日',
  `large_area_name` STRING COMMENT '运营服务大区，枚举值：重庆大区、成都大区、福州大区、广东一点点快递区域、长沙大区、广州大区、苏南大区、上海大区、苏州大区、昆明快递大区、武汉大区、昆明大区、南宁大区、贵阳大区、可可快递服务区、青岛大区、杭州大区',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元）',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元）',
  `order_cust_cnt` BIGINT COMMENT '交易客户数（个）',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（元/客户），计算公式：应付总金额/客户数',
  `order_cnt` BIGINT COMMENT '交易订单数（单）',
  `lose_cust_cnt` BIGINT COMMENT '交易流失客户数（个），定义：90天内活跃用户近60天未下单客户数',
  `lose_cust_ratio` DECIMAL(38,18) COMMENT '交易流失率（百分比），计算公式：流失客户数/总客户数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数（个）',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润（元）',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润（元）',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润（元）',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次（次/天），平均每日履约次数',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数（个）',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用（元）',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额（元）',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额（元）',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数（个）',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润（元）',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额（元）',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额（元）',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数（个）',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润（元）',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数（款）',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数（款）',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总表，按运营大区维度统计交易和履约相关核心指标，包括金额、客户数、订单数、利润等关键业务指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '销售大区KPI交易指标日汇总表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```