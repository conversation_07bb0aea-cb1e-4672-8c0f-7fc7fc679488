CREATE TABLE IF NOT EXISTS app_saas_product_movement_week_di(
	tenant_id BIGINT COMMENT '租户ID，唯一标识一个SaaS租户',
	time_tag STRING COMMENT '时间标签，格式为yyyyMMdd，表示每周一的日期',
	on_sale_num BIGINT COMMENT '在售商品数量，统计周期内处于销售状态的商品总数',
	pay_success_num BIGINT COMMENT '支付成功商品数量，统计周期内完成支付的商品数量',
	sale_rate DECIMAL(38,18) COMMENT '动销率，计算公式：支付成功商品数/在售商品数，表示商品销售效率',
	warehouse_type BIGINT COMMENT '仓库归属类型：0-自营商品，1-第三方商品',
	delivery_type BIGINT COMMENT '配送方式：0-品牌方配送，1-第三方配送',
	goods_type BIGINT COMMENT '商品类型：0-无货商品，1-报价货品，2-自营货品，3-其他类型（根据数据分布发现存在类型3）'
) 
COMMENT 'SaaS商品维度动销表（周维度），按周统计各租户商品的销售情况和动销指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS商品销售动销周统计表，包含商品销售数量、动销率等核心指标') 
LIFECYCLE 30;