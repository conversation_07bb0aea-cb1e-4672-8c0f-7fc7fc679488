```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_category_cust_monitor_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `cust_type` STRING COMMENT '客户类型，枚举值：ALL-全部客户/平台客户-平台客户/大客户-大客户/批发-批发客户',
  `category1` STRING COMMENT '一级类目，枚举值：ALL-全部类目/其他-其他类目/鲜果-鲜果类目/乳制品-乳制品类目',
  `category2` STRING COMMENT '二级类目，枚举值：ALL-全部类目/谷物制品-谷物制品/新鲜水果-新鲜水果/仓配-物资-仓配物资/糖丨糖制品-糖及糖制品/其他-其他类目/包材-包材/饮料-饮料/蛋丨蛋制品-蛋及蛋制品/水果制品-水果制品/蔬菜制品-蔬菜制品/糕点丨面包-糕点及面包/饮品原料-饮品原料/食品添加剂-食品添加剂/食用油丨油脂及制品-食用油及油脂制品/调味品-调味品/酒-酒类/饼干丨糖果丨可可豆制品-饼干糖果可可豆制品/测试类目(勿动)11-测试类目/坚果制品-坚果制品/无-无类目/权限测试类目2-权限测试类目/权限测试类目-权限测试类目/成品原料-成品原料/POP—鲜果-POP鲜果/新鲜蔬菜-新鲜蔬菜/鲜果礼盒-鲜果礼盒/测试一级类目T-测试类目/肉丨肉制品-肉及肉制品/茶制品-茶制品/海鲜｜水产品｜制品-海鲜水产品制品',
  `category3` STRING COMMENT '三级类目，包含ALL-全部类目/五谷杂粮-五谷杂粮/五谷杂粮米粉-五谷杂粮米粉/仁果类-仁果类/仓配耗材-仓配耗材等100+个枚举值',
  `category4` STRING COMMENT '四级类目，包含ALL-全部类目/糯米-糯米/糯米粉-糯米粉/大米粉丨粘米粉-大米粉粘米粉/苹果-苹果/梨-梨/枇杷-枇杷/山楂-山楂/人参果-人参果/仓配包材-仓配包材等300+个枚举值',
  `pv` BIGINT COMMENT '曝光PV，页面浏览量',
  `click_pv` BIGINT COMMENT '点击PV，用户点击次数',
  `add_pv` BIGINT COMMENT '加购PV，加入购物车次数',
  `buy_pv` BIGINT COMMENT '直接购买PV，直接购买次数',
  `pv_value` DECIMAL(38,18) COMMENT '千次曝光价值，每千次曝光产生的价值',
  `order_cnt` BIGINT COMMENT '订单数，订单数量',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV，交易应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV，交易实付总金额',
  `sku_cnt` BIGINT COMMENT '动销sku数，有销售记录的SKU数量',
  `on_sale_sku_cnt` BIGINT COMMENT '在架sku数，在售SKU数量',
  `sale_sku_rate` DECIMAL(38,18) COMMENT 'sku动销率，动销SKU数/在架SKU数',
  `spu_cnt` BIGINT COMMENT '动销spu数，有销售记录的SPU数量',
  `on_sale_spu_cnt` BIGINT COMMENT '在架spu数，在售SPU数量',
  `sale_spu_rate` DECIMAL(38,18) COMMENT 'spu动销率，动销SPU数/在架SPU数',
  `order_sku_cnt` BIGINT COMMENT '销量，销售商品数量',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易商品重量，交易商品总重量',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，履约应付总金额',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，履约实付总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本费用，总成本费用',
  `dlv_market_amt` DECIMAL(38,18) COMMENT '营销费用，履约营销费用',
  `dlv_market_roi` DECIMAL(38,18) COMMENT '营销费用roi，营销投入产出比',
  `gross_profit_amt` DECIMAL(38,18) COMMENT '实付毛利润，实付总金额的毛利润',
  `origin_profit_rate` DECIMAL(38,18) COMMENT '应付毛利率，应付金额的毛利率',
  `real_profit_rate` DECIMAL(38,18) COMMENT '实付毛利率，实付金额的毛利率',
  `dlv_sku_cnt` DECIMAL(38,18) COMMENT '履约数量，履约商品数量',
  `dlv_timing_origin_amt` DECIMAL(38,18) COMMENT '履约省心送应付GMV，省心送履约应付金额',
  `dlv_pet_origin_amt` DECIMAL(38,18) COMMENT '履约单件应付GMV，单件商品履约应付金额',
  `dlv_pet_real_amt` DECIMAL(38,18) COMMENT '履约单件实付GMV，单件商品履约实付金额',
  `dlv_pet_cost_amt` DECIMAL(38,18) COMMENT '履约单件成本，单件商品履约成本',
  `dlv_pet_profit_amt` DECIMAL(38,18) COMMENT '履约单件毛利润，单件商品履约毛利润',
  `dlv_sku_weight` DECIMAL(38,18) COMMENT '履约重量kg，履约商品总重量（千克）',
  `dlv_pet_weight_origin_amt` DECIMAL(38,18) COMMENT '履约单kg应付GMV，每千克履约应付金额',
  `dlv_pet_weight_real_amt` DECIMAL(38,18) COMMENT '履约单kg实付GMV，每千克履约实付金额',
  `dlv_pet_weight_cost_amt` DECIMAL(38,18) COMMENT '履约单kg成本，每千克履约成本',
  `dlv_pet_weight_profit_amt` DECIMAL(38,18) COMMENT '履约单kg毛利润，每千克履约毛利润',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后金额，未收货的售后金额',
  `after_sale_noreceived_proportion` DECIMAL(38,18) COMMENT '未到货售后金额占比，未到货售后金额占总金额比例',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后金额，已收货的售后金额',
  `after_sale_received_proportion` DECIMAL(38,18) COMMENT '已到货售后金额占比，已到货售后金额占总金额比例',
  `store_quantity` BIGINT COMMENT '可用库存，当前可用库存数量',
  `quality_amt_4_3` DECIMAL(38,18) COMMENT '3/4效期库存金额，剩余3/4保质期的库存金额',
  `quality_amt_2_1` DECIMAL(38,18) COMMENT '1/2效期库存金额，剩余1/2保质期的库存金额',
  `quality_amt_4_1` DECIMAL(38,18) COMMENT '1/4效期库存金额，剩余1/4保质期的库存金额',
  `quality_amt_under_4_1` DECIMAL(38,18) COMMENT '1/4效期以下库存金额，剩余保质期少于1/4的库存金额',
  `advent_amt` DECIMAL(38,18) COMMENT '临期库存金额，临近过期的库存金额',
  `turnover` DECIMAL(38,18) COMMENT '近三十天周转，近30天库存周转率',
  `purchase_in_cnt` BIGINT COMMENT '采购入库数量，采购入库商品数量',
  `purchase_in_amt` DECIMAL(38,18) COMMENT '采购入库金额，采购入库总金额',
  `damage_in_cnt` BIGINT COMMENT '货损出库数量，货损出库商品数量',
  `damage_in_amt` DECIMAL(38,18) COMMENT '货损出库金额，货损出库总金额',
  `dlv_origin_growth_amount` DECIMAL(38,18) COMMENT '履约应付增长额，履约应付金额增长额',
  `dlv_origin_growth_rate` DECIMAL(38,18) COMMENT '履约应付增长率，履约应付金额增长率',
  `dlv_origin_profit_growth_amount` DECIMAL(38,18) COMMENT '履约毛利润增长额，履约毛利润增长额',
  `dlv_real_profit_growth_rate` DECIMAL(38,18) COMMENT '履约毛利润增长率，履约毛利润增长率',
  `dlv_origin_growth_label` STRING COMMENT 'GMC增速标签，枚举值：低降幅-低幅度下降/稳定-稳定/高降幅-高幅度下降/低增幅-低幅度增长/高增幅-高幅度增长',
  `dlv_ofit_growth_label` STRING COMMENT '毛利润增速标签，枚举值：高降幅-高幅度下降/稳定-稳定/低降幅-低幅度下降/低增幅-低幅度增长/高增幅-高幅度增长',
  `dlv_origin_amt_label` BIGINT COMMENT '履约应付GMV类目排名（四级），在四级类目中的排名',
  `dlv_total_last_origin_rate` DECIMAL(38,18) COMMENT '履约应付GMV占比（四级）上级占比，在上级类目中的占比'
) 
COMMENT '类目监控周表，用于监控各类目客户每周的业务指标，包括曝光、点击、交易、履约、库存等数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，如20250922表示2025年9月22日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='类目监控周表，提供详细的类目维度业务监控数据') 
LIFECYCLE 30;
```