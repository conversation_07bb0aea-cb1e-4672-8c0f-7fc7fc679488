```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_recommend_transform_di` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd',
  `scene` STRING COMMENT '场景：首页、商品详情页、购物车页、分类页、活动页、特价专区',
  `cust_uv` BIGINT COMMENT '用户UV（独立访客数）',
  `cust_pv` BIGINT COMMENT '用户PV（页面浏览量）',
  `new_cust_uv` BIGINT COMMENT '新用户UV（新独立访客数）',
  `sku_impression_uv` BIGINT COMMENT '商品曝光UV（看到商品的独立用户数）',
  `sku_impression_pv` BIGINT COMMENT '商品曝光PV（商品被看到的次数）',
  `sku_click_uv` BIGINT COMMENT '商品点击UV（点击商品的独立用户数）',
  `sku_click_pv` BIGINT COMMENT '商品点击PV（商品被点击的次数）',
  `sku_cart_uv` BIGINT COMMENT '商品加购UV（加入购物车的独立用户数）',
  `sku_cart_pv` BIGINT COMMENT '商品加购PV（商品被加入购物车的次数）',
  `order_cnt` BIGINT COMMENT '下单数（订单数量）',
  `order_amt` DECIMAL(38,18) COMMENT '下单金额',
  `order_uv` BIGINT COMMENT '下单用户UV（下单的独立用户数）',
  `order_paid_cnt` BIGINT COMMENT '支付订单数',
  `order_paid_amt` DECIMAL(38,18) COMMENT '支付金额',
  `order_paid_uv` BIGINT COMMENT '支付订单用户UV（支付的独立用户数）',
  `order_paid_amt_avg` DECIMAL(38,18) COMMENT '平均支付金额（支付金额/支付订单数）',
  `sku_click_uv_rate` DECIMAL(38,18) COMMENT '商品UV点击率（商品点击UV/商品曝光UV）',
  `sku_click_pv_rate` DECIMAL(38,18) COMMENT '商品PV点击率（商品点击PV/商品曝光PV）',
  `sku_cart_uv_rate` DECIMAL(38,18) COMMENT '商品UV加购率（商品加购UV/商品曝光UV）',
  `sku_cart_pv_rate` DECIMAL(38,18) COMMENT '商品PV加购率（商品加购PV/商品曝光PV）',
  `order_paid_uv_rate` DECIMAL(38,18) COMMENT '订单支付UV转化率（支付用户UV/总UV）',
  `abexperiments_experiment_id` STRING COMMENT 'AB实验解析字段-experiment_id，枚举值：无',
  `abexperiments_experiment_place` STRING COMMENT 'AB实验解析字段-experiment_place，枚举值：无',
  `abexperiments_variant_id` STRING COMMENT 'AB实验解析字段-variant_id，枚举值：无'
) 
COMMENT '商城推荐转化汇总表，统计各场景下的推荐转化指标，包括曝光、点击、加购、下单、支付等关键转化数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '商城推荐转化汇总表，用于分析推荐系统的转化效果',
  'columnar.nested.type' = 'true'
) 
LIFECYCLE 30;
```