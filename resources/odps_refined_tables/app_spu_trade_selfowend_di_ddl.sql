```sql
CREATE TABLE IF NOT EXISTS app_spu_trade_selfowend_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `register_province` STRING COMMENT '注册省，客户注册地址所在的省份',
    `register_city` STRING COMMENT '注册市，客户注册地址所在的城市',
    `register_area` STRING COMMENT '注册区，客户注册地址所在的区县',
    `spu_id` STRING COMMENT 'SPU ID，商品标准单元的唯一标识',
    `spu_name` STRING COMMENT 'SPU名称，商品标准单元的名称',
    `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他、水果/果切/榨汁店',
    `brand_type` STRING COMMENT '大客户类型；枚举值：普通、大客户、批发客户；（注：单店、批发大客户、普通大客户、KA大客户 已弃用）',
    `brand_name` STRING COMMENT '品牌名称，商品所属的品牌',
    `category1` STRING COMMENT '一级类目，商品最高层级的分类',
    `category2_id` STRING COMMENT '二级类目ID，二级分类的唯一标识',
    `category2` STRING COMMENT '二级类目，商品的二级分类名称',
    `category3_id` STRING COMMENT '三级类目ID，三级分类的唯一标识',
    `category3` STRING COMMENT '三级类目，商品的三级分类名称',
    `category4_id` STRING COMMENT '四级类目ID，四级分类的唯一标识',
    `category4` STRING COMMENT '四级类目，商品的四级分类名称',
    `origin_total_amt` DECIMAL(38,18) COMMENT '实付总金额，客户实际支付的总金额，单位为元',
    `real_total_amt` DECIMAL(38,18) COMMENT '应付总金额，订单应付的总金额，单位为元',
    `cust_cnt` BIGINT COMMENT '客户数，当日下单的客户数量',
    `new_cust_cnt` BIGINT COMMENT '新客户数，历史截止当天首次下单的新客户数量',
    `order_time_cnt` DECIMAL(38,18) COMMENT '客户下单时间间隔之和，所有客户下单时间间隔的累计值，单位为分钟',
    `order_time_avg` DECIMAL(38,18) COMMENT '平均下单时间间隔，客户下单时间间隔的平均值，单位为分钟'
)
COMMENT '城市整体交易数据日表，按城市维度统计的商品交易数据，包含客户信息、商品信息、交易金额和客户行为指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的日期分区'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '城市整体交易数据日表',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```