```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_financial_accounts_order_item_di`(
  `tenant_id` BIGINT COMMENT '租户ID，唯一标识一个租户',
  `brand_id` BIGINT COMMENT '品牌ID，唯一标识一个品牌',
  `type` BIGINT COMMENT '费用类型：0-订单明细；1-售后单明细',
  `order_no` STRING COMMENT '订单编号，唯一标识一个订单',
  `order_item_id` BIGINT COMMENT '订单明细ID，唯一标识订单中的一个商品项',
  `order_time` DATETIME COMMENT '下单时间，格式为年月日时分秒',
  `after_sale_order_no` STRING COMMENT '售后单编号，唯一标识一个售后单',
  `store_id` BIGINT COMMENT '门店ID，唯一标识一个门店',
  `store_name` STRING COMMENT '门店名称',
  `delivery_address` STRING COMMENT '商品配送地址',
  `sku` STRING COMMENT '商品SKU编码，库存管理单位',
  `pd_id` BIGINT COMMENT '商品ID，唯一标识一个商品',
  `pd_name` STRING COMMENT '商品名称',
  `weight` STRING COMMENT '商品规格信息，包括包装规格、等级、重量等',
  `sub_type` BIGINT COMMENT '商品二级性质：1-自营代销不入仓；2-自营代销入仓；3-自营经销；4-代仓代仓',
  `order_type` STRING COMMENT '订单类型/售后单类型，取值范围：已到货售后、账期',
  `handle_type` STRING COMMENT '售后服务原因，取值范围：退款',
  `remark` STRING COMMENT '订单备注/售后原因，取值范围：商品品质问题、空字符串',
  `finish_time` DATETIME COMMENT '履约/售后完成时间，格式为年月日时分秒',
  `quantity` BIGINT COMMENT '下单个数/售后数量',
  `payable_amount` DECIMAL(38,18) COMMENT '应付单价，保留18位小数',
  `actually_paid_amount` DECIMAL(38,18) COMMENT '实付单价，保留18位小数',
  `total_actually_paid_amount` DECIMAL(38,18) COMMENT '实付/售后总价，保留18位小数',
  `delivery_fee` DECIMAL(38,18) COMMENT '运费金额，保留18位小数',
  `item_unit` STRING COMMENT '售后单位，取值范围：个、g、件、盒、箱、包、袋、瓶、组、块'
)
COMMENT '账期订单费用项明细表，记录订单和售后单的费用明细信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '账期订单费用项明细表，包含订单和售后单的费用明细、商品信息、门店信息等',
  'lifecycle' = '30'
);
```