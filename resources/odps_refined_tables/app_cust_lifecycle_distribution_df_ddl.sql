```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_lifecycle_distribution_df` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举值：A1、A2、A3、B1、B2、L1、L2、L3、N0、N1、N2、S1、S2、W',
  `cust_cnt` BIGINT COMMENT '总客户数',
  `disbaled_private_cust_cnt` BIGINT COMMENT '锁定客户数',
  `private_cust_cnt` BIGINT COMMENT '分配客户数',
  `bd_cnt` BIGINT COMMENT '跟进bd数',
  `order_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天下单实付金额',
  `order_real_amt_60d` DECIMAL(38,18) COMMENT '最近60天下单实付金额',
  `order_real_amt_365d` DECIMAL(38,18) COMMENT '最近365天下单实付金额',
  `bd_order_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天分配bd客户下单实付金额',
  `bd_order_real_amt_60d` DECIMAL(38,18) COMMENT '最近60天分配bd客户下单实付金额',
  `bd_order_real_amt_365d` DECIMAL(38,18) COMMENT '最近365天分配bd客户下单实付金额',
  `login_cust_cnt` BIGINT COMMENT '近30日登录总客户数',
  `login_disbaled_private_cust_cnt` BIGINT COMMENT '近30日登录锁定客户数',
  `login_private_cust_cnt` BIGINT COMMENT '近30日登录分配客户数',
  `login_bd_cnt` BIGINT COMMENT '近30日登录跟进bd数',
  `login_order_real_amt_30d` DECIMAL(38,18) COMMENT '近30日登录最近30天下单实付金额',
  `login_order_real_amt_60d` DECIMAL(38,18) COMMENT '近30日登录最近60天下单实付金额',
  `login_order_real_amt_365d` DECIMAL(38,18) COMMENT '近30日登录最近365天下单实付金额',
  `login_bd_order_real_amt_30d` DECIMAL(38,18) COMMENT '近30日登录最近30天分配bd客户下单实付金额',
  `login_bd_order_real_amt_60d` DECIMAL(38,18) COMMENT '近30日登录最近60天分配bd客户下单实付金额',
  `login_bd_order_real_amt_365d` DECIMAL(38,18) COMMENT '近30日登录最近365天分配bd客户下单实付金额',
  `login_value_cust_cnt` BIGINT COMMENT '近30日登录价值高总客户数',
  `login_value_disbaled_private_cust_cnt` BIGINT COMMENT '近30日登录价值高锁定客户数',
  `login_value_private_cust_cnt` BIGINT COMMENT '近30日登录价值高分配客户数',
  `login_value_bd_cnt` BIGINT COMMENT '近30日登录价值高跟进bd数',
  `login_value_order_real_amt_30d` DECIMAL(38,18) COMMENT '近30日登录价值高最近30天下单实付金额',
  `login_value_order_real_amt_60d` DECIMAL(38,18) COMMENT '近30日登录价值高最近60天下单实付金额',
  `login_value_order_real_amt_365d` DECIMAL(38,18) COMMENT '近30日登录价值高最近365天下单实付金额',
  `login_value_bd_order_real_amt_30d` DECIMAL(38,18) COMMENT '近30日登录价值高最近30天分配bd客户下单实付金额',
  `login_value_bd_order_real_amt_60d` DECIMAL(38,18) COMMENT '近30日登录价值高最近60天分配bd客户下单实付金额',
  `login_value_bd_order_real_amt_365d` DECIMAL(38,18) COMMENT '近30日登录价值高最近365天分配bd客户下单实付金额'
) 
COMMENT '客户分配指标汇总表，按客户生命周期标签统计各类客户数量、订单金额等分配相关指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='客户分配指标汇总表，按客户生命周期标签统计各类客户数量、订单金额等分配相关指标') 
LIFECYCLE 30;
```