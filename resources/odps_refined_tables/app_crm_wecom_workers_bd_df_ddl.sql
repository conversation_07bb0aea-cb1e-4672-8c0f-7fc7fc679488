CREATE TABLE IF NOT EXISTS app_crm_wecom_workers_bd_df(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
	`bd_id` BIGINT COMMENT '销售ID，唯一标识一个销售人员',
	`bd_name` STRING COMMENT '销售姓名',
	`m1_name` STRING COMMENT '城市负责人名称（M1管理者）',
	`m2_name` STRING COMMENT '区域负责人名称（M2管理者）',
	`m3_name` STRING COMMENT '部门负责人名称（M3管理者）',
	`region` STRING COMMENT '大区名称，取值范围：华南一区、上海大区、山东大区、川渝大区、苏皖大区、浙江大区、华中大区、云贵桂大区、华南二区、福泉、四川、虚拟区域',
	`job_state` STRING COMMENT '是否在职，取值范围：是-在职，否-离职',
	`wecom_state` STRING COMMENT '企微状态，取值范围：已激活-已激活企业微信，未激活-未激活企业微信，退出企业-已退出企业'
) 
COMMENT '销售企微激活状态看板，记录销售人员的企微激活状态和在职状态'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='销售企微激活状态看板表') 
LIFECYCLE 30;