```sql
CREATE TABLE IF NOT EXISTS app_search_frontcat_mini_rank_df(
    category_cust_type STRING COMMENT '前台一级类目-客户业态，格式为"类目ID-业态名称"，如"103-咖啡"、"105-茶饮"等，取值范围包括：103-其他、103-咖啡、103-水果/果切/榨汁店、103-甜品冰淇淋、103-茶饮、103-西餐、103-西餐披萨、103-面包蛋糕、105-其他、105-咖啡、105-水果/果切/榨汁店、105-甜品冰淇淋、105-茶饮、105-西餐、105-面包蛋糕、107-其他、107-咖啡、107-水果/果切/榨汁店、107-甜品冰淇淋、107-糖水/水果捞、107-茶饮、107-西餐、107-西餐披萨、107-面包蛋糕、115-其他、115-咖啡、115-水果/果切/榨汁店、115-甜品冰淇淋、115-茶饮、115-西餐、115-面包蛋糕、122-其他、122-咖啡、122-水果/果切/榨汁店、122-甜品冰淇淋、122-茶饮、122-西餐、122-面包蛋糕、146-其他、146-咖啡、146-甜品冰淇淋、146-茶饮、146-面包蛋糕、160-其他、160-咖啡、160-水果/果切/榨汁店、160-甜品冰淇淋、160-茶饮、160-西餐、160-面包蛋糕等',
    sku_ids STRING COMMENT 'SKU列表，多个SKU ID用英文逗号分隔，如"N001S01R005,N001H01Y003,N001S01R002"'
)
COMMENT '搜索结果迷你榜单，展示不同前台类目-客户业态组合下的推荐SKU列表'
PARTITIONED BY (ds STRING COMMENT '分区字段，数据日期，格式为yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='搜索结果迷你榜单表，包含前台类目-客户业态维度和对应的推荐SKU列表')
LIFECYCLE 60;
```