```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_search_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据发生的年月日',
  `search_name` STRING COMMENT '搜索词，用户输入的搜索关键词',
  `search_cnt` BIGINT COMMENT '搜索次数，统计周期内的搜索总次数',
  `search_cust_cnt` BIGINT COMMENT '搜索客户数，去重后的搜索用户数量'
)
COMMENT '搜索行为日志数据表，记录用户的搜索关键词、搜索频次和用户覆盖情况'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据采集的年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '搜索行为明细数据表，用于分析用户搜索偏好和搜索热度趋势',
  'lifecycle' = '30'
);
```