```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_trade_detail_di`(
    `date` STRING COMMENT '交易日期，格式：yyyyMMdd',
    `sku_type` STRING COMMENT '商品类型，枚举值：鲜沐自营-鲜沐平台自营商品，鲜沐代仓-鲜沐平台代仓商品，品牌方自营-品牌方自营商品',
    `store_type` STRING COMMENT '门店类型，枚举值：直营店-品牌直营门店，加盟店-品牌加盟门店，托管店-品牌托管门店',
    `brand_type` STRING COMMENT '品牌类型，枚举值：大客户-大型品牌客户',
    `settlement_type` STRING COMMENT '结算方式，枚举值：现结-即时结算，账期-账期结算',
    `total_gmv` DECIMAL(38,18) COMMENT '总交易GMV（商品交易总额）',
    `xianmu_sku_gmv` DECIMAL(38,18) COMMENT '鲜沐商品交易GMV（鲜沐平台商品的交易总额）',
    `xianmu_sku_income_gmv` DECIMAL(38,18) COMMENT '鲜沐商品品牌加价收入（鲜沐平台商品的品牌加价收入）',
    `after_sale_nonarrival_sku_cnt` BIGINT COMMENT '售后商品数量（未到货），统计未到货的售后商品数量',
    `after_sale_nonarrival_amt` DECIMAL(38,18) COMMENT '售后金额（未到货），统计未到货的售后金额',
    `after_sale_arrival_sku_cnt` BIGINT COMMENT '售后商品数量（已到货），统计已到货的售后商品数量',
    `after_sale_arrival_amt` DECIMAL(38,18) COMMENT '售后金额（已到货），统计已到货的售后金额'
)
COMMENT 'SaaS交易数据日汇总表，包含每日交易GMV、售后数据等核心业务指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SaaS平台交易明细日汇总表，用于分析交易趋势和售后情况',
    'last_data_modified_time' = '2025-09-23 02:47:11'
)
LIFECYCLE 30;
```