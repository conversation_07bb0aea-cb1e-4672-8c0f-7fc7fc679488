```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_purchase_order_time_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `is_purchase` STRING COMMENT '根据下单前行为划分：a未进入-下单前15分钟未点击采购助手的商品；b进入-下单前15分钟点击采购助手的商品，含常购商品和榜单推荐商品',
  `type` STRING COMMENT '实验分组类型：V3-V3版本实验组；V4-V4版本实验组；对照组-对照组',
  `order_cnt` BIGINT COMMENT '下单数量',
  `order_interval_minute` DECIMAL(38,18) COMMENT '下单间隔时长均值，单位：分钟',
  `order_interval_minute_75` DECIMAL(38,18) COMMENT '下单间隔时长75分位值，单位：分钟',
  `order_interval_minute_mid` DECIMAL(38,18) COMMENT '下单间隔时长中位值，单位：分钟',
  `order_interval_minute_25` DECIMAL(38,18) COMMENT '下单间隔时长25分位值，单位：分钟'
) 
COMMENT '下单时长效率分析表，用于分析不同实验分组下用户下单行为的时长效率指标'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期分区'
)
STORED AS ALIORC  
TBLPROPERTIES (
  'comment'='下单时长效率分析表，包含不同实验分组的下单数量和各种下单间隔时长统计指标',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```