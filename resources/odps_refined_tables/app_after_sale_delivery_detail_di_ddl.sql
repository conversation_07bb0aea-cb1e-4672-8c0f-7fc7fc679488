```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_after_sale_delivery_detail_di` (
  `date` STRING COMMENT '业务日期，格式：yyyyMMdd',
  `after_sale_order_id` STRING COMMENT '售后单号，即after_sale_order_no',
  `apply_time` DATETIME COMMENT '售后申请时间，格式：yyyy-MM-dd HH:mm:ss',
  `finish_time` DATETIME COMMENT '审核完成时间，格式：yyyy-MM-dd HH:mm:ss',
  `after_sale_sku_cnt` BIGINT COMMENT '售后商品数量',
  `after_sale_unit` STRING COMMENT '售后单位，枚举值：g-克、件、盒、包、个、桶、瓶、罐、袋',
  `after_sale_origin_amt` DECIMAL(38,18) COMMENT '售后应退金额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后实退金额',
  `after_sale_type` STRING COMMENT '售后分类，枚举值：质量-腐烂/发霉/变质/黑斑、质量-结块/无法打发、仓配物流-包装破损、仓配物流-少称、质量-标品发霉/变质、错拍、质量-有籽、质量-花皮、质量-过熟、仓配物流-开裂/压伤、质量-过黄/过老/不新鲜、质量-过生、特殊处理(客户体验)、质量-其他(口感问题)、质量-单果重量不足、仓配物流-缺货、仓配物流-漏发、其他、仓配物流-错发、退运费、保质期-过期/临期、仓配物流-失温(结块,打发)',
  `after_sale_order_type` STRING COMMENT '售后订单类型，枚举值：普通售后、极速售后',
  `reason` STRING COMMENT '售后原因，枚举值：商品品质问题、包装问题、商品数量不符、其他、保质期不符、平台发错货',
  `handle_type` STRING COMMENT '处理方式，枚举值：返券、退货退款、退款、拒收退款、录入账单、退货录入账单、换货、补发',
  `after_sale_remark` STRING COMMENT '发起备注，用户填写的售后备注信息',
  `review_remark` STRING COMMENT '客服备注，客服填写的处理备注',
  `detail_review_remark` STRING COMMENT '审核备注，详细审核意见和说明',
  `reviewer` STRING COMMENT '审核人，处理售后审核的人员姓名',
  `approver` STRING COMMENT '审批人，最终审批售后申请的人员姓名',
  `order_no` STRING COMMENT '原始订单号',
  `order_type` STRING COMMENT '订单类型，枚举值：省心送、其他',
  `order_date` STRING COMMENT '下单日期，格式：yyyyMMdd',
  `real_unit_amt` DECIMAL(38,18) COMMENT '配送实付单价',
  `origin_unit_amt` DECIMAL(38,18) COMMENT '配送应付单价',
  `sku_cnt` BIGINT COMMENT '配送商品数量',
  `deliver_time` DATETIME COMMENT '配送日期，格式：yyyy-MM-dd 00:00:00',
  `store_no` BIGINT COMMENT '配送仓编号',
  `store_name` STRING COMMENT '配送仓名称',
  `warehouse_no` BIGINT COMMENT '库存仓编号',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `path` STRING COMMENT '配送线路代码',
  `driver` STRING COMMENT '司机姓名',
  `sku_id` STRING COMMENT '商品SKU编码',
  `spu_id` BIGINT COMMENT '商品SPU ID，即pd_id',
  `spu_no` STRING COMMENT 'SPU编号',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT 'SKU描述，即weight重量规格信息',
  `sku_type` STRING COMMENT '商品类型，枚举值：自营、代仓、代售',
  `sku_brand` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级分类，枚举值：鲜果、乳制品、其他',
  `category2` STRING COMMENT '二级分类，枚举值：新鲜水果、乳制品、糕点丨面包、饼干丨糖果丨可可豆制品、食用油丨油脂及制品、新鲜蔬菜、蛋丨蛋制品、谷物制品、饮料、水果制品、肉丨肉制品、糖丨糖制品、成品原料、蔬菜制品、包材、饮品原料',
  `category3` STRING COMMENT '三级分类，枚举值：柑果类、浆果类、液体乳、冷冻面团、核果类、饼干、瓠果类、食用油脂制品、绿叶菜类、蛋类、奶酪丨芝士、瓜菜类、面粉丨小麦粉、其他新鲜水果、果蔬汁、冷冻水果、炼乳、冷加工糕点、稀奶油、混合肉类、水果风味制品、砂糖、可可豆制品、粉圆类配料、冷冻蔬菜、保温袋、黄油、果汁原料',
  `category4` STRING COMMENT '四级分类，枚举值：橙、草莓、常温牛奶、生制冷冻面团、熟制冷冻面团、芒果、蓝莓、装饰饼干、牛油果、西瓜、柠檬、植脂奶油、柿子、芭乐、香蕉、葡萄、薄荷、新鲜蛋类、马斯卡彭、南瓜、提子、鲜牛奶、低筋面粉、柚、石榴、无花果、凤梨丨菠萝、火龙果、水果番茄丨圣女果｜西红柿、果汁、蜜瓜、橘子、冷冻果肉、罐装炼乳、熟粉类糕点、搅打型稀奶油、桃子、树莓、椰子、热加工熟混合肉制品、高筋面粉、水果类馅料、木瓜、金桔、白砂糖、甘蓝、冻鸡蛋黄、巧克力、芋圆',
  `cust_id` BIGINT COMMENT '售后用户ID，即客户ID',
  `cust_name` STRING COMMENT '客户名称',
  `cust_type` STRING COMMENT '客户类型，枚举值：大客户、普通',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `brand_id` BIGINT COMMENT '品牌ID（原大客户），-1表示无品牌',
  `brand_name` STRING COMMENT '品牌的企业名称',
  `brand_alias` STRING COMMENT '品牌的品牌名称',
  `city_id` BIGINT COMMENT '运营服务ID',
  `city_name` STRING COMMENT '运营服务名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID',
  `large_area_name` STRING COMMENT '运营服务大区名称，枚举值：苏州大区、杭州大区、上海大区、广州大区、长沙大区、成都大区、武汉大区、重庆大区、苏南大区、福州大区、青岛大区、贵阳大区、南宁大区',
  `province` STRING COMMENT '注册时省份',
  `city` STRING COMMENT '注册时城市',
  `area` STRING COMMENT '注册时区域',
  `apply_remark` STRING COMMENT '用户备注，用户申请时的额外备注信息',
  `store_method` STRING COMMENT '储存区域，枚举值：冷藏、常温、冷冻',
  `is_out_time` STRING COMMENT '是否超时售后，枚举值：是、否',
  `purchase_no` STRING COMMENT '采购批次号',
  `supplier` STRING COMMENT '批次供应商名称',
  `only_code` STRING COMMENT '唯一码，商品唯一标识码',
  `failed_rate` DECIMAL(38,18) COMMENT '不合格率',
  `damage_rate` DECIMAL(38,18) COMMENT '货损率',
  `question_desc` STRING COMMENT '货检评价，枚举值：合格入库、碰压伤、黑心/黑丝、烂底/烂心/烂根、虫害（虫卵/虫眼）、内质过生/过熟、糖度不足、不新鲜、发霉/腐烂、花皮',
  `question_type` STRING COMMENT '货检描述，具体质量问题描述',
  `receipt_method` STRING COMMENT '收货方式，枚举值：正常入库、特批入库、挑选入库、拒收、换货入库',
  `origin_purchase_no` STRING COMMENT '原采购单号',
  `min_stock_time` DATETIME COMMENT '首次入库日期，格式：yyyy-MM-dd HH:mm:ss',
  `quality_day` BIGINT COMMENT '保质期天数',
  `in_stock_day` BIGINT COMMENT '在库天数',
  `origin_sku` STRING COMMENT '原SKU编码',
  `warn_day` BIGINT COMMENT '到期预警天数',
  `shelf_life` BIGINT COMMENT '保鲜天数（鲜果）'
) 
COMMENT '已到货售后配送明细数据表，包含售后订单的配送详情、商品信息、客户信息、质检信息等完整数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='已到货售后配送明细数据表，记录售后订单的完整配送流程和商品质量信息',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```