CREATE TABLE IF NOT EXISTS app_saas_merchant_store_order_analysis_df(
	tenant_id BIGINT COMMENT '租户ID',
	type BIGINT COMMENT '统计周期类型：1-周，2-月，3-季度',
	time_tag STRING COMMENT '时间标签，格式为yyyyMMdd，表示统计周期的起始日期',
	store_id BIGINT COMMENT '门店ID',
	average_order_period DECIMAL(38,18) COMMENT '平均订货周期（天）',
	average_order_period_last_period DECIMAL(38,18) COMMENT '上周期平均订货周期（天）',
	average_order_period_upper_period DECIMAL(38,18) COMMENT '平均订货周期环比变化率（百分比）',
	order_amount BIGINT COMMENT '订货数量',
	order_amount_last_period BIGINT COMMENT '上周期订货数量',
	order_amount_upper_period DECIMAL(38,18) COMMENT '订货数量环比变化率（百分比）',
	order_price DECIMAL(38,18) COMMENT '订货金额（元）',
	order_price_last_period DECIMAL(38,18) COMMENT '上周期订货金额（元）',
	order_price_upper_period DECIMAL(38,18) COMMENT '订货金额环比变化率（百分比）',
	last_order_time STRING COMMENT '最后订货日期，格式为yyyy-MM-dd',
	last_order_amount BIGINT COMMENT '最后订货数量',
	last_order_price DECIMAL(38,18) COMMENT '最后订货金额（元）'
) 
COMMENT 'SaaS门店订货分析表，包含门店订货周期、数量、金额等指标的统计分析，支持周、月、季度不同维度的分析'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true') 
LIFECYCLE 30;