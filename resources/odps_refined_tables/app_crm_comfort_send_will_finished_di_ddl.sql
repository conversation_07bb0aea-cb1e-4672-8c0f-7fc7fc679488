```sql
CREATE TABLE IF NOT EXISTS app_crm_comfort_send_will_finished_di(
    m_id BIGINT COMMENT '商户ID，唯一标识商户',
    order_no STRING COMMENT '订单编号，唯一标识订单',
    order_time DATETIME COMMENT '订单生成时间，格式：年月日时分秒',
    sku_id STRING COMMENT '商品SKU编码，唯一标识商品规格',
    pd_name STRING COMMENT '商品名称',
    pd_weight STRING COMMENT '商品规格描述，如：950mL*12盒',
    pd_amount BIGINT COMMENT '订单内商品数量，取值范围：1-80',
    pay_amount DECIMAL(38,18) COMMENT '实付总额，精确到小数点后18位',
    area_no BIGINT COMMENT '商户所在运营区域编码，取值范围：1001-44207',
    bd_id BIGINT COMMENT '归属BD ID，公海为0，取值范围：0-1180780',
    day_tag STRING COMMENT '数据所在日标记，格式：yyyyMMdd',
    m_name STRING COMMENT '商户名称',
    province STRING COMMENT '省份名称，枚举值：上海、浙江、湖北、贵州、广东、四川、江苏、福建、安徽、湖南、江西、山东',
    city STRING COMMENT '城市名称',
    area STRING COMMENT '区县名称'
)
COMMENT 'CRM即将配送完成的省心送订单表（未配送数量<=3个），记录即将完成配送的省心送订单信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='CRM即将配送完成的省心送订单表，包含商户信息、订单信息、商品信息和地理信息')
LIFECYCLE 30;
```