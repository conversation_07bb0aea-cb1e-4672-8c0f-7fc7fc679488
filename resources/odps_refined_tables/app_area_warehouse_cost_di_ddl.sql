CREATE TABLE IF NOT EXISTS app_area_warehouse_cost_di(
	service_area STRING COMMENT '服务区域：华东、广西、贵阳、华南、福建、昆明、华北、华西、华中',
	warehosue_no BIGINT COMMENT '仓库编号，唯一标识每个仓库',
	warehosue_name STRING COMMENT '仓库名称：苏州总仓、南宁总仓、贵阳总仓、东莞冷冻总仓、福州总仓、南京总仓、昆明总仓、东莞总仓、青岛总仓、华西总仓、武汉总仓、嘉兴海盐总仓、嘉兴总仓、长沙总仓、嘉兴水果批发总仓、重庆总仓、上海总仓',
	in_quality BIGINT COMMENT '入库件数，统计周期内入库的商品数量',
	out_quality BIGINT COMMENT '出库件数，统计周期内出库的商品数量',
	on_quality BIGINT COMMENT '在库件数，统计时点在库的商品数量',
	storage_amt DECIMAL(38,18) COMMENT '仓储成本，仓库运营产生的存储费用',
	delivery_amt DECIMAL(38,18) COMMENT '配送GMV，通过该仓库配送产生的商品交易总额'
)
COMMENT '库存仓维度仓储成本表，按区域和仓库统计仓储运营成本和配送GMV数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='库存仓维度仓储成本表，用于分析各区域仓库的运营效率和成本结构')
LIFECYCLE 30;