```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_mall_banner_di` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `cust_type` STRING COMMENT '客户行业类型，枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他、水果/果切/榨汁店',
  `life_cycle` STRING COMMENT '生命周期标签（粗），枚举值：稳定期、导入期、准流失期、已流失期、成长期、新人期、沉默期、适应期',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举值：S1、N0、L2、L3、A1、A2、A3、B1、B2、N1、N2、L1、S2、W',
  `register_province` STRING COMMENT '注册时省份',
  `register_city` STRING COMMENT '注册时城市',
  `register_area` STRING COMMENT '注册时区域',
  `city_id` BIGINT COMMENT '运营服务区ID',
  `city_name` STRING COMMENT '运营服务区名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID',
  `large_area_name` STRING COMMENT '运营服务大区名称，枚举值：杭州大区、广州大区、上海大区、成都大区、青岛大区、苏州大区、苏南大区、长沙大区、武汉大区、福州大区、重庆大区、南宁大区、昆明大区、贵阳大区、昆明快递大区',
  `banner_id` STRING COMMENT 'banner页ID',
  `banner_name` STRING COMMENT 'banner页名称',
  `enter_pv` BIGINT COMMENT '进入页面浏览量',
  `enter_uv` BIGINT COMMENT '进入页面独立访客数',
  `sku_click_pv` BIGINT COMMENT '商品点击浏览量',
  `sku_click_uv` BIGINT COMMENT '商品点击独立访客数',
  `receive_click_pv` BIGINT COMMENT '立即领取点击浏览量',
  `receive_click_uv` BIGINT COMMENT '立即领取点击独立访客数',
  `sku_purchase_click_pv` BIGINT COMMENT '商品立即采购点击浏览量',
  `sku_purchase_click_uv` BIGINT COMMENT '商品立即采购点击独立访客数',
  `sku_cart_buy_pv` BIGINT COMMENT '商品加购浏览量',
  `sku_cart_buy_uv` BIGINT COMMENT '商品加购独立访客数',
  `sku_instant_buy_pv` BIGINT COMMENT '商品立即购买浏览量',
  `sku_instant_buy_uv` BIGINT COMMENT '商品立即购买独立访客数',
  `sku_order_order_cnt` BIGINT COMMENT '购买次数',
  `sku_order_cust_cnt` BIGINT COMMENT '购买人数'
) 
COMMENT '商城banner页流量分析表，用于分析商城banner页的用户行为数据，包括页面访问、商品点击、购买转化等指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '商城banner页流量分析表',
  'lifecycle' = '30'
);
```