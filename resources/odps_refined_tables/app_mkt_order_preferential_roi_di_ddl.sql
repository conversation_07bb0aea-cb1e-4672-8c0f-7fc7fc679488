CREATE TABLE IF NOT EXISTS app_mkt_order_preferential_roi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`city_id` BIGINT COMMENT '运营服务区ID，唯一标识一个城市运营服务区',
	`city_name` STRING COMMENT '运营服务区名称，如：中山、苏州、南京等',
	`large_area_id` BIGINT COMMENT '运营服务大区ID，唯一标识一个大区',
	`large_area_name` STRING COMMENT '运营服务大区名称，如：广州大区、苏州大区、武汉大区等',
	`cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
	`life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举值：S1、S2、W、N1、N2、A1、A2、A3、B1、B2、L1、L2',
	`preferential_type` STRING COMMENT '营销活动类型，枚举值：特价活动、临保活动、行业活动券、销售品类券、售后补偿券、换购、奶油卡、销售客情券、平台活动券、区域拉新券、其他、销售现货券、销售月活券、赠品、销售囤货券、满减、配送补偿券',
	`preferential_amt` DECIMAL(38,18) COMMENT '营销金额，单位为元，保留18位小数',
	`origin_total_amt` DECIMAL(38,18) COMMENT '订单应付金额，单位为元，保留18位小数',
	`real_total_amt` DECIMAL(38,18) COMMENT '订单实付金额，单位为元，保留18位小数'
)
COMMENT '交易维度营销活动ROI拆解报表，用于分析不同营销活动的投入产出比'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='交易维度营销活动ROI拆解报表，包含营销活动类型、金额、客户团队等信息')
LIFECYCLE 30;