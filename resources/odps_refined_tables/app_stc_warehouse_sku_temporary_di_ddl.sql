```sql
CREATE TABLE IF NOT EXISTS app_stc_warehouse_sku_temporary_di(
	warehouse_no BIGINT COMMENT '库存仓编号',
	warehouse_name STRING COMMENT '库存仓名称，枚举值：上海总仓、东莞冷冻总仓、东莞总仓、华西总仓、南京总仓、南宁总仓、嘉兴总仓、嘉兴海盐总仓、昆明总仓、武汉总仓、济南总仓、福州总仓',
	sku_id STRING COMMENT 'SKU编号，商品最小库存单位标识',
	spu_name STRING COMMENT 'SPU名称，标准产品单元名称',
	category1 STRING COMMENT '一级类目名称，枚举值：乳制品、其他、鲜果',
	category4_id STRING COMMENT '四级类目ID',
	category4 STRING COMMENT '四级类目名称',
	sku_brand STRING COMMENT 'SKU品牌名称',
	sku_property STRING COMMENT 'SKU性质，枚举值：常规、临保、拆包、破袋',
	disc STRING COMMENT 'SKU描述，商品规格描述',
	warn_days BIGINT COMMENT '到期预警天数，距离过期剩余天数预警阈值',
	unit_amt DECIMAL(38,18) COMMENT '成本单价，单个商品成本价格',
	store_quantity BIGINT COMMENT '在仓库存数量，当前仓库实际库存数量',
	sale_out_quality BIGINT COMMENT '历史两周销售出库数量，过去14天销售出库量',
	allocate_out_quality BIGINT COMMENT '历史两周调拨出库数量，过去14天调拨出库量',
	avg_quality BIGINT COMMENT '过去2周日均出库量，14天平均日出库量',
	temporary_date STRING COMMENT '临保日期，格式：yyyyMMdd，商品临近保质期日期',
	estimated_date DATETIME COMMENT '预计售罄日期，格式：yyyy-MM-dd HH:mm:ss，预计商品售完日期',
	temporary_cnt BIGINT COMMENT '预计临保件数，预计会进入临保状态的商品数量',
	temporary_amt DECIMAL(38,18) COMMENT '预计临保成本，临保商品的总成本金额',
	estimated_sale_cnt DECIMAL(38,18) COMMENT '预估出库量，预计未来销售出库数量',
	quality_date STRING COMMENT '保质期日期，格式：yyyyMMdd，商品保质期到期日期',
	damage_date DATETIME COMMENT '货损时间，格式：yyyy-MM-dd HH:mm:ss，商品发生货损的日期时间',
	doc BIGINT COMMENT '在库/预销标识，库存状态标识',
	doc_cnt BIGINT COMMENT 'doc汇总，库存状态汇总数量',
	damage_time BIGINT COMMENT '距离货损时长，距离货损剩余天数',
	quality_time BIGINT COMMENT '距离临保时长，距离临保剩余天数',
	damage_rask STRING COMMENT '货损风险标识，枚举值：-、货损风险',
	temporary_rask STRING COMMENT '临保风险标识，枚举值：-、临保风险',
	sky_type STRING COMMENT '仓储类型，枚举值：自营、代仓、代售',
	estimated_damage_cnt BIGINT COMMENT '预计货损件数，预计会发生货损的商品数量',
	estimated_damage_amt DECIMAL(38,18) COMMENT '预计货损成本，货损商品的总成本金额',
	quality_date_tag STRING COMMENT '保质期标签，枚举值：60天内、60天外，基于保质期日期的分类',
	temporary_date_tag STRING COMMENT '临保日期标签，枚举值：60天内、60天外，基于临保日期的分类',
	temporary_tag STRING COMMENT '临保风险标识，枚举值：否、是，是否有临保风险'
) 
COMMENT '库存仓、SKU维度临保数据表，记录商品临保、货损等相关库存风险数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，数据统计日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='库存仓和SKU维度的临保数据表，包含商品临保预警、货损风险、库存状态等关键指标') 
LIFECYCLE 30;
```