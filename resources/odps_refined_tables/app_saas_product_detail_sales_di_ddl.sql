```sql
CREATE TABLE IF NOT EXISTS app_saas_product_detail_sales_di(
    tenant_id BIGINT COMMENT '租户ID，唯一标识一个租户',
    time_tag STRING COMMENT '时间标签，格式为yyyyMMdd，表示年月日',
    item_id BIGINT COMMENT '商品编码，唯一标识一个商品',
    sku_id STRING COMMENT '商品SKU ID，唯一标识一个商品的具体规格',
    delivery_type BIGINT COMMENT '仓储类型：0-自营仓，1-第三方仓',
    warehouse_type BIGINT COMMENT '商品类型：0-自营，2-经销',
    title STRING COMMENT '商品名称',
    specification STRING COMMENT '商品规格描述',
    category STRING COMMENT '商品类目路径',
    brand_name STRING COMMENT '品牌名称',
    supply_price DECIMAL(38,18) COMMENT '供应价，商品供应价格',
    price DECIMAL(38,18) COMMENT '售价，商品销售价格',
    category_id BIGINT COMMENT '三级类目ID',
    store_type BIGINT COMMENT '门店类型：0-直营店，1-加盟店，2-托管店',
    store_id BIGINT COMMENT '门店ID，唯一标识一个门店',
    store_name STRING COMMENT '门店名称',
    province STRING COMMENT '省份名称',
    city STRING COMMENT '城市名称',
    address STRING COMMENT '地址信息，格式为省+市',
    sales_num BIGINT COMMENT '销售数量',
    sales_price DECIMAL(38,18) COMMENT '销售额',
    after_sale_num BIGINT COMMENT '售后数量',
    after_sale_price DECIMAL(38,18) COMMENT '售后金额',
    goods_type BIGINT COMMENT '商品类型：0-无货商品，1-报价货品，2-自营货品',
    supplier_name STRING COMMENT '供应商名称',
    after_sale_unit STRING COMMENT '售后单位：箱、件、g、袋、卷、桶、罐、个、组、套、包、盒、瓶、条、块、张、顶、斤、G、份、筐、支、只',
    after_sale_apply_price DECIMAL(38,18) COMMENT '供应商退款申请金额',
    after_sale_total_price DECIMAL(38,18) COMMENT '供应商退款实际金额'
)
COMMENT 'SaaS商品销售数据表，包含商品销售明细、售后信息、门店信息等'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='SaaS平台商品销售明细数据表，记录各门店商品销售情况、售后处理信息及供应商相关数据')
LIFECYCLE 30;
```