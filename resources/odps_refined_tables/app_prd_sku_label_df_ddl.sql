CREATE TABLE IF NOT EXISTS app_prd_sku_label_df(
	`sku_id` STRING COMMENT 'SKU ID，商品最小库存单位唯一标识',
	`spu_name` STRING COMMENT '商品名称，即标准产品单元名称',
	`category1` STRING COMMENT '一级分类，商品最顶层分类',
	`category2` STRING COMMENT '二级分类，商品第二层分类',
	`category3` STRING COMMENT '三级分类，商品第三层分类',
	`category4` STRING COMMENT '四级分类，商品最细粒度分类',
	`sku_spec` STRING COMMENT '商品规格描述，如包装规格、重量等',
	`sku_type` STRING COMMENT '商品类型：0-自营商品',
	`create_time` DATETIME COMMENT '商品创建时间，格式：年月日时分秒',
	`is_new` STRING COMMENT '是否新品：是-SKU创建时间60天内算新品，否-非新品',
	`is_180d_order` STRING COMMENT '近半年是否有动销：是-近180天有销售记录，否-近180天无销售记录',
	`abc_label` STRING COMMENT '货品ABC分类：A-重点商品，B-一般商品，C-次要商品，无-未分类',
	`season_sign` STRING COMMENT '季节性标识：春季、夏季、秋季、冬季、无-无季节性',
	`life_cycle` STRING COMMENT '生命周期阶段：新品-新上市商品，成长高-高速成长期，成长中-中速成长期，成长低-低速成长期，稳定-稳定期，衰退高-高速衰退期，衰退中-中速衰退期，衰退低-低速衰退期，长尾-长尾商品，无-未分类',
	`grow_coe` DECIMAL(38,18) COMMENT '成长系数，商品增长趋势的量化指标',
	`grow_type` STRING COMMENT '成长曲线类型：波动型-波动增长，高成长型-高速增长，高衰退型-高速衰退',
	`cust_preference` STRING COMMENT '业态偏好：茶饮客户偏好、面包蛋糕客户偏好、咖啡客户偏好、西餐客户偏好、甜品冰淇淋客户偏好、水果/果切/榨汁店客户偏好、其他客户偏好及其组合，无-无特定偏好',
	`exposure_type` STRING COMMENT '曝光类别：低曝光、中曝光、高曝光',
	`click_type` STRING COMMENT '点击类别：低点击、中点击、高点击',
	`elastic_coefficient` DECIMAL(38,18) COMMENT '弹性系数，价格变化对需求影响的敏感度指标',
	`elastic_coefficient_type` STRING COMMENT '弹性类别：不弹性商品、弹性商品、中度弹性商品',
	`gross_type` STRING COMMENT '毛利类别：低毛利、中毛利、高毛利',
	`order_sku_cnt_type` STRING COMMENT '销量类别：低销量、中销量、高销量',
	`order_amt_type` STRING COMMENT 'GMV类别：低GMV、中GMV、高GMV',
	`turnover_type` STRING COMMENT '周转类别：高周转、中周转、低周转',
	`store_cost_type` STRING COMMENT '库存金额类别：低库存、中库存、高库存',
	`store_order_type` STRING COMMENT '库存应用类标签：低库存低销量、低库存中销量、低库存高销量、中库存低销量、中库存中销量、中库存高销量、高库存低销量、高库存中销量、高库存高销量',
	`order_gross_type` STRING COMMENT '销售应用类标签：低销量低毛利、低销量中毛利、低销量高毛利、中销量低毛利、中销量中毛利、中销量高毛利、高销量低毛利、高销量中毛利、高销量高毛利',
	`click_order_type` STRING COMMENT '流量应用类标签：低点击低销量、低点击中销量、低点击高销量、中点击低销量、中点击中销量、中点击高销量、高点击低销量、高点击中销量、高点击高销量',
	`purchase_amt_1d` DECIMAL(38,18) COMMENT '最近1天采购金额',
	`purchase_amt_15d` DECIMAL(38,18) COMMENT '最近15天采购金额',
	`purchase_amt_30d` DECIMAL(38,18) COMMENT '最近30天采购金额',
	`purchase_amt_90d` DECIMAL(38,18) COMMENT '最近90天采购金额',
	`purchase_amt_180d` DECIMAL(38,18) COMMENT '最近180天采购金额',
	`purchase_sku_cnt_1d` BIGINT COMMENT '最近1天采购数量',
	`purchase_sku_cnt_15d` BIGINT COMMENT '最近15天采购数量',
	`purchase_sku_cnt_30d` BIGINT COMMENT '最近30天采购数量',
	`purchase_sku_cnt_90d` BIGINT COMMENT '最近90天采购数量',
	`purchase_sku_cnt_180d` BIGINT COMMENT '最近180天采购数量',
	`first_order_time` DATETIME COMMENT '最早一次下单时间，格式：年月日时分秒',
	`last_order_time` DATETIME COMMENT '最近一次下单时间，格式：年月日时分秒',
	`order_origin_amt_1d` DECIMAL(38,18) COMMENT '最近1天下单应付金额',
	`order_origin_amt_15d` DECIMAL(38,18) COMMENT '最近15天下单应付金额',
	`order_origin_amt_30d` DECIMAL(38,18) COMMENT '最近30天下单应付金额',
	`order_origin_amt_90d` DECIMAL(38,18) COMMENT '最近90天下单应付金额',
	`order_origin_amt_180d` DECIMAL(38,18) COMMENT '最近180天下单应付金额',
	`order_real_amt_1d` DECIMAL(38,18) COMMENT '最近1天下单实付金额',
	`order_real_amt_15d` DECIMAL(38,18) COMMENT '最近15天下单实付金额',
	`order_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天下单实付金额',
	`order_real_amt_90d` DECIMAL(38,18) COMMENT '最近90天下单实付金额',
	`order_real_amt_180d` DECIMAL(38,18) COMMENT '最近180天下单实付金额',
	`order_preferential_amt_1d` DECIMAL(38,18) COMMENT '最近1天下单营销金额',
	`order_preferential_amt_15d` DECIMAL(38,18) COMMENT '最近15天下单营销金额',
	`order_preferential_amt_30d` DECIMAL(38,18) COMMENT '最近30天下单营销金额',
	`order_preferential_amt_90d` DECIMAL(38,18) COMMENT '最近90天下单营销金额',
	`order_preferential_amt_180d` DECIMAL(38,18) COMMENT '最近180天下单营销金额',
	`order_cust_cnt_1d` BIGINT COMMENT '最近1天下单客户数',
	`order_cust_cnt_15d` BIGINT COMMENT '最近15天下单客户数',
	`order_cust_cnt_30d` BIGINT COMMENT '最近30天下单客户数',
	`order_cust_cnt_90d` BIGINT COMMENT '最近90天下单客户数',
	`order_cust_cnt_180d` BIGINT COMMENT '最近180天下单客户数',
	`order_sku_cnt_1d` BIGINT COMMENT '最近1天下单商品件数',
	`order_sku_cnt_15d` BIGINT COMMENT '最近15天下单商品件数',
	`order_sku_cnt_30d` BIGINT COMMENT '最近30天下单商品件数',
	`order_sku_cnt_90d` BIGINT COMMENT '最近90天下单商品件数',
	`order_sku_cnt_180d` BIGINT COMMENT '最近180天下单商品件数',
	`order_cnt_1d` BIGINT COMMENT '最近1天下单订单数',
	`order_cnt_15d` BIGINT COMMENT '最近15天下单订单数',
	`order_cnt_30d` BIGINT COMMENT '最近30天下单订单数',
	`order_cnt_90d` BIGINT COMMENT '最近90天下单订单数',
	`order_cnt_180d` BIGINT COMMENT '最近180天下单订单数',
	`order_origin_unit_amt_180d_avg` DECIMAL(38,18) COMMENT '最近180天平均应付单价',
	`order_origin_unit_amt_180d_avg_range` STRING COMMENT '价格段：根据近180天的平均价格进行划分，取值范围：[0,50)、[50,100)、[100,150)、[150,200)、[200,250)、[250,300)、[300,350)、[350,400)、[400,)',
	`delivery_origin_amt_1d` DECIMAL(38,18) COMMENT '最近1天履约应付金额',
	`delivery_origin_amt_15d` DECIMAL(38,18) COMMENT '最近15天履约应付金额',
	`delivery_origin_amt_30d` DECIMAL(38,18) COMMENT '最近30天履约应付金额',
	`delivery_origin_amt_90d` DECIMAL(38,18) COMMENT '最近90天履约应付金额',
	`delivery_origin_amt_180d` DECIMAL(38,18) COMMENT '最近180天履约应付金额',
	`delivery_point_cnt_1d` BIGINT COMMENT '最近1天履约点位数',
	`delivery_point_cnt_15d` BIGINT COMMENT '最近15天履约点位数',
	`delivery_point_cnt_30d` BIGINT COMMENT '最近30天履约点位数',
	`delivery_point_cnt_90d` BIGINT COMMENT '最近90天履约点位数',
	`delivery_point_cnt_180d` BIGINT COMMENT '最近180天履约点位数',
	`delivery_cost_amt_180d_avg` DECIMAL(38,18) COMMENT '最近180天履约平均成本单价',
	`delivery_cost_gross_180d_avg` DECIMAL(38,18) COMMENT '最近180天履约平均应付毛利率',
	`delivery_cost_gross_180d_avg_range` STRING COMMENT '毛利率段：根据近180天的毛利进行划分，取值范围：0-5%、5-10%、10-15%、15-20%、20-25%、25-30%、30%以上',
	`store_quantity` BIGINT COMMENT '在库数量',
	`store_cost` DECIMAL(38,18) COMMENT '在库金额',
	`temporary_store_quantity` BIGINT COMMENT '临期数量',
	`temporary_store_ratio` DECIMAL(38,18) COMMENT '临期数量占比',
	`purchase_road_quantity` BIGINT COMMENT '采购在途数量',
	`purchase_road_cost` DECIMAL(38,18) COMMENT '采购在途金额',
	`allocate_road_quantity` BIGINT COMMENT '调拨在途数量',
	`allocate_road_cost` DECIMAL(38,18) COMMENT '调拨在途金额',
	`turnover_days` DECIMAL(38,18) COMMENT '周转天数，计算公式：近7天平均在库金额/近7天平均销售出库金额(含自提)',
	`store_days` DECIMAL(38,18) COMMENT '库存可用天数，计算公式：在库金额/近7天平均销售出库金额(含自提)',
	`damage_sku_cnt_1d` BIGINT COMMENT '最近1天货损数量',
	`damage_sku_cnt_15d` BIGINT COMMENT '最近15天货损数量',
	`damage_sku_cnt_30d` BIGINT COMMENT '最近30天货损数量',
	`damage_sku_cnt_90d` BIGINT COMMENT '最近90天货损数量',
	`damage_sku_cnt_180d` BIGINT COMMENT '最近180天货损数量',
	`damage_amt_1d` DECIMAL(38,18) COMMENT '最近1天货损金额',
	`damage_amt_15d` DECIMAL(38,18) COMMENT '最近15天货损金额',
	`damage_amt_30d` DECIMAL(38,18) COMMENT '最近30天货损金额',
	`damage_amt_90d` DECIMAL(38,18) COMMENT '最近90天货损金额',
	`damage_amt_180d` DECIMAL(38,18) COMMENT '最近180天货损金额',
	`after_sale_deliveryed_amt_1d` DECIMAL(38,18) COMMENT '最近1天已到货售后金额',
	`after_sale_deliveryed_amt_15d` DECIMAL(38,18) COMMENT '最近15天已到货售后金额',
	`after_sale_deliveryed_amt_30d` DECIMAL(38,18) COMMENT '最近30天已到货售后金额',
	`after_sale_deliveryed_amt_90d` DECIMAL(38,18) COMMENT '最近90天已到货售后金额',
	`after_sale_deliveryed_amt_180d` DECIMAL(38,18) COMMENT '最近180天已到货售后金额',
	`after_sale_deliveryed_order_cnt_1d` BIGINT COMMENT '最近1天已到货售后订单数',
	`after_sale_deliveryed_order_cnt_15d` BIGINT COMMENT '最近15天已到货售后订单数',
	`after_sale_deliveryed_order_cnt_30d` BIGINT COMMENT '最近30天已到货售后订单数',
	`after_sale_deliveryed_order_cnt_90d` BIGINT COMMENT '最近90天已到货售后订单数',
	`after_sale_deliveryed_order_cnt_180d` BIGINT COMMENT '最近180天已到货售后订单数',
	`after_sale_notdeliveryed_amt_1d` DECIMAL(38,18) COMMENT '最近1天未到货售后金额',
	`after_sale_notdeliveryed_amt_15d` DECIMAL(38,18) COMMENT '最近15天未到货售后金额',
	`after_sale_notdeliveryed_amt_30d` DECIMAL(38,18) COMMENT '最近30天未到货售后金额',
	`after_sale_notdeliveryed_amt_90d` DECIMAL(38,18) COMMENT '最近90天未到货售后金额',
	`after_sale_notdeliveryed_amt_180d` DECIMAL(38,18) COMMENT '最近180天未到货售后金额',
	`after_sale_notdeliveryed_order_cnt_1d` BIGINT COMMENT '最近1天未到货售后订单数',
	`after_sale_notdeliveryed_order_cnt_15d` BIGINT COMMENT '最近15天未到货售后订单数',
	`after_sale_notdeliveryed_order_cnt_30d` BIGINT COMMENT '最近30天未到货售后订单数',
	`after_sale_notdeliveryed_order_cnt_90d` BIGINT COMMENT '最近90天未到货售后订单数',
	`after_sale_notdeliveryed_order_cnt_180d` BIGINT COMMENT '最近180天未到货售后订单数',
	`sku_exposure_pv_1d` BIGINT COMMENT '最近1天曝光PV',
	`sku_exposure_pv_15d` BIGINT COMMENT '最近15天曝光PV',
	`sku_exposure_pv_30d` BIGINT COMMENT '最近30天曝光PV',
	`sku_exposure_uv_1d` BIGINT COMMENT '最近1天曝光UV',
	`sku_exposure_uv_15d` BIGINT COMMENT '最近15天曝光UV',
	`sku_exposure_uv_30d` BIGINT COMMENT '最近30天曝光UV',
	`sku_click_pv_1d` BIGINT COMMENT '最近1天点击PV',
	`sku_click_pv_15d` BIGINT COMMENT '最近15天点击PV',
	`sku_click_pv_30d` BIGINT COMMENT '最近30天点击PV',
	`sku_click_uv_1d` BIGINT COMMENT '最近1天点击UV',
	`sku_click_uv_15d` BIGINT COMMENT '最近15天点击UV',
	`sku_click_uv_30d` BIGINT COMMENT '最近30天点击UV'
) 
COMMENT '商品标签表（自营），包含自营商品的各类标签、分类、销售、库存、流量等全方位数据指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='自营商品标签表，提供商品的多维度标签信息和业务指标数据') 
LIFECYCLE 30;