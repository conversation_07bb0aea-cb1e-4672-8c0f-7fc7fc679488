CREATE TABLE IF NOT EXISTS app_purchase_list_recommend_df(
	m_id BIGINT COMMENT '商家ID，取值范围：30-7373',
	sku STRING COMMENT 'SKU编码，商品唯一标识，包含字母数字组合，如D009H20T008、A003S01R012等',
	product_id BIGINT COMMENT '产品ID，取值范围：1-17593',
	recommend_sort BIGINT COMMENT '推荐排序序号，取值范围：1-20，数值越小优先级越高'
) 
COMMENT '进货单推荐商品表，存储商家进货单中的商品推荐排序信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='进货单推荐商品表，包含商家ID、SKU、产品ID和推荐排序信息') 
LIFECYCLE 30;