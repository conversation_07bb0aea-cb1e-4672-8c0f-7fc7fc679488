```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_order_cohort_wi` (
  `first_order_year` STRING COMMENT '首购周年份，格式：yyyy',
  `first_order_week` BIGINT COMMENT '首购周周数，取值范围：1-53',
  `first_order_monday` STRING COMMENT '首购周周一日期，格式：yyyyMMdd（年月日）',
  `first_order_sunday` STRING COMMENT '首购周周日日期，格式：yyyyMMdd（年月日）',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `first_order_cust_cnt` BIGINT COMMENT 'w0首购人数',
  `cohort_cust_cnt_1` BIGINT COMMENT 'w1复购人数',
  `cohort_cust_cnt_2` BIGINT COMMENT 'w2复购人数',
  `cohort_cust_cnt_3` BIGINT COMMENT 'w3复购人数',
  `cohort_cust_cnt_4` BIGINT COMMENT 'w4复购人数',
  `cohort_cust_cnt_5` BIGINT COMMENT 'w5复购人数',
  `cohort_cust_cnt_6` BIGINT COMMENT 'w6复购人数',
  `cohort_cust_cnt_7` BIGINT COMMENT 'w7复购人数',
  `cohort_cust_cnt_8` BIGINT COMMENT 'w8复购人数',
  `cohort_cust_cnt_9` BIGINT COMMENT 'w9复购人数',
  `cohort_cust_cnt_10` BIGINT COMMENT 'w10复购人数',
  `cohort_cust_cnt_11` BIGINT COMMENT 'w11复购人数',
  `cohort_cust_cnt_12` BIGINT COMMENT 'w12复购人数',
  `cohort_cust_cnt_13` BIGINT COMMENT 'w13复购人数',
  `cohort_cust_cnt_14` BIGINT COMMENT 'w14复购人数',
  `cohort_cust_cnt_15` BIGINT COMMENT 'w15复购人数',
  `cohort_cust_cnt_16` BIGINT COMMENT 'w16复购人数',
  `cohort_cust_cnt_17` BIGINT COMMENT 'w17复购人数',
  `cohort_cust_cnt_18` BIGINT COMMENT 'w18复购人数',
  `cohort_cust_cnt_19` BIGINT COMMENT 'w19复购人数',
  `cohort_cust_cnt_20` BIGINT COMMENT 'w20复购人数',
  `cohort_cust_cnt_21` BIGINT COMMENT 'w21复购人数',
  `cohort_cust_cnt_22` BIGINT COMMENT 'w22复购人数',
  `cohort_cust_cnt_23` BIGINT COMMENT 'w23复购人数',
  `cohort_cust_cnt_24` BIGINT COMMENT 'w24复购人数',
  `first_order_real_amt` DECIMAL(38,18) COMMENT 'w0首购实付金额',
  `cohort_real_amt_1` DECIMAL(38,18) COMMENT 'w1复购实付金额',
  `cohort_real_amt_2` DECIMAL(38,18) COMMENT 'w2复购实付金额',
  `cohort_real_amt_3` DECIMAL(38,18) COMMENT 'w3复购实付金额',
  `cohort_real_amt_4` DECIMAL(38,18) COMMENT 'w4复购实付金额',
  `cohort_real_amt_5` DECIMAL(38,18) COMMENT 'w5复购实付金额',
  `cohort_real_amt_6` DECIMAL(38,18) COMMENT 'w6复购实付金额',
  `cohort_real_amt_7` DECIMAL(38,18) COMMENT 'w7复购实付金额',
  `cohort_real_amt_8` DECIMAL(38,18) COMMENT 'w8复购实付金额',
  `cohort_real_amt_9` DECIMAL(38,18) COMMENT 'w9复购实付金额',
  `cohort_real_amt_10` DECIMAL(38,18) COMMENT 'w10复购实付金额',
  `cohort_real_amt_11` DECIMAL(38,18) COMMENT 'w11复购实付金额',
  `cohort_real_amt_12` DECIMAL(38,18) COMMENT 'w12复购实付金额',
  `cohort_real_amt_13` DECIMAL(38,18) COMMENT 'w13复购实付金额',
  `cohort_real_amt_14` DECIMAL(38,18) COMMENT 'w14复购实付金额',
  `cohort_real_amt_15` DECIMAL(38,18) COMMENT 'w15复购实付金额',
  `cohort_real_amt_16` DECIMAL(38,18) COMMENT 'w16复购实付金额',
  `cohort_real_amt_17` DECIMAL(38,18) COMMENT 'w17复购实付金额',
  `cohort_real_amt_18` DECIMAL(38,18) COMMENT 'w18复购实付金额',
  `cohort_real_amt_19` DECIMAL(38,18) COMMENT 'w19复购实付金额',
  `cohort_real_amt_20` DECIMAL(38,18) COMMENT 'w20复购实付金额',
  `cohort_real_amt_21` DECIMAL(38,18) COMMENT 'w21复购实付金额',
  `cohort_real_amt_22` DECIMAL(38,18) COMMENT 'w22复购实付金额',
  `cohort_real_amt_23` DECIMAL(38,18) COMMENT 'w23复购实付金额',
  `cohort_real_amt_24` DECIMAL(38,18) COMMENT 'w24复购实付金额'
) 
COMMENT '客户cohort分析表，记录客户首购周后的复购行为数据，用于客户留存和复购分析'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='客户cohort分析表，按首购周分组统计后续24周的复购情况',
               'last_data_modified_time'='2025-09-23 02:23:28') 
LIFECYCLE 30;
```