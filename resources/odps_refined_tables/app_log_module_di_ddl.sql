CREATE TABLE IF NOT EXISTS app_log_module_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`module` STRING COMMENT '模块名称，取值范围：其他、分类、首页推荐、banner、搜索、活动、AI采购助手、常购清单、订单列表页、购物车、换购列表、优惠券可用页面',
	`cust_uv` BIGINT COMMENT '进入模块的独立客户数（UV）',
	`cust_pv` BIGINT COMMENT '进入模块的页面访问次数（PV）',
	`sku_cnt` BIGINT COMMENT '商品曝光数量',
	`click_uv` BIGINT COMMENT '点击商品的独立客户数（UV）',
	`click_pv` BIGINT COMMENT '点击商品的次数（PV）',
	`addbuy_uv` BIGINT COMMENT '加购商品的独立客户数（UV）',
	`addbuy_pv` BIGINT COMMENT '加购商品的次数（PV）',
	`order_cust_cnt` BIGINT COMMENT '下单客户数量',
	`sku_pv` BIGINT COMMENT '商品总曝光次数',
	`sku_click_pv` BIGINT COMMENT '商品总点击次数',
	`cust_type` STRING COMMENT '客户业态类型，取值范围：烘焙、咖啡、茶饮、其他',
	`cust_flag` STRING COMMENT '是否老客标识，取值范围：是、否',
	`customer_click_pv` BIGINT COMMENT '客户点击PV',
	`customer_click_uv` BIGINT COMMENT '客户点击UV',
	`order_cnt` BIGINT COMMENT '下单次数',
	`sku_exposure_pv` BIGINT COMMENT '商品曝光PV',
	`sku_exposure_uv` BIGINT COMMENT '商品曝光UV',
	`sku_add_to_cart_pv` BIGINT COMMENT '商品加购PV',
	`sku_add_to_cart_uv` BIGINT COMMENT '商品加购UV'
) 
COMMENT '首页各模块数据统计表，记录各模块的客户访问、商品曝光、点击、加购、下单等行为数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='首页模块数据明细表，按日期和模块维度统计客户行为和商品交互数据') 
LIFECYCLE 30;