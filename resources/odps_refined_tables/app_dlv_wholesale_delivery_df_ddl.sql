CREATE TABLE IF NOT EXISTS app_dlv_wholesale_delivery_df(
    `finish_date` STRING COMMENT '订单完成日期，格式：yyyyMMdd，表示年月日',
    `order_date` STRING COMMENT '订单下单日期，格式：yyyyMMdd，表示年月日',
    `order_no` STRING COMMENT '订单编号，唯一标识一个订单',
    `cust_id` BIGINT COMMENT '客户唯一标识ID',
    `cust_name` STRING COMMENT '客户全称',
    `cust_group` STRING COMMENT '客户分组类型：平台客户、批发客户',
    `point_id` BIGINT COMMENT '配送点位ID',
    `delivery_province` STRING COMMENT '配送地址省份',
    `delivery_city` STRING COMMENT '配送地址城市',
    `delivery_area` STRING COMMENT '配送地址区域/区县',
    `delivery_address` STRING COMMENT '详细配送地址',
    `bd_id` BIGINT COMMENT '销售人员ID',
    `bd_name` STRING COMMENT '销售人员姓名',
    `m1_name` STRING COMMENT 'M1级管理者姓名（销售主管）',
    `m2_name` STRING COMMENT 'M2级管理者姓名（销售经理）',
    `m3_name` STRING COMMENT 'M3级管理者姓名（销售总监）',
    `sale_area` STRING COMMENT '销售区域：杭州、浦西、杭州湾、苏州、薄荷测试、浦东、无锡等',
    `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV金额（原始总金额）',
    `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV金额（实际支付金额）'
)
COMMENT '批发客户履约数据表，记录批发客户的订单履约信息，包括订单详情、客户信息、配送地址、销售层级关系和GMV数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='批发客户履约数据表',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;