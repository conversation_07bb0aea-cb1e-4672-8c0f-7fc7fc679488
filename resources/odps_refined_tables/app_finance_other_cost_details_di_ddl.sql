CREATE TABLE IF NOT EXISTS app_finance_other_cost_details_di(
    `service_area` STRING COMMENT '大区，取值范围：华东、未知、西南、云南、福建、华中、山东、广西、贵州',
    `warehouse_no` BIGINT COMMENT '库存仓编号，数值型标识',
    `warehouse_name` STRING COMMENT '库存仓名称，取值范围：嘉兴总仓、嘉兴海盐总仓、南京总仓、东莞冷冻总仓、东莞总仓、华西总仓、昆明总仓、福州总仓、长沙总仓、青岛总仓、武汉总仓、南宁总仓、贵阳总仓',
    `sku` STRING COMMENT '商品SKU编码，唯一商品标识',
    `cost_type` STRING COMMENT '成本类型，取值范围：货损出库、调拨货损出库、盘盈入库、盘亏出库、出样出库',
    `quantity` BIGINT COMMENT '库存变动数量，正数表示入库，负数表示出库',
    `cost` DECIMAL(38,18) COMMENT '成本金额（含税）',
    `cost_notax` DECIMAL(38,18) COMMENT '成本金额（不含税）',
    `date_flag` STRING COMMENT '数据产生日期，格式：yyyyMMdd',
    `category1` STRING COMMENT '商品一级类目，取值范围：乳制品、其他、鲜果'
)
COMMENT '财务口径其他成本明细表，记录除主营业务成本外的其他成本明细数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '财务口径其他成本明细表，包含货损、盘盈盘亏、出样等非主营业务成本明细',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;