CREATE TABLE IF NOT EXISTS app_xianmu_sale_purchase_back_item_df(
	return_no STRING COMMENT '退货编号，唯一标识一次退货操作，如：ZCTG550850',
	purchase_no STRING COMMENT '采购单号，关联的采购订单编号，如：ZC0125OWJCTJ0328142568',
	order_no STRING COMMENT '销售单号，关联的销售订单编号，如：0125OWJCTJ0328142568',
	sku STRING COMMENT 'SKU编码，商品唯一标识，如：81737321485',
	pd_name STRING COMMENT '商品名称，如：性能测试品',
	weight STRING COMMENT '商品规格/重量信息，可能为None表示无规格信息',
	purchase_quantity BIGINT COMMENT '采购数量，原始采购的商品数量',
	actual_quantity BIGINT COMMENT '实际退货数量，实际办理退货的商品数量',
	cost DECIMAL(38,18) COMMENT '单个成本，单个商品的成本价格，单位：元',
	total_cost DECIMAL(38,18) COMMENT '总成本，退货商品的总成本金额，单位：元',
	no_in_quantity BIGINT COMMENT '未入库数量，采购但尚未入库的商品数量'
)
COMMENT '采购退货单表，记录采购退货相关的商品信息，包括退货编号、关联订单、商品信息、数量及成本等'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='采购退货业务数据表，用于记录和管理采购退货流程中的商品明细信息')
LIFECYCLE 30;