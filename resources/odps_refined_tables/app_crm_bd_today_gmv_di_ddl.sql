```sql
CREATE TABLE IF NOT EXISTS app_crm_bd_today_gmv_di(
    bd_id BIGINT COMMENT '销售ID，唯一标识一个销售人员',
    bd_name STRING COMMENT '销售姓名',
    is_m1 BIGINT COMMENT '是否M1管理者：1-是，0-否',
    total_gmv DECIMAL(38,18) COMMENT '总GMV（含SaaS），单位：元',
    single_gmv DECIMAL(38,18) COMMENT '单店GMV，单位：元',
    vip_gmv DECIMAL(38,18) COMMENT '大客户GMV，单位：元',
    fruit_gmv DECIMAL(38,18) COMMENT '鲜果品类GMV，单位：元',
    dairy_gmv DECIMAL(38,18) COMMENT '乳制品品类GMV，单位：元',
    non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品品类GMV，单位：元',
    brand_gmv DECIMAL(38,18) COMMENT '自营品牌GMV，单位：元',
    reward_gmv DECIMAL(38,18) COMMENT '固定奖励SKU的GMV，单位：元',
    reward_amout BIGINT COMMENT '固定奖励SKU销量，单位：件',
    brand_order_merchant BIGINT COMMENT '自营品牌下单客户数',
    pull_new_amout BIGINT COMMENT '拉新数量',
    visit_num BIGINT COMMENT '拜访客户数量',
    delivery_gmv DECIMAL(38,18) COMMENT '配送服务GMV，单位：元',
    delivery_spu_avg DECIMAL(38,18) COMMENT '配送SPU均值，单位：元/SPU',
    estimated_income DECIMAL(38,18) COMMENT '预估总收益，单位：元',
    agent_gmv DECIMAL(38,18) COMMENT '代售GMV，单位：元',
    order_merchant BIGINT COMMENT '总下单客户数'
)
COMMENT 'BD本日GMV统计表，记录销售人员每日各项业务指标数据，包括GMV分项、客户拜访、拉新等核心业务数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='BD本日GMV统计表，用于销售业绩分析和业务监控',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```