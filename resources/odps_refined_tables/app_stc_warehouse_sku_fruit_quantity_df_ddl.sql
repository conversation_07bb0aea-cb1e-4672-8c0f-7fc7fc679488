CREATE TABLE IF NOT EXISTS app_stc_warehouse_sku_fruit_quantity_df(
	`warehouse_no` BIGINT COMMENT '库存仓号，数值型标识',
	`warehouse_name` STRING COMMENT '库存仓名称，枚举值包括：上海总仓、嘉兴总仓、华西总仓、重庆总仓、福州总仓、长沙总仓、南宁总仓、昆明总仓、苏州总仓、贵阳总仓、青岛总仓、东莞总仓、美团优选杭州一仓、美团优选杭州二仓、美团优选上海仓、美团虚拟代下单总仓、美团代加工虚拟仓、南京总仓、叮咚买菜昆山仓、济南总仓、嘉兴水果批发总仓、武汉总仓',
	`sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
	`on_sale` STRING COMMENT '上架状态，枚举值：上架-正常销售、异常-状态异常、下架-已下架',
	`sku_type` STRING COMMENT 'SKU类型，枚举值：自营-平台自营商品、代仓-第三方仓库代管、代售-第三方销售',
	`sku_disc` STRING COMMENT '商品规格描述，如：5个装、5斤*1包/普通/单果70-200g等',
	`spu_name` STRING COMMENT '商品名称',
	`category1` STRING COMMENT '一级类目，枚举值：鲜果',
	`category2` STRING COMMENT '二级类目，枚举值包括：新鲜水果、新鲜蔬菜、鲜果礼盒、POP—鲜果',
	`category3` STRING COMMENT '三级类目，枚举值包括：柑果类、核果类、浆果类、绿叶菜类、根茎菜类、茄果菜类、水生菜类、食用菌类、水果礼盒、其他蔬菜、瓠果类、仁果类、其他新鲜水果、瓜菜类、榴莲、西瓜、葡萄、提子、蜜瓜、苹果、香蕉、梨、桃子、枣、李子、小番茄、蓝莓/树莓、芒果、山竹、龙眼、石榴、车厘子/樱桃、火龙果、猕猴桃/奇异果、椰子、凤梨/菠萝、水果黄瓜、菠萝蜜、草莓、柠檬/百香果、柑/橙、木瓜/杨桃、牛油果、橘/桔、柚、其他鲜果、工具/包装',
	`sku_life` STRING COMMENT 'SKU生命周期状态，枚举值：使用中-正常使用、已删除-已删除、上新处理中-正在上新、None-无状态',
	`origin_palce` STRING COMMENT '产地信息',
	`is_domestic` STRING COMMENT '是否国产，枚举值：是-国产、否-进口',
	`is_big_cust` STRING COMMENT '是否大客户专享，枚举值：是-大客户专享、否-非大客户专享',
	`quality_time` BIGINT COMMENT '保质期，单位：天',
	`store_batch_cnt` BIGINT COMMENT '在仓批次数',
	`first_batch_date` STRING COMMENT '最早批次日期，格式：yyyy-MM-dd HH:mm:ss，表示年月日时分秒',
	`first_batch_time` BIGINT COMMENT '最早批次库龄，单位：天（与今日对比）',
	`last_batch_date` STRING COMMENT '最新批次日期，格式：yyyy-MM-dd HH:mm:ss，表示年月日时分秒',
	`last_batch_time` BIGINT COMMENT '最新批次库龄，单位：天（与今日对比）',
	`store_quantity` BIGINT COMMENT '仓库库存数量',
	`lock_quantity` BIGINT COMMENT '锁定库存数量',
	`safe_quantity` BIGINT COMMENT '安全库存数量',
	`road_quantity` BIGINT COMMENT '在途库存数量',
	`uesed_quantity` BIGINT COMMENT '可用库存数量',
	`uesed_road_quantity` BIGINT COMMENT '可用及在途库存数量',
	`road_amt` DECIMAL(38,18) COMMENT '在途库存成本金额',
	`uesed_road_amt` DECIMAL(38,18) COMMENT '可用及在途成本金额',
	`unit_coat` DECIMAL(38,18) COMMENT '成本单价',
	`nearly_7_sale_cnt` BIGINT COMMENT '近7天出库量',
	`nearly_7_sale_avg` DECIMAL(38,18) COMMENT '近7天日均出库量',
	`nearly_7_sale_amt` DECIMAL(38,18) COMMENT '近7天出库成本金额',
	`nearly_7_store_amt` DECIMAL(38,18) COMMENT '近7天在库成本金额',
	`sku_turnover` DECIMAL(38,18) COMMENT '近7天周转天数',
	`nearly_7_sale_out_rask` DECIMAL(38,18) COMMENT '近7天售罄率',
	`nearly_3_sale_cnt` BIGINT COMMENT '近3天出库量',
	`nearly_3_sale_avg` DECIMAL(38,18) COMMENT '近3天日均出库量',
	`doc` DECIMAL(38,18) COMMENT '库存覆盖天数，计算公式：可用+在途 / 近三日均出库',
	`flag_tag` STRING COMMENT '库存打标，枚举值：高售罄-销售速度快、七天无动销-7天无销售、高周转-周转率高、严重滞销-滞销严重、--无标签',
	`turnover_quantity` DECIMAL(38,18) COMMENT '周转库存数量',
	`emergency_quantity` DECIMAL(38,18) COMMENT '应急库存数量',
	`excess_quantity` DECIMAL(38,18) COMMENT '过剩库存数量',
	`risk_quantity` DECIMAL(38,18) COMMENT '风险库存数量'
) 
COMMENT '仓+SKU维度鲜果库存数据表，包含各仓库SKU的库存信息、批次信息、销售指标和库存状态标签'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='鲜果库存数据分析表，用于库存管理和销售预测') 
LIFECYCLE 30;