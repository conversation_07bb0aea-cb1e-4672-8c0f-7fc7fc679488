```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_recommendation_final` (
  `key` STRING COMMENT '推荐key，枚举类型：product_recommend_sku-商品SKU推荐，product_recommend_mid_v2-会员ID推荐',
  `hashkey` STRING COMMENT '推荐hash key，取值包括m_id(会员ID)或sku(商品ID)，示例值：10041743180、10041743822、100553等',
  `value` STRING COMMENT '推荐value，包含多个推荐项的逗号分隔列表，每个推荐项可能是商品ID、会员ID或其他推荐标识符'
)
COMMENT '推荐结果汇总成list表，用于将推荐结果同步至Redis缓存系统，提高推荐服务的响应速度'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '推荐结果汇总表，存储各类推荐场景的最终结果',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```