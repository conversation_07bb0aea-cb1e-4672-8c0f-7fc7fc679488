CREATE TABLE IF NOT EXISTS app_service_area_deliver_cost_wi(
	`year` STRING COMMENT '年份，格式：YYYY',
	`week_of_year` STRING COMMENT '周数，格式：1-53',
	`monday` STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
	`sunday` STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
	`service_area` STRING COMMENT '服务区域，枚举值：华东、华北、华南、福建、广西、昆明、华中、贵阳、华西',
	`area_no` BIGINT COMMENT '城配仓编号，取值范围：1-144',
	`area_name` STRING COMMENT '城配仓名称',
	`out_point_cnt` BIGINT COMMENT '外区点位数，取值范围：0-801',
	`in_point_cnt` BIGINT COMMENT '内区点位数，取值范围：0-3326',
	`total_point_cnt` BIGINT COMMENT '总点位数，取值范围：0-3326',
	`heytea_point_cnt` BIGINT COMMENT '喜茶点位数，取值范围：0',
	`self_point_cnt` BIGINT COMMENT '自营点位数，取值范围：0-3326',
	`bms_delivery_amt` DECIMAL(38,18) COMMENT 'BMS配送费用',
	`offine_delivery_amt` DECIMAL(38,18) COMMENT '线下配送费用',
	`total_delivery_amt` DECIMAL(38,18) COMMENT '总配送费用',
	`offine_other_amt` DECIMAL(38,18) COMMENT '自营配送额外费_管理费税费及其他',
	`heytea_other_amt` DECIMAL(38,18) COMMENT '喜茶配送额外费_管理费税费及其他',
	`heytea_bms_deliver_amt` DECIMAL(38,18) COMMENT '喜茶BMS费用',
	`heytea_may_car_amt` DECIMAL(38,18) COMMENT '喜茶专车费用',
	`heytea_car_amt` DECIMAL(38,18) COMMENT '喜茶打车费用',
	`heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送费用',
	`self_delivery_amt` DECIMAL(38,18) COMMENT '自营配送费用'
)
COMMENT '服务区域城配仓配送费用明细表，按周统计各城配仓的配送费用和点位数信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：YYYYMMDD（年月日）')
STORED AS ALIORC
TBLPROPERTIES ('comment'='服务区域城配仓配送费用明细表，包含各区域配送仓的点位统计和费用明细数据')
LIFECYCLE 30;