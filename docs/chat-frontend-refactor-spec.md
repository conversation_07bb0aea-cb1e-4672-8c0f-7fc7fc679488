# ChatBI 前端聊天重构方案（Spec + TODO）

> 目的：用更简单、可维护、可推导的数据流重写“会话/消息/轮询”的核心逻辑，消除多处补丁和竞态，提升稳定性与可观测性。

## 背景与问题

- 现状症状
  - 新建对话后侧栏偶发不出现最新标题；首屏历史加载时会“覆盖/抖动”。
  - 进行中消息切换会话再切回，AI 气泡不见但后台仍在轮询，需要额外“恢复”流程。
  - 组件/状态体量大、职责交叉（`useChatState`、`useHistoryState`、`pollingManager`、`ChatInput`、`ChatbiLayout`），维护困难。
  - 多源真相：既存 `conversationGroups` 为持久状态而非派生，导致“覆盖/合并/去重”层层补丁。

- 根因总结
  - UI 引用耦合：用 `streamingMessageRef` 指针驱动渲染，切会话重建数组后需“恢复”才能找回引用。
  - ID 绑定脆弱：UI 消息 ID（字符串）与服务端 `chat_history_id`（数字）的绑定靠时序事件，易断裂。
  - 分组持久：`conversationGroups` 不派生，任何后台加载都会与本地临时插入互相覆盖。

## 目标（Goals）

- 单一数据真相：以 Store 中规范化结构为源，视图均为派生（computed）
- 首条消息路径统一：无论“点击新建”或“直接发送”，插入侧栏的时机与数据一致
- 进行中消息可回溯：切换/刷新后仍能无缝恢复进行中的消息展示与轮询
- API 边界清晰：发送、选择、加载、轮询等由 Store 以函数边界承接
- 低侵入、可回退：分阶段替换旧逻辑，里程碑间可分支验证

非目标（Non-goals）
- 不改后端接口协议（除非必要）
- 不做样式大改（但会简化不必要的状态耦合）

约束与假设（Constraints & Assumptions）
- 后端 `/query`：若前端不传 `conversation_id`，会生成 UUID 并回传
- 文档、代码注释一律中文；前端不使用 indigo 色系（设计约束）

## 高层设计（High-level Design）

### 单一 Store（`conversationStore`）

- 状态（规范化）
  - `conversations: Map<string, Conversation>`
    - `Conversation = { id, title, lastTs, isGoodCase?, isBadCase?, goodCaseFeedback?, badCaseFeedback? }`
  - `messagesByCid: Map<string, Message[]>`
    - `Message = { uiId: string, role: 'user'|'assistant', content, renderedContent?, images?, timestamp, isInProcess?, isStreaming?, isError?, isInterrupted?, chatHistoryId?: number }`
  - `inflightByChatId: Map<number, { cid: string, uiId: string }>`
  - `activeCid: string|null`

- 派生（computed）
  - `conversationGroups = groupByDate(conversations)`
  - `activeMessages = messagesByCid.get(activeCid) || []`

- 方法（边界）
  - 会话选择：`select(cid)` → 更新 `activeCid`，不做渲染以外副作用
  - URL 装载：`loadFromUrl()` → 若有 `chat` 参数则拉取单聊并注册；否则保持欢迎态
  - 历史分页：`loadHistory(page)` → 合并 `conversations` 与 `messagesByCid`（仅增量 upsert）
  - 发送消息：`send({ text, images, agent })`
    - 追加用户消息 `appendUserMessage`（若无 `activeCid` 则等后端返回）
    - 追加 AI 占位 `appendAssistantPlaceholder`（生成 `uiId`）
    - 调 `/query`：得到 `{ conversation_id, chat_history_id }`
      - 若新会话：`registerConversation(conversation_id)` 并迁移占位到新 cid（如有）
      - 建立映射 `inflightByChatId.set(chat_history_id, { cid, uiId })`
      - `polling.start(chat_history_id, { onUpdate, onComplete, onError })`
  - 轮询回调：
    - `onUpdate`：根据 `chat_history_id` 查 `inflightByChatId` → 定位消息（`cid + uiId`）→ 更新 `content/renderedContent/isStreaming` 等
    - `onComplete`：同上，落最终内容并清理 `inflight` 映射
  - 分享/标记：对外暴露薄包装，调用既有 `historyService` 即可

### 组件适配（逐步替换）

- `ChatInput`：仅负责输入与调用 `store.send`；不再持本地“流式指针”
- `HistorySidebar`：读 `conversationGroups` 和 `activeCid`，触发 `store.select`
- `ChatArea`：读 `activeMessages` 渲染；不再处理“恢复”
- `useChatState / useHistoryState`：保留 API 但逐步代理到 Store；完成后删除冗余逻辑（`streamingMessageRef/recoverStreamingMessages` 等）

### URL 策略

- 选中会话时更新 `?chat=<cid>`；新对话保持无 `chat` 参数的欢迎态直到发送

## 数据模型与接口（草案）

```js
// src/static/js/chatbi/store/conversationStore.js
import { reactive, computed } from 'vue';

const state = reactive({
  conversations: new Map(),
  messagesByCid: new Map(),
  inflightByChatId: new Map(),
  activeCid: null,
});

function select(cid) { state.activeCid = cid; }

async function loadFromUrl() { /* 读取 URL 并调用 loadConversation(cid) */ }
async function loadHistory(page=1, limit=20) { /* 只 upsert，不覆盖 */ }
async function loadConversation(cid) { /* 拉单聊并 upsert */ }

function appendUserMessage(cid, { text, images }) { /* 生成 uiId，push 到 messagesByCid[cid] */ }
function appendAssistantPlaceholder(cid) { /* 生成 uiId，push 占位并返回 uiId */ }

async function send({ text, images, agent }) {
  const cid = state.activeCid; // 可为空
  const uiId = appendAssistantPlaceholder(cid);
  // 调 /query，拿 conversation_id & chat_history_id
  // 完成后：registerConversation(realCid)，迁移占位（如 cid 为空或变化），绑定 inflight 映射并启动轮询
}

// 轮询回调（由 pollingManager 调用或内部实现）
function handlePollUpdate(chatHistoryId, data) { /* 根据 inflight 映射找到消息后更新 */ }
function handlePollComplete(chatHistoryId, data) { /* 同上并收尾 */ }

export const conversationStore = { state, select, loadFromUrl, loadHistory, loadConversation, send, handlePollUpdate, handlePollComplete };
```

## 渐进式落地（里程碑）

里程碑1（发送链路接入 Store，轮询与映射稳定）
- 新增 `store/conversationStore.js`（仅实现发送、占位、轮询回调、select、activeMessages 派生）
- `ChatInput` 改为调用 `store.send`
- `ChatbiLayout` 中“恢复逻辑”临时保留，但不再依赖；验证“切走再切回气泡可见”

里程碑2（读取链路接入 Store，派生视图）
- 将 `useHistoryState` 的 `conversationGroups` 改为 computed 派生（或代理到 Store 的 selector）
- `HistorySidebar` 使用派生 `conversationGroups`；`ChatArea` 使用 `activeMessages`
- `useChatState`、`useHistoryState` 逐步变为 Store 代理层

里程碑3（清理与收敛）
- 删除 `streamingMessageRef/recoverStreamingMessages`、分组“合并/覆盖”补丁等冗余代码
- `historyService` 仅保留分页/单聊加载、标记/分享接口
- 小范围样式/类名调整（不涉及品牌色）

## 详细 TODO（按里程碑拆解）

- [x] M1-1 新建 `src/static/js/chatbi/store/conversationStore.js`，写明 JSDoc 类型（中文）
- [x] M1-2 实现核心状态：`conversations/messagesByCid/inflightByChatId/activeCid`
- [x] M1-3 实现 `send()`：
  - [ ] 生成占位 `uiId`，push 到当前（或临时）会话消息数组
  - [ ] 调 `/query`，绑定 `chat_history_id → {cid, uiId}`，启动轮询
  - [ ] onUpdate/onComplete 通过映射定位并更新对应消息
- [x] M1-4 `ChatInput` 改为直接使用 `store.send()`；保持原 props/emits 兼容
- [x] M1-5 `pollingManager` 接口适配：新增基于 `chat_history_id` 的统一回调注入
- [x] M1-6 验证：新会话首发→切走→切回，气泡持续存在且可更新

- [x] M2-1 提供 selector：`getConversationGroups()`（按天派生，不持久存储）
- [x] M2-2 `HistorySidebar` 使用 selector；`ChatArea` 使用 `activeMessages`
- [x] M2-3 `useChatState/useHistoryState` 改为薄代理；保留对外 API，内部转调 Store（Chat 层已不再依赖，剩余旧API将于M3清理）
- [x] M2-4 URL 装载：`store.loadFromUrl()`，支持 `?chat=` 直接打开单聊
- [x] M2-5 历史分页：`store.loadHistory(page)` → 仅 upsert，不覆盖

- [x] M3-1 移除：`streamingMessageRef`、`recoverStreamingMessages`、分组“合并/覆盖”补丁（通过薄代理替换 useChatState，删除 useMessageHandler）
- [x] M3-2 整理注释与死代码；删冗余日志；保留 `localStorage` 开关型调试日志（删除 useMessageHandler / useHistoryState，清空旧流式路径）
- [x] M3-3 文档与维护手册：新增 `docs/conversation-store-usage.md`（中文）

## 验收标准（Acceptance Criteria）

- 新会话：不点击“新建”，直接发送 → 侧栏立即出现标题；切走再切回，AI 气泡连续打字直至完成
- 旧会话：发送→切走→切回 → 气泡持续且日志、时间等信息正确
- 首屏：欢迎/推荐组件在“新建对话后但未发送”仍显示；发送后收起
- 刷新：带 `?chat=<cid>` 进入 → 自动加载该会话；若消息进行中自动续跑轮询
- 历史：首屏历史返回不会顶掉刚插入的会话或消息；无重复条目
- 性能：发送与轮询时，消息区域无明显抖动；大于 200 条消息的渲染仍流畅（基本滚动）

## 风险 & 缓解

- 轮询映射泄漏：`inflightByChatId` 未清理 → 在 `onComplete/onError` 必须清理；`stopAllPolling()` 在页面卸载时调用
- URL 与选择不一致：统一由 `store.select()` 驱动，路由仅做镜像（浏览器后退恢复选择）
- 历史与实时并发：`loadHistory` 只 upsert，且 `Message` 去重规则以 `(chatHistoryId || uiId)` 为键
- 移动端侧栏：切换关闭抽屉逻辑保留在布局层，不侵入 Store

## 里程碑时间预估

- M1：0.5–1 天
- M2：0.5 天
- M3：0.5 天
- 文档与打磨：0.5 天

## 开发说明（代码片段）

```js
// 片段：发送主过程
async function send({ text, images = [], agent = null }) {
  const cid = state.activeCid; // 可能为 null（新会话）
  const uiId = createUiId();

  // 暂存占位到临时cid（用 activeCid 或特殊键），待真实cid返回后迁移
  upsertAssistantPlaceholder(cid, { uiId });

  const payload = { query: text, images, agent };
  if (cid) payload.conversation_id = cid;

  const resp = await fetch('/query', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
  const data = await resp.json();

  const realCid = data.conversation_id;
  const chatHistoryId = data.chat_history_id;
  ensureConversation(realCid, { titleFrom: text });
  migratePlaceholderIfNeeded(cid, realCid, uiId); // 如果 cid 为空或变化

  inflightByChatId.set(chatHistoryId, { cid: realCid, uiId });
  pollingManager.startPolling(chatHistoryId, {
    onUpdate: (d) => handlePollUpdate(chatHistoryId, d),
    onComplete: (d) => handlePollComplete(chatHistoryId, d),
    onError: (e) => handlePollError(chatHistoryId, e),
  });
}
```

## 回滚策略

- Store 文件独立新增，先由 `ChatInput` 接入；若线上异常，可快速回退到旧 `useChatState` 发送路径
- 清理阶段在 M3 才执行，确保可以在 M1/M2 任一节点回滚

## 可观测性与调试

- 本地 `localStorage.setItem('debug:chat', '1')` 开启日志：打印 inflight 映射、选择切换、轮询状态
- 关键边界出错时，统一以 `console.warn` 附带上下文（cid/uiId/chatHistoryId）

—— 完 ——
