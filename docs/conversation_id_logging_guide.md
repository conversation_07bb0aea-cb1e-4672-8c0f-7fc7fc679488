# Conversation ID 日志输出功能使用指南

## 概述

本项目已实现了完整的conversation_id日志输出功能，确保在整个请求处理链路中的所有日志都包含conversation_id信息，便于日志过滤和问题追踪。

## 功能特性

### 1. 自动日志增强
- 所有日志消息自动包含conversation_id前缀
- 格式：`[2025-09-05 15:59:29] - INFO - chat_bi_mysql - [conversation_id] 原始日志消息`
- 无需修改现有代码，完全向后兼容

### 2. 线程安全隔离
- 使用`threading.local`确保不同线程间的conversation_id完全隔离
- 避免对话A的conversation_id影响对话B的日志

### 3. 离线任务保护
- 自动检测离线任务（定时任务、后台服务等）
- 离线任务的日志不会包含conversation_id，避免污染

### 4. 上下文生命周期管理
- 自动设置和清理conversation_id上下文
- 支持上下文管理器模式，确保资源正确释放

## 架构组件

### 1. 核心模块

#### `src/utils/conversation_context.py`
- 提供基于线程本地存储的conversation_id上下文管理
- 核心类：`ConversationContext`
- 全局实例：`conversation_context`

#### `src/utils/enhanced_logger.py`
- 增强的日志记录器，自动在日志中包含conversation_id
- 核心类：`ConversationFormatter`
- 自动升级现有logger

#### `src/utils/logger.py`
- 原始logger配置，已升级支持conversation_id
- 自动尝试升级为enhanced_logger

### 2. 处理器集成

#### API处理器 (`src/services/agent/api_query_processor.py`)
- 继承BaseQueryProcessor，自动获得conversation_id支持
- 无需额外配置

#### 飞书处理器 (`src/services/feishu/query_processor.py`)
- 在`handle_agent_query`方法中设置conversation_id上下文
- 在finally块中清理上下文

#### 基础处理器 (`src/services/agent/base_query_processor.py`)
- 统一处理conversation_id上下文设置和清理
- 支持主线程和后台任务

## 使用方法

### 1. 基础使用（自动）
```python
from src.utils.logger import logger

# 在有conversation_id上下文的环境中，日志会自动包含conversation_id
logger.info("这条日志会自动包含conversation_id")
```

### 2. 手动设置上下文
```python
from src.utils.conversation_context import conversation_context
from src.utils.logger import logger

# 设置conversation_id
conversation_context.set_conversation_id("your_conversation_id")

# 这些日志会包含conversation_id
logger.info("处理用户请求")
logger.warning("发现潜在问题")

# 清理上下文
conversation_context.clear_conversation_id()
```

### 3. 使用上下文管理器
```python
from src.utils.conversation_context import conversation_context
from src.utils.logger import logger

# 推荐方式：使用上下文管理器
with conversation_context.conversation_scope("your_conversation_id"):
    logger.info("在上下文管理器内的日志")
    # 自动清理，无需手动调用clear
```

## 日志输出示例

### 无conversation_id的日志
```
[2025-09-05 15:59:29] - INFO - chat_bi_mysql - 这是一条普通日志
```

### 有conversation_id的日志
```
[2025-09-05 15:59:29] - INFO - chat_bi_mysql - [test_conv_12345] 这是一条带conversation_id的日志
```

### 线程隔离示例
```
[2025-09-05 15:59:29] - INFO - chat_bi_mysql - [thread_conv_1] 线程1开始工作
[2025-09-05 15:59:29] - INFO - chat_bi_mysql - [thread_conv_2] 线程2开始工作
[2025-09-05 15:59:29] - INFO - chat_bi_mysql - [thread_conv_3] 线程3开始工作
```

## 测试验证

运行测试脚本验证功能：
```bash
uv run python test_conversation_id_logging.py
```

测试内容包括：
- Logger升级状态检查
- 基础日志功能
- 带conversation_id的日志功能
- 上下文管理器功能
- 线程隔离功能

## 故障排除

### 1. Logger未升级
如果看到以下消息：
```
[Logger] 导入enhanced_logger失败: ...
```

检查：
- 确保`src/utils/enhanced_logger.py`文件存在
- 检查导入路径是否正确

### 2. Conversation_id未显示
如果日志中没有显示conversation_id：

检查：
- 是否正确设置了conversation_id上下文
- 是否在离线任务中（离线任务会被自动过滤）
- Logger是否成功升级

### 3. 线程间干扰
如果发现不同对话的conversation_id混乱：

检查：
- 确保每个线程都正确设置了自己的conversation_id
- 确保在处理完成后正确清理了上下文

## 最佳实践

### 1. 上下文管理
- 优先使用上下文管理器模式
- 确保在异常情况下也能正确清理上下文
- 避免在全局范围内长期持有conversation_id

### 2. 性能考虑
- conversation_id功能对性能影响极小
- 线程本地存储访问速度很快
- 日志格式化开销可忽略

### 3. 调试技巧
- 使用conversation_id快速过滤特定对话的日志
- 在复杂的异步处理中追踪请求流向
- 结合时间戳分析请求处理时间

## 技术细节

### 线程安全实现
```python
class ConversationContext:
    def __init__(self):
        # 使用threading.local确保线程安全
        self._local = threading.local()
```

### 日志格式化实现
```python
class ConversationFormatter(logging.Formatter):
    def format(self, record: logging.LogRecord) -> str:
        # 获取当前conversation_id
        conversation_id = safe_get_conversation_id()
        
        # 在格式化后的消息中插入conversation_id
        if conversation_id:
            # 处理逻辑...
```

### 离线任务检测
```python
def is_offline_task() -> bool:
    thread_name = threading.current_thread().name
    offline_patterns = ['token_refresh', 'session_cleanup', 'background', ...]
    return any(pattern in thread_name.lower() for pattern in offline_patterns)
```

## 总结

conversation_id日志输出功能已完全集成到项目中，提供了：
- 自动化的日志增强
- 线程安全的上下文管理
- 完整的生命周期控制
- 向后兼容的API

无需修改现有代码即可享受conversation_id日志追踪的便利。