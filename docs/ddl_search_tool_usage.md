# DDL内容搜索工具使用说明

## 概述

DDL内容搜索工具 (`search_ddl_by_content`) 是一个新的AI工具函数，用于在ODPS表定义文件中进行基于内容的智能搜索。与传统的精确表名匹配不同，该工具支持使用自然语言关键词（包括中文）在DDL文件中搜索相关内容。

## 功能特点

### 1. 智能内容搜索
- **支持中文关键词**：可以使用"SaaS订单"、"客户信息"、"库存管理"等中文关键词
- **模糊匹配**：不需要精确的表名，基于文件内容进行搜索
- **多关键词支持**：支持多个关键词组合搜索，用空格、逗号等分隔

### 2. 相关度排序
- **智能评分**：根据关键词在文件中的出现频率和位置计算相关度分数
- **注释权重**：关键词出现在注释中会获得更高的相关度分数
- **结果排序**：按相关度从高到低返回搜索结果

### 3. 结果控制
- **数量限制**：默认返回最多10个最相关的表DDL，可自定义
- **完整内容**：返回匹配表的完整DDL定义
- **文件信息**：包含文件名和相关度分数

## 使用方法

### 函数签名
```python
async def search_ddl_by_content(keywords: str, max_results: int = 10) -> str
```

### 参数说明
- `keywords` (str): 搜索关键词，支持中文。可以是单个词或多个词（用空格分隔）
- `max_results` (int): 最多返回的表DDL数量，默认10个

### 返回值
返回包含匹配到的DDL文件内容的字符串，格式如下：
```
=== 表名.md (相关度: 0.85) ===
[DDL内容]

=== 表名2.md (相关度: 0.72) ===
[DDL内容]
```

## 使用示例

### 示例1：搜索SaaS相关表
```python
# 搜索SaaS订单相关的表
result = await search_ddl_by_content("SaaS订单", max_results=5)
```

### 示例2：搜索客户相关表
```python
# 搜索客户信息相关的表
result = await search_ddl_by_content("客户", max_results=10)
```

### 示例3：多关键词搜索
```python
# 搜索库存和仓库相关的表
result = await search_ddl_by_content("库存 仓库", max_results=8)
```

### 示例4：业务主题搜索
```python
# 搜索财务相关的表
result = await search_ddl_by_content("财务 账单 收入", max_results=6)
```

## 技术实现

### 1. 搜索机制
- 使用Linux `grep` 命令进行高效的文件内容搜索
- 支持递归搜索 `resources/odps_raw_tables/` 目录下的所有 `.md` 文件
- 忽略大小写进行匹配

### 2. 相关度算法
相关度分数计算包含以下因素：
- **基础分数**：关键词存在即获得0.3分
- **频率分数**：基于关键词出现次数，最多0.4分
- **位置加权**：关键词出现在注释中额外获得0.3分
- **归一化**：最终分数归一化到0-1范围

### 3. 性能优化
- **超时控制**：grep搜索设置30秒超时
- **并发处理**：支持多关键词并行搜索
- **内存优化**：只读取匹配文件的内容

## 适用场景

### 1. 主题式表结构探索
当AI需要了解某个业务主题相关的所有表结构时：
```python
# 探索订单相关的所有表
result = await search_ddl_by_content("订单")
```

### 2. 业务分析支持
为业务分析提供相关表结构信息：
```python
# 分析客户行为需要的表
result = await search_ddl_by_content("客户 行为 访问")
```

### 3. 数据建模参考
在进行数据建模时查找相关表结构：
```python
# 查找库存管理相关表
result = await search_ddl_by_content("库存 仓储 出入库")
```

## 注意事项

### 1. 搜索范围
- 仅搜索 `resources/odps_raw_tables/` 目录下的 `.md` 文件
- 不包括其他格式的DDL文件

### 2. 关键词建议
- 使用具体的业务术语效果更好
- 避免过于通用的词汇（如"表"、"数据"等）
- 可以组合使用多个相关关键词

### 3. 结果解读
- 相关度分数仅供参考，实际相关性需要结合业务判断
- 建议查看返回的前几个结果，通常最相关

## 与现有工具的区别

| 特性 | `fetch_ddl_for_table` | `search_ddl_by_content` |
|------|----------------------|------------------------|
| 搜索方式 | 精确表名匹配 | 内容模糊搜索 |
| 输入要求 | 必须知道确切表名 | 只需要业务关键词 |
| 返回结果 | 指定表的DDL | 多个相关表的DDL |
| 适用场景 | 已知表名查询DDL | 主题探索和发现 |
| 中文支持 | 不适用 | 完全支持 |

## 总结

DDL内容搜索工具为AI提供了一种全新的表结构发现方式，通过自然语言关键词搜索，可以快速找到与特定业务主题相关的所有表结构，大大提高了AI理解和分析业务数据的能力。
