# 会话 Store 使用说明（前端）

本文档简述 `conversationStore` 的职责、状态与常用方法（中文）。

## 职责
- 统一管理会话与消息（发送、占位、轮询、读取）。
- 提供分页历史与分组派生视图（供侧栏使用）。
- 管理分享/删除等对话级操作。

## 状态（只读 reactive）
- `state.activeCid: string|null` 当前活动会话ID。
- `state.messagesByCid: Map<string, Message[]>` 每个会话的消息列表。
- `state.conversations: Map<string, {id,title,lastTs}>` 会话索引，供分组排序。
- `state.isLoading/error` 聊天区用的加载/错误标记。
- `state.isHistoryLoading/historyError` 侧栏历史的加载/错误标记。
- `state.currentPage/pageSize/totalCount` 分页信息。
- `state.isShareModalOpen/shareUrl/isGeneratingShareLink` 分享模态框状态。
- `state.deleteConfirmModal` 删除确认模态状态。

## 派生（computed）
- `activeMessages` 当前活动会话的消息数组。
- `conversationGroups` 侧栏用的分组视图（今天/昨天/过去7天/过去30天/更早），组内按 lastTs 降序。
- `hasMorePages` 是否还有下一页历史。

## 方法
- `select(cid)` 选择会话（传 null 进入欢迎态）。
- `send({ text, images, agent, conversationId, uiId })` 发送消息：本地插入用户消息 + AI 占位；/query 返回后迁移临时ID并开始轮询。
- `loadHistory(page, limit)` 分页加载历史（仅 upsert，不覆盖）。
- `loadConversation(cid)` 加载单聊并设为活动会话。
- `loadFromUrl()` 解析 URL 中 `?chat=`，返回 { success, conversationId }。
- 分享相关：`shareConversation(cid)` / `closeShareModal()`。
- 删除相关：`openDeleteConfirmModal(cid,title)` / `confirmDeleteConversation()` / `closeDeleteConfirmModal()`。

## 约定
- 时间排序使用后端消息的 `updated_at`（优先）或 `timestamp`（毫秒），不做“点击置顶”。
- 历史加载状态与聊天区加载状态分离，互不影响。

