"""
API endpoints for sharing conversations.
"""
import os
from datetime import datetime

from flask import render_template, session, Blueprint, request, jsonify, url_for

from src.services.chatbot.sharing_service import get_shared_conversation_info, \
    get_shared_conversation_messages
from src.repositories.chatbi.conversation_sharing import create_share_link
from src.services.auth.user_login_with_feishu import login_required, HOST_NAME
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email
from src.utils.avatar_utils import get_user_avatar
from src.services.auth.user_admin_service import check_user_is_admin

# Create a Blueprint for share endpoints
# Using url_prefix='' to maintain original URL paths
share_bp = Blueprint('share', __name__, url_prefix='')


@share_bp.route('/api/share_conversation', methods=['POST'])
@login_required
def share_conversation_endpoint():
    """
    POST /api/share_conversation endpoint that generates a share link for a conversation.
    Request body: { "conversation_id": "xxx" }
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    if not username or not email:
        return jsonify({"error": "User information not found in session"}), 401

    data = request.get_json()
    if not data or "conversation_id" not in data:
        return jsonify({"error": "Missing conversation_id"}), 400

    conversation_id = data["conversation_id"]
    share_id = create_share_link(username, email, conversation_id)
    if not share_id:
        return jsonify({"error": "Failed to create share link"}), 500

    # Construct complete share URL
    share_url = url_for("share.view_shared_conversation", share_id=share_id, _external=True)
    return jsonify({"share_id": share_id, "share_url": share_url})


@share_bp.route('/share/<share_id>')
@login_required
def view_shared_conversation(share_id):
    """
    GET /share/<share_id> endpoint that renders a shared conversation.
    """
    user_info = session.get("user_info")
    user_name = user_info.get("name")
    user_id = user_info.get("union_id")
    user_email = get_valid_user_email(user_info)
    user_avatar_thumb = get_user_avatar(user_info, user_email)
    job_title = user_info.get("job_title")
    # 检查用户是否是管理员（通过union_id）
    is_admin = check_user_is_admin(user_id) if user_id else False

    share_info = get_shared_conversation_info(share_id)
    if not share_info:
        return "分享链接无效或已失效", 404

    # Check if current user is the owner
    is_owner = (user_name == share_info["owner_username"] and user_email == share_info[
        "owner_email"])
    is_shared = True

    # Load the owner's conversation content
    shared_conversation = get_shared_conversation_messages(share_id)

    # Get APPLICATION_ROOT from environment
    APPLICATION_ROOT = os.getenv("APPLICATION_ROOT", "/crm-chatbi")

    return render_template(
        "index.html",
        user_name=user_name,
        user_avatar_thumb=user_avatar_thumb,
        hostname=HOST_NAME,
        cache_control_timestamp=datetime.now().timestamp(),
        user_email=user_email,
        job_title=job_title,
        is_admin=is_admin,
        is_shared=is_shared,
        is_owner=is_owner,
        shared_conversation=shared_conversation,
        share_owner_name=share_info["owner_username"],
        share_id=share_id,
        app_root=APPLICATION_ROOT,  # Add app_root to the template context
    )
