import { ref, watch, onBeforeUnmount } from 'vue';
import { renderMarkdown, renderMarkdownLite } from '../../utils/MarkdownRenderer.js';

export default {
    name: 'TypewriterMarkdown',
    props: {
        content: { type: String, required: true },
        renderedContent: { type: String, default: '' },
        isStreaming: { type: Boolean, default: false },
        // 动画配置
        typewriterSpeed: { type: Number, default: 200 }, // 目标渲染速度（字符/秒）
        // 容器类
        containerClass: { type: String, default: 'markdown-content min-w-0' }
    },
    setup(props) {
        const renderedHtml = ref('');

        // 动画状态
        const targetContent = ref('');
        const visibleCount = ref(0);
        const isAnimating = ref(false);
        let rafId = null;
        let lastTs = 0;
        // 使用浮点累积以避免“每帧至少一个字符”的现象，确保按cps平滑推进
        let progressCount = 0;

        // 根据内容长度动态计算批次大小
        const getBatchSize = (totalLength) => {
            if (totalLength <= 100) return 3;      // 短内容：逐字显示
            if (totalLength <= 500) return 5;      // 中等内容：5字符一批
            if (totalLength <= 1000) return 10;     // 较长内容：10字符一批
            if (totalLength <= 2000) return 15;    // 长内容：15字符一批
            return Math.min(25, Math.max(15, Math.floor(totalLength / 100))); // 超长内容：动态调整，最多25字符一批
        };

        // 计算批次渲染速度（批次/秒）
        const getBatchesPerSecond = (total, forceFast) => {
            const targetSpeed = (typeof props.typewriterSpeed === 'number' && props.typewriterSpeed > 0) ? props.typewriterSpeed : 200; // 字符/秒
            const batchSize = getBatchSize(total);

            if (forceFast || !props.isStreaming) {
                // 非流式或强制快速：使用高速渲染（500字符/秒）
                const fastSpeed = Math.max(targetSpeed * 2.5, 500);
                return fastSpeed / batchSize;
            }

            // 流式阶段：根据剩余内容和目标速度计算
            const backlog = Math.max(0, total - visibleCount.value);
            if (backlog <= 0) {
                // 没有待渲染内容时，使用基础速度
                return Math.max(1, targetSpeed / batchSize / 4); // 降低到1/4速度等待新内容
            }

            // 根据目标字符速度计算批次速度
            const desiredBatchesPerSec = targetSpeed / batchSize;

            // 限制速度范围：最小0.5批次/秒，最大20批次/秒
            return Math.max(0.5, Math.min(20, desiredBatchesPerSec));
        };

        const stopAnimation = () => {
            if (rafId) cancelAnimationFrame(rafId);
            rafId = null;
            isAnimating.value = false;
        };

        const startAnimation = (forceFast = false) => {
            if (rafId) cancelAnimationFrame(rafId);
            isAnimating.value = true;
            lastTs = 0;
            // 保持已展示的字符数量，重置累积器为当前批次索引
            const currentBatchSize = getBatchSize(targetContent.value?.length || 0);
            progressCount = currentBatchSize > 0 ? Math.floor(visibleCount.value / currentBatchSize) : 0;

            // 如果目标内容为空，直接清空渲染内容
            if (!targetContent.value) {
                renderedHtml.value = '';
                isAnimating.value = false;
                return;
            }

            const step = (ts) => {
                if (!lastTs) lastTs = ts;
                const dt = (ts - lastTs) / 1000;
                lastTs = ts;

                const fullText = targetContent.value || '';
                const total = fullText.length;

                if (visibleCount.value < total) {
                    const batchSize = getBatchSize(total);
                    const batchesPerSecond = getBatchesPerSecond(total, forceFast);

                    // 计算基于批次的进度增量
                    progressCount += batchesPerSecond * dt;

                    // 计算下一个批次的结束位置
                    const nextBatchIndex = Math.floor(progressCount);
                    const nextCount = Math.min(total, (nextBatchIndex + 1) * batchSize);

                    if (nextCount > visibleCount.value) {
                        visibleCount.value = nextCount;
                        const partial = fullText.slice(0, visibleCount.value);
                        try {
                            // 流式阶段用轻量渲染，完成后再做完整高亮
                            renderedHtml.value = props.isStreaming ? renderMarkdownLite(partial) : renderMarkdown(partial);
                        } catch (e) {
                            renderedHtml.value = (partial || '').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br>');
                        }
                    }

                    rafId = requestAnimationFrame(step);
                } else {
                    // 动画完成：确保最终渲染是正确的
                    isAnimating.value = false;
                    rafId = null;

                    // 确保最终渲染内容与目标内容一致
                    if (!props.isStreaming && props.renderedContent) {
                        renderedHtml.value = props.renderedContent;
                    } else if (!props.isStreaming) {
                        renderedHtml.value = renderMarkdown(fullText);
                    } else {
                        // 流式状态下，确保显示完整的当前内容
                        try {
                            renderedHtml.value = renderMarkdownLite(fullText);
                        } catch (e) {
                            renderedHtml.value = (fullText || '').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br>');
                        }
                    }
                }
            };

            rafId = requestAnimationFrame(step);
        };

        const sync = () => {
            const contentStr = typeof props.content === 'string' ? props.content : (props.content ? JSON.stringify(props.content) : '');
            targetContent.value = contentStr;

            if (!props.isStreaming) {
                stopAnimation();
                visibleCount.value = contentStr.length;
                const batchSize = getBatchSize(contentStr.length);
                progressCount = batchSize > 0 ? Math.floor(contentStr.length / batchSize) : 0;
                renderedHtml.value = props.renderedContent || renderMarkdown(contentStr);
                return;
            }

            // 流式：处理内容变化
            if (visibleCount.value > contentStr.length) {
                // 新内容比当前显示的短，需要立即重新渲染较短的内容
                visibleCount.value = contentStr.length;
                const batchSize = getBatchSize(contentStr.length);
                progressCount = batchSize > 0 ? Math.floor(contentStr.length / batchSize) : 0;

                // 立即渲染新的较短内容，而不是等待动画循环
                if (contentStr.length === 0) {
                    // 内容为空时直接清空
                    renderedHtml.value = '';
                } else {
                    try {
                        renderedHtml.value = props.isStreaming ? renderMarkdownLite(contentStr) : renderMarkdown(contentStr);
                    } catch (e) {
                        renderedHtml.value = (contentStr || '').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br>');
                    }
                }
            }

            // 无论内容长短，都启动动画以处理可能的后续更新
            startAnimation(false);
        };

        watch(() => props.content, () => sync(), { immediate: true });
        watch(() => props.renderedContent, () => { if (!props.isStreaming) sync(); });
        watch(() => props.isStreaming, (nowStreaming) => {
            if (!nowStreaming) {
                if (targetContent.value) startAnimation(true); else stopAnimation();
            }
        });

        onBeforeUnmount(() => stopAnimation());

        return { renderedHtml };
    },
    template: `
        <div :data-skip-hljs="isStreaming ? 'true' : null" :class="containerClass" v-html="renderedHtml"></div>
    `
};
