/**
 * Chat State 兼容层（薄代理到 conversationStore）
 *
 * 重构后，聊天数据流完全由 conversationStore 接管。
 * 此文件仅为兼容历史导入，提供最小可用的字段与空操作方法。
 */
import { computed } from 'vue';
import { conversationStore } from '../store/conversationStore.js';

export function useChatState() {
  return {
    // 状态（映射到 Store 或空）
    messages: conversationStore.activeMessages,
    isLoadingMessages: computed(() => conversationStore.state.isLoading),
    messageError: computed(() => conversationStore.state.error),
    activeConversationId: computed(() => conversationStore.state.activeCid),
    streamingMessageId: { value: null },
    historyState: {},
    currentLogs: '',

    // 分享相关（映射到 Store）
    isShareModalOpen: computed(() => conversationStore.state.isShareModalOpen),
    shareUrl: computed(() => conversationStore.state.shareUrl),
    isGeneratingShareLink: computed(() => conversationStore.state.isGeneratingShareLink),

    // 方法（最小实现/空操作）
    loadConversationMessages: async (cid) => { if (cid) await conversationStore.loadConversation(cid); },
    selectConversation: (cid) => conversationStore.select(cid),
    newConversation: () => conversationStore.select(null),
    shareConversation: conversationStore.shareConversation,
    closeShareModal: conversationStore.closeShareModal,
    clearLogs: () => {},

    // 流式相关（由 Store 处理，这里保持空操作以兼容旧调用）
    addUserMessage: () => '',
    addAiMessage: () => '',
    updateStreamingMessage: () => {},
    completeStreamingMessage: () => {},
    handleMessageError: () => {},
    recoverStreamingMessages: () => {},
    pollingManager: {}
  };
}

