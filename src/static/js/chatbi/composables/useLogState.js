/**
 * Log State Composable
 *
 * Manages the state and logic for logs in the chat functionality
 * 使用单例模式确保状态在组件间共享
 */
import { ref } from 'vue';

// 在模块级别创建状态，确保单例
const currentLogs = ref(''); // 当前日志内容，HTML格式

/**
 * 处理历史消息中的日志
 * @param {Array} messages 消息数组
 */
const processHistoryLogs = (messages) => {
    // 收集所有助手消息的日志
    let accumulatedLogs = '';
    let hasLogs = false;

    // 遍历所有助手消息，累积日志
    messages.forEach(msg => {
        if (msg.role === 'assistant' && msg.logs) {
            hasLogs = true;
            if (typeof msg.logs === 'string') {
                // 字符串类型的日志（HTML）
                accumulatedLogs += msg.logs + '<br/><br/>';
            } else if (Array.isArray(msg.logs) && msg.logs.length > 0) {
                // 数组类型的日志
                // 将数组转换为HTML，确保与queryService.js中的样式一致
                const logsHtml = msg.logs.map(log => {
                    // 检查是否已经是HTML格式
                    if (log.startsWith('<p') && log.endsWith('</p>')) {
                        return log;
                    }
                    // 使用CSS类而不是内联样式
                    return `<p class="log-line">${log.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</p>`;
                }).join('\n');
                accumulatedLogs += logsHtml + '<br/>';
            }
        }
    });

    // 如果有累积的日志，设置到日志面板
    if (hasLogs) {
        currentLogs.value = accumulatedLogs;
    }
};

/**
 * 更新流式消息的日志
 * @param {Object} data 消息数据
 * @param {Object} message 当前消息对象
 */
const updateStreamingLogs = (data, message) => {
    // 处理日志 - 优先使用 logsHTML，因为它包含格式化的日志
    if (data.state && data.state.logsHTML && typeof data.state.logsHTML === 'string') {
        // 检查新日志是否包含当前日志
        if (typeof currentLogs.value === 'string' && currentLogs.value.length > 0) {
            if (data.state.logsHTML.includes(currentLogs.value)) {
                // 如果新日志包含当前日志，直接替换
                currentLogs.value = data.state.logsHTML;
            } else {
                // 如果新日志不包含当前日志，追加新日志
                currentLogs.value += data.state.logsHTML;
            }
        } else {
            // 如果当前日志为空，直接设置
            currentLogs.value = data.state.logsHTML;
        }

        // 同时保存到消息对象中，使用相同的逻辑
        if (typeof message.logs === 'string' && message.logs.length > 0) {
            if (data.state.logsHTML.includes(message.logs)) {
                message.logs = data.state.logsHTML;
            } else {
                message.logs += data.state.logsHTML;
            }
        } else {
            message.logs = data.state.logsHTML;
        }
    }
    // 如果没有 logsHTML：处理 logs（数组或字符串），并尽量去重
    else if (data.state && data.state.logs) {
        if (Array.isArray(data.state.logs)) {
            const newArr = data.state.logs;
            const newHtml = newArr.map(log => {
                if (typeof log === 'string' && log.startsWith('<p') && log.endsWith('</p>')) return log;
                return `<p class="log-line">${String(log).replace(/</g, "&lt;").replace(/>/g, "&gt;")}</p>`;
            }).join('\n');

            // 面板去重：若新HTML包含当前HTML（累积快照），替换；否则追加
            if (typeof currentLogs.value === 'string' && currentLogs.value.length > 0) {
                currentLogs.value = newHtml.includes(currentLogs.value) ? newHtml : (currentLogs.value + newHtml);
            } else {
                currentLogs.value = newHtml;
            }

            // 消息对象去重
            if (Array.isArray(message.logs)) {
                const oldArr = message.logs;
                const isPrefix = oldArr.length <= newArr.length && oldArr.every((v, i) => v === newArr[i]);
                message.logs = isPrefix ? newArr : [...oldArr, ...newArr.filter(x => !oldArr.includes(x))];
            } else if (typeof message.logs === 'string' && message.logs.length > 0) {
                message.logs = newHtml.includes(message.logs) ? newHtml : (message.logs + newHtml);
            } else {
                message.logs = newArr;
            }
        } else if (typeof data.state.logs === 'string') {
            const newStr = data.state.logs;
            // 面板去重：包含则替换，否则追加
            if (typeof currentLogs.value === 'string' && currentLogs.value.length > 0) {
                currentLogs.value = newStr.includes(currentLogs.value) ? newStr : (currentLogs.value + newStr);
            } else {
                currentLogs.value = newStr;
            }

            // 消息对象去重：字符串包含则替换，否则追加
            if (typeof message.logs === 'string' && message.logs.length > 0) {
                message.logs = newStr.includes(message.logs) ? newStr : (message.logs + newStr);
            } else if (Array.isArray(message.logs)) {
                const oldHtml = message.logs.map(log => (typeof log === 'string' && log.startsWith('<p') && log.endsWith('</p>'))
                    ? log
                    : `<p class="log-line">${String(log).replace(/</g, "&lt;").replace(/>/g, "&gt;")}</p>`).join('\n');
                message.logs = newStr.includes(oldHtml) ? newStr : (oldHtml + newStr);
            } else {
                message.logs = newStr;
            }
        }
    }

    return message;
};

/**
 * 清空日志
 */
const clearLogs = () => {
    currentLogs.value = '';
};

/**
 * 使用日志状态
 * @returns {Object} 日志状态和方法
 */
export function useLogState() {
    return {
        // 状态
        currentLogs,

        // 方法
        processHistoryLogs,
        updateStreamingLogs,
        clearLogs
    };
}
