/**
 * Agent配置文件
 * 
 * 这个文件定义了所有可用的AI代理及其配置信息，
 * 包括显示名称、描述、颜色主题、输入提示等。
 * 
 * 添加新的Agent只需要在这个文件中添加配置即可，
 * 无需修改ChatInput.js中的硬编码逻辑。
 */

export const AGENT_CONFIG = {
    // 通用问答代理
    general_chat_bot: {
        id: 'general_chat_bot',
        name: '问答',
        title: '鲜沐AI，可以回答一切和鲜沐有关的问题',
        placeholder: '鲜沐AI，可以回答一切和鲜沐有关的问题，不过不支持取数。请输入您的问题，或粘贴图片...',
        footerText: '问答模式使用中',
        theme: {
            color: 'blue',
            borderClass: 'border-blue-300',
            activeClass: 'bg-blue-50 border-blue-300 text-blue-600'
        },
        order: 1
    },

    // 深度研究分析代理
    deep_research_agent: {
        id: 'deep_research_agent',
        name: '深度分析',
        title: '深度分析鲜沐的业务数据，获取业务洞察',
        placeholder: '深度分析鲜沐的业务数据，获取业务洞察。请输入您的问题，或粘贴图片...',
        footerText: '深度分析已启动',
        theme: {
            color: 'purple',
            borderClass: 'border-purple-300',
            activeClass: 'bg-purple-50 border-purple-300 text-purple-600'
        },
        order: 2
    },

    // ODPS自动驾驶代理
    odps_autopilot_agent: {
        id: 'odps_autopilot_agent',
        name: '数仓Autopilot',
        title: '数仓自动驾驶，智能发现相关表结构并执行数据分析',
        placeholder: '数仓自动驾驶，智能发现相关表结构并执行数据分析。请输入您的问题，或粘贴图片...',
        footerText: '数仓Autopilot已启动',
        theme: {
            color: 'orange',
            borderClass: 'border-orange-300',
            activeClass: 'bg-orange-50 border-orange-300 text-orange-600'
        },
        order: 3
    }
};

/**
 * 获取所有可用的Agent配置
 * @returns {Array} 按order排序的Agent配置数组
 */
export function getAvailableAgents() {
    // 如果职务是“销售专员”，则不允许使用数仓Autopilot
    const jobTitle = window.userInfo.jobTitle;
    if (jobTitle === '销售专员') {
        console.log('jobTitle', jobTitle,'您不能使用数仓Autopilot');
        return Object.values(AGENT_CONFIG).filter(agent => agent.id !== 'odps_autopilot_agent').sort((a, b) => a.order - b.order);
    }else{
        console.log('jobTitle', jobTitle,'您可使用数仓Autopilot');
        return Object.values(AGENT_CONFIG).sort((a, b) => a.order - b.order);
    }
}

/**
 * 根据ID获取Agent配置
 * @param {string} agentId - Agent ID
 * @returns {Object|null} Agent配置对象，如果不存在则返回null
 */
export function getAgentConfig(agentId) {
    return AGENT_CONFIG[agentId] || null;
}

/**
 * 获取Agent的边框样式类
 * @param {string} agentId - Agent ID
 * @returns {string} CSS类名
 */
export function getAgentBorderClass(agentId) {
    const config = getAgentConfig(agentId);
    return config ? config.theme.borderClass : 'border-base-300';
}

/**
 * 获取Agent的placeholder文本
 * @param {string} agentId - Agent ID
 * @returns {string} placeholder文本
 */
export function getAgentPlaceholder(agentId) {
    const config = getAgentConfig(agentId);
    return config ? config.placeholder : '输入您的问题或粘贴图片...';
}

/**
 * 获取Agent的底部文案
 * @param {string} agentId - Agent ID
 * @returns {string} 底部文案
 */
export function getAgentFooterText(agentId) {
    const config = getAgentConfig(agentId);
    return config ? config.footerText : 'Autopilot';
}

/**
 * 获取Agent按钮的样式类
 * @param {string} agentId - Agent ID
 * @param {boolean} isSelected - 是否被选中
 * @returns {Array} CSS类名数组
 */
export function getAgentButtonClasses(agentId, isSelected) {
    const baseClasses = [
        'px-2 py-1 text-xs rounded-md transition-all duration-75 border',
        'hover:bg-base-200 text-base-content/70 hover:text-base-content',
        'flex items-center justify-center font-medium'
    ];

    if (isSelected) {
        const config = getAgentConfig(agentId);
        if (config) {
            baseClasses.push(config.theme.activeClass);
        } else {
            baseClasses.push('bg-base-100 border-base-400');
        }
    } else {
        baseClasses.push('bg-transparent border-base-300');
    }

    return baseClasses;
}

/**
 * 检查Agent是否存在
 * @param {string} agentId - Agent ID
 * @returns {boolean} 是否存在
 */
export function isValidAgent(agentId) {
    return agentId in AGENT_CONFIG;
}

/**
 * 获取默认Agent ID
 * @returns {string} 默认Agent ID
 */
export function getDefaultAgent() {
    // 返回order最小的Agent作为默认Agent
    const agents = getAvailableAgents();
    return agents.length > 0 ? agents[0].id : null;
}
