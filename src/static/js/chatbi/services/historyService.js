/**
 * 历史/标记/分享 Service（前端）
 *
 * 注意：分页/单聊加载已迁移至 conversationStore，此处仅保留对话级操作接口。
 */

/**
 * 实际执行获取历史记录的函数
 *
 * @param {number} page - 页码（从1开始）
 * @param {number} limit - 每页条数
 * @returns {Promise<Object>} - Promise解析为 { history: Object, total_count: number }
 */
async function _fetchHistoryImpl(page = 1, limit = 20) {
    try {
        // 计算偏移量
        const offset = (page - 1) * limit;

        // 发起API请求
        const response = await fetch(`/api/history?limit=${limit}&offset=${offset}`);

        // 检查是否被重定向到登录页面
        if (response.url.includes('/login')) {
            throw new Error('用户未登录，请先登录');
        }

        // 检查响应是否正常
        if (!response.ok) {
            try {
                const errorData = await response.json();
                throw new Error(errorData.error || '获取历史记录失败');
            } catch (jsonError) {
                // 如果响应不是JSON格式
                throw new Error(`获取历史记录失败: ${response.status} ${response.statusText}`);
            }
        }

        // 解析并返回数据
        const data = await response.json();

        // 获取历史记录数据
        const historyData = data.history || {};

        return {
            history: historyData,
            total_count: data.total_count || 0
        };
    } catch (error) {
        console.error('[HistoryService] 获取历史记录时出错:', error);
        throw error;
    }
}

// （已废弃）历史分页获取由 conversationStore 直接实现

/**
 * 获取会话历史记录（带分页）
 * 使用防抖机制避免短时间内重复请求
 *
 * @param {number} page - 页码（从1开始）
 * @param {number} limit - 每页条数
 * @returns {Promise<Object>} - Promise解析为 { history: Object, total_count: number }
 */
// export async function fetchHistory() { throw new Error('请改用 conversationStore.loadHistory'); }

/**
 * 获取单个对话的消息内容
 * 直接调用 /api/history?chat=xxx 接口获取单个对话
 *
 * @param {string} conversationId - 对话ID
 * @returns {Promise<Array>} - Promise解析为对话消息数组
 */
// export async function fetchConversation() { throw new Error('请改用 conversationStore.loadConversation'); }

/**
 * Delete a conversation by ID
 *
 * @param {string} conversationId - The ID of the conversation to delete
 * @returns {Promise<Object>} - Promise resolving to the API response
 */
export async function deleteConversation(conversationId) {
    try {
        // Make API request
        const response = await fetch(`/api/history/${conversationId}`, {
            method: 'DELETE'
        });

        // Check if response is ok
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '删除对话失败');
        }

        // Parse and return the data
        return await response.json();
    } catch (error) {
        console.error('删除对话时出错:', error);
        throw error;
    }
}

/**
 * Mark or unmark a conversation as a bad case
 *
 * @param {string} conversationId - The ID of the conversation to mark
 * @param {boolean} isBadCase - Whether to mark (true) or unmark (false) the conversation
 * @returns {Promise<Object>} - Promise resolving to the API response
 */
export async function markConversationAsBadCase(conversationId, isBadCase = true) {
    try {
        // Make API request
        const response = await fetch('/api/mark_conversation_as_bad_case', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                conversation_id: conversationId,
                is_bad_case: isBadCase
            })
        });

        // Check if response is ok
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '标记不良案例失败');
        }

        // Parse and return the data
        return await response.json();
    } catch (error) {
        console.error('标记不良案例时出错:', error);
        throw error;
    }
}

/**
 * Mark or unmark a conversation as a good case
 *
 * @param {string} conversationId - The ID of the conversation to mark
 * @param {boolean} isGoodCase - Whether to mark (true) or unmark (false) the conversation
 * @returns {Promise<Object>} - Promise resolving to the API response
 */
export async function markConversationAsGoodCase(conversationId, isGoodCase = true) {
    try {
        // Make API request
        const response = await fetch('/api/mark_conversation_as_good_case', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                conversation_id: conversationId,
                is_good_case: isGoodCase
            })
        });

        // Check if response is ok
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '标记Good Case失败');
        }

        // Parse and return the data
        return await response.json();
    } catch (error) {
        console.error('标记Good Case时出错:', error);
        throw error;
    }
}

/**
 * Mark or unmark a specific chat history message as a bad case
 *
 * @param {number} chatHistoryId - The ID of the chat history message to mark
 * @param {boolean} isBadCase - Whether to mark (true) or unmark (false) the message
 * @param {string} conversationId - The conversation ID (required for temporary ID mapping)
 * @returns {Promise<Object>} - Promise resolving to the API response
 */
export async function markChatHistoryAsBadCase(chatHistoryId, isBadCase = true, conversationId = null) {
    try {
        const requestBody = {
            chat_history_id: chatHistoryId,
            is_bad_case: isBadCase
        };

        // 如果提供了conversationId，添加到请求中（用于临时ID映射）
        if (conversationId) {
            requestBody.conversation_id = conversationId;
        }

        // Make API request
        const response = await fetch('/api/mark_chat_history_as_bad_case', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        // Check if response is ok
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '标记消息为不良案例失败');
        }

        return await response.json();
    } catch (error) {
        console.error('Error marking chat history as bad case:', error);
        throw error;
    }
}

/**
 * Mark or unmark a specific chat history message as a good case
 *
 * @param {number} chatHistoryId - The ID of the chat history message to mark
 * @param {boolean} isGoodCase - Whether to mark (true) or unmark (false) the message
 * @param {string} conversationId - The conversation ID (required for temporary ID mapping)
 * @returns {Promise<Object>} - Promise resolving to the API response
 */
export async function markChatHistoryAsGoodCase(chatHistoryId, isGoodCase = true, conversationId = null) {
    try {
        const requestBody = {
            chat_history_id: chatHistoryId,
            is_good_case: isGoodCase
        };

        // 如果提供了conversationId，添加到请求中（用于临时ID映射）
        if (conversationId) {
            requestBody.conversation_id = conversationId;
        }

        // Make API request
        const response = await fetch('/api/mark_chat_history_as_good_case', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        // Check if response is ok
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '标记消息为Good Case失败');
        }

        return await response.json();
    } catch (error) {
        console.error('Error marking chat history as good case:', error);
        throw error;
    }
}

/**
 * Submit feedback for a conversation rating
 *
 * @param {string} conversationId - The ID of the conversation
 * @param {string} ratingType - Type of rating ('positive' or 'negative')
 * @param {Object} feedbackData - Feedback data object
 * @param {Array} feedbackData.selectedTags - Array of selected feedback tags
 * @param {string} feedbackData.customFeedback - Custom feedback text
 * @returns {Promise<Object>} - Promise resolving to the API response
 */
export async function submitConversationFeedback(conversationId, ratingType, feedbackData) {
    try {
        // 确定API端点
        const endpoint = ratingType === 'positive'
            ? '/api/mark_conversation_as_good_case'
            : '/api/mark_conversation_as_bad_case';

        // 构建请求负载
        const payload = {
            conversation_id: conversationId,
            [ratingType === 'positive' ? 'is_good_case' : 'is_bad_case']: true,
            feedback_tags: feedbackData.selectedTags || [],
            custom_feedback: feedbackData.customFeedback || ''
        };

        // 发送API请求
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });

        // 检查响应状态
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '提交反馈失败');
        }

        // 解析并返回数据
        return await response.json();
    } catch (error) {
        console.error('提交反馈时出错:', error);
        throw error;
    }
}

/**
 * Get feedback data for a conversation
 *
 * @param {string} conversationId - The ID of the conversation
 * @param {string} ratingType - Type of rating ('positive' or 'negative')
 * @returns {Promise<Object|null>} - Promise resolving to feedback data or null if no feedback exists
 */
export async function getConversationFeedback(conversationId, ratingType) {
    try {
        // 注意：这里需要后端提供专门的获取反馈数据的API端点
        // 目前我们可以先返回null，表示没有现有反馈数据
        // 在实际实现中，可能需要扩展现有的API或创建新的端点

        // TODO: 实现获取反馈数据的API调用
        console.log(`获取会话 ${conversationId} 的 ${ratingType} 反馈数据`);
        return null;
    } catch (error) {
        console.error('获取反馈数据时出错:', error);
        return null;
    }
}

// Note: We don't need a separate function to fetch conversation messages
// as we use the history data that's already loaded in the useHistoryState composable.
