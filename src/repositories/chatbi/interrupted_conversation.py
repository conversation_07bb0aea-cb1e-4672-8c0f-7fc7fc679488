"""
被中断对话仓储模块

提供被中断对话的数据访问功能，遵循DDD架构原则
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Dict, Any, Optional
from mysql.connector import Error

from src.utils.logger import logger
from src.db.connection import execute_db_query


class InterruptedConversationRepository(ABC):
    """被中断对话仓储接口"""
    
    @abstractmethod
    def find_interrupted_assistant_messages(self, app_startup_time: datetime) -> List[Dict[str, Any]]:
        """查找被中断的assistant消息"""
        pass
    
    @abstractmethod
    def update_interrupted_message(self, message_id: int, new_content: str) -> bool:
        """更新被中断的消息状态和内容"""
        pass
    
    @abstractmethod
    def count_interrupted_messages(self, app_startup_time: datetime) -> int:
        """获取被中断消息的数量"""
        pass
    
    @abstractmethod
    def find_conversation_assistant_messages(self, conversation_id: str, app_startup_time: datetime) -> List[Dict[str, Any]]:
        """查找特定对话的assistant消息状态"""
        pass


class ChatbiInterruptedConversationRepository(InterruptedConversationRepository):
    """被中断对话仓储类"""
    
    def find_interrupted_assistant_messages(self, app_startup_time: datetime) -> List[Dict[str, Any]]:
        """
        查找被中断的assistant消息
        
        查找条件：
        1. role = 'assistant' 
        2. is_in_process = 1 (正在处理中)
        3. created_at < 应用启动时间
        
        Args:
            app_startup_time (datetime): 应用启动时间
            
        Returns:
            List[Dict[str, Any]]: 被中断的消息列表
        """
        sql = """
            SELECT id, conversation_id, username, email, content, created_at, agent
            FROM chat_history 
            WHERE role = 'assistant' 
                AND is_in_process = 1 
                AND created_at < %s
            ORDER BY conversation_id, created_at ASC
        """
        
        try:
            results = execute_db_query(sql, (app_startup_time,), fetch='all')
            logger.debug(f"查询被中断对话SQL: {sql}")
            logger.debug(f"查询参数: 应用启动时间={app_startup_time}")
            logger.debug(f"查询结果数量: {len(results) if results else 0}")
            
            return results if results else []
            
        except Error as e:
            logger.exception(f"查找被中断对话时发生数据库错误: {e}", exc_info=True)
            return []
        except Exception as e:
            logger.exception(f"查找被中断对话时发生意外错误: {e}", exc_info=True)
            return []
    
    def update_interrupted_message(self, message_id: int, new_content: str) -> bool:
        """
        更新被中断的消息状态和内容
        
        Args:
            message_id (int): 消息ID
            new_content (str): 新的消息内容
            
        Returns:
            bool: 更新是否成功
        """
        update_sql = """
            UPDATE chat_history 
            SET content = %s, 
                is_in_process = 0, 
                updated_at = CURRENT_TIMESTAMP
            WHERE id = %s AND role = 'assistant' AND is_in_process = 1
        """
        
        try:
            affected_rows = execute_db_query(
                update_sql, 
                (new_content, message_id), 
                commit=True, 
                fetch='count'
            )
            
            if affected_rows > 0:
                logger.debug(f"成功更新被中断消息 ID: {message_id}")
                return True
            else:
                logger.warning(f"未能更新被中断消息 ID: {message_id}，可能已被其他进程处理")
                return False
                
        except Error as e:
            logger.exception(f"更新被中断消息时发生数据库错误 ID: {message_id}: {e}", exc_info=True)
            return False
        except Exception as e:
            logger.exception(f"更新被中断消息时发生意外错误 ID: {message_id}: {e}", exc_info=True)
            return False
    
    def count_interrupted_messages(self, app_startup_time: datetime) -> int:
        """
        获取被中断消息的数量
        
        Args:
            app_startup_time (datetime): 应用启动时间
            
        Returns:
            int: 被中断消息数量
        """
        sql = """
            SELECT COUNT(*) as count
            FROM chat_history 
            WHERE role = 'assistant' 
                AND is_in_process = 1 
                AND created_at < %s
        """
        
        try:
            result = execute_db_query(sql, (app_startup_time,), fetch='one')
            count = result.get('count', 0) if result else 0
            logger.debug(f"被中断消息数量: {count}")
            return count
            
        except Exception as e:
            logger.exception(f"获取被中断消息数量时发生错误: {e}", exc_info=True)
            return 0
    
    def find_conversation_assistant_messages(self, conversation_id: str, app_startup_time: datetime) -> List[Dict[str, Any]]:
        """
        查找特定对话的assistant消息状态
        
        Args:
            conversation_id (str): 对话ID
            app_startup_time (datetime): 应用启动时间
            
        Returns:
            List[Dict[str, Any]]: assistant消息列表
        """
        sql = """
            SELECT id, role, content, is_in_process, created_at, updated_at
            FROM chat_history 
            WHERE conversation_id = %s 
                AND role = 'assistant'
            ORDER BY created_at DESC
            LIMIT 5
        """
        
        try:
            results = execute_db_query(sql, (conversation_id,), fetch='all')
            return results if results else []
            
        except Exception as e:
            logger.exception(f"查找对话assistant消息时发生错误: {e}", exc_info=True)
            return []


# 创建仓储实例的工厂函数
def create_interrupted_conversation_repository() -> InterruptedConversationRepository:
    """创建被中断对话仓储实例"""
    return ChatbiInterruptedConversationRepository()