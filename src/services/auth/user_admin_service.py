"""
用户管理员权限服务

提供用户管理员权限检查功能，遵循DDD架构模式
"""

from typing import Optional
from src.repositories.chatbi.user import UserRepository, ChatbiUserRepository
from src.utils.logger import logger


class UserAdminService:
    """用户管理员权限服务类"""
    
    def __init__(self, user_repository: Optional[UserRepository] = None):
        """
        初始化用户管理员权限服务
        
        Args:
            user_repository: 用户仓储接口实现，如果为None则使用默认实现
        """
        self.user_repository = user_repository or ChatbiUserRepository()
    
    def check_user_is_admin(self, union_id: str) -> bool:
        """
        检查用户是否为管理员
        
        Args:
            union_id: 飞书用户的union_id
            
        Returns:
            bool: 是否为管理员
        """
        if not union_id:
            return False
        
        try:
            return self.user_repository.is_admin_by_union_id(union_id)
        except Exception as e:
            logger.exception(f"检查用户管理员权限失败 union_id: {union_id}: {e}")
            return False


# 全局服务实例
user_admin_service = UserAdminService()


def check_user_is_admin(union_id: str) -> bool:
    """
    检查用户是否为管理员的便捷函数
    
    Args:
        union_id: 飞书用户的union_id
        
    Returns:
        bool: 是否为管理员
    """
    return user_admin_service.check_user_is_admin(union_id)
