"""
Good case service module.

This module provides business logic for managing good cases.
"""

import functools
import concurrent.futures
from typing import Optional

from src.utils.logger import logger
from src.repositories.chatbi.good_case import (
    get_conversation_good_case_status,
)

# 创建专用线程池用于异步发送飞书通知
_NOTIFICATION_EXECUTOR = concurrent.futures.ThreadPoolExecutor(
    max_workers=5,  # 通知发送不需要太多线程
    thread_name_prefix="good_case_notification_worker"
)


# 已废弃：这些函数已不再使用，因为统一使用消息级通知机制避免重复通知
# def feishu_notification_decorator(func):
#     """
#     装饰器：在标记 good case 后异步发送飞书通知
#     已废弃：为了避免多轮对话时的重复通知问题，现在统一使用消息级通知机制
#     """
#     pass

# def _send_good_case_notification_async(conversation_id: str, is_good_case: bool, user_name: Optional[str]) -> None:
#     """
#     异步发送Good Case通知到飞书群聊（在线程池中执行）
#     已废弃：为了避免多轮对话时的重复通知问题，现在统一使用消息级通知机制
#     """
#     pass


def _send_good_case_notification_for_chat_history_async(chat_history_id: int, is_good_case: bool, user_name: str = None):
    """
    异步发送基于chat_history_id的Good Case标记/取消标记通知

    Args:
        chat_history_id (int): chat history ID
        is_good_case (bool): 是否为good case
        user_name (Optional[str]): 标记用户名
    """
    try:
        # 获取chat_history记录的详细信息
        from src.repositories.chatbi.history import get_chat_history_by_id
        chat_history = get_chat_history_by_id(chat_history_id)

        if not chat_history:
            logger.warning(f"Chat history {chat_history_id} not found for notification")
            return

        conversation_id = chat_history.get('conversation_id')

        # 获取该chat_history_id对应的用户消息和assistant回复
        from src.utils.conversation_utils import get_chat_history_context_for_notification
        context = get_chat_history_context_for_notification(chat_history_id)

        if not context:
            logger.warning(f"No context found for chat history {chat_history_id}")
            return

        user_message_content = context.get('user_message')
        assistant_message_content = context.get('assistant_message')

        # 获取对话所属用户信息
        from src.repositories.chatbi.history import check_conversation_owner
        conversation_owner = check_conversation_owner(conversation_id)
        conversation_user_name = None
        if conversation_owner:
            conversation_user_name = conversation_owner.get('username')

        # 导入飞书通知函数（延迟导入避免循环依赖）
        from src.services.feishu.message_apis import send_good_case_notification, send_good_case_unmark_notification

        notification_func = send_good_case_notification if is_good_case else send_good_case_unmark_notification
        action = "标记" if is_good_case else "取消标记"

        # 发送通知，传递chat_history_id相关的消息内容
        notification_sent = notification_func(
            conversation_id,
            user_name,  # 标记用户
            user_message_content,  # 用户消息内容
            conversation_user_name,  # 对话用户
            assistant_message_content,  # assistant回复内容
            chat_history_id=chat_history_id  # 传递chat_history_id用于通知显示
        )

        if notification_sent:
            logger.info(f"异步发送Good Case{action}通知成功，chat_history_id: {chat_history_id}")
        else:
            logger.warning(f"异步发送Good Case{action}通知失败，chat_history_id: {chat_history_id}")

    except Exception as e:
        action = "标记" if is_good_case else "取消标记"
        logger.exception(f"异步发送Good Case{action}通知时发生异常: {str(e)}", exc_info=True)


def mark_good_case(conversation_id: str, is_good_case: bool = True, user_name: str = None,
                  feedback_tags: list = None, custom_feedback: str = None, chat_history_id: int = None, **kwargs) -> bool:
    """
    Mark or unmark a conversation as a good case.

    Args:
        conversation_id (str): The ID of the conversation
        is_good_case (bool, optional): Whether to mark as good case (True) or unmark (False). Defaults to True.
        user_name (str, optional): The name of the user who marked the good case. Used for notification. Defaults to None.
        feedback_tags (list, optional): List of selected feedback tags. Defaults to None.
        custom_feedback (str, optional): Custom feedback text from user. Defaults to None.
        chat_history_id (int, optional): The ID of the chat history message to mark. Defaults to None.
        **kwargs: 接受任意额外的关键字参数（如card_id等），用于兼容事件处理器传递的额外参数

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    # 参数验证
    if not conversation_id or not isinstance(conversation_id, str):
        logger.exception(f"Invalid conversation_id: {conversation_id}")
        return False

    action = "marked" if is_good_case else "unmarked"
    feedback_info = ""
    if feedback_tags or custom_feedback:
        feedback_info = f" with feedback (tags: {feedback_tags}, text: {bool(custom_feedback)})"
    logger.info(f"User {action} conversation {conversation_id} as good case, user_name: {user_name}{feedback_info}")



    # 消息级别good/bad case标记实现
    try:
        if chat_history_id is None:
            # 向后 Compatibility：如果从conversation_id调用，需要查找对应的消息ID
            from src.services.chatbot.history_service import get_latest_assistant_message_id
            chat_history_id = get_latest_assistant_message_id(conversation_id, user_name)
            
            if not chat_history_id:
                logger.exception("无法找到对应的assistant消息ID进行标记")
                return False
        
        # 直接使用新的CaseRepository接口，避免废弃警告
        from src.repositories.chatbi.case_repository import CaseRepository
        success = CaseRepository.mark_chat_history_as_case(
            chat_history_id=chat_history_id,
            case_type='good',
            marked_by=user_name,
            feedback_tags=feedback_tags,
            custom_feedback=custom_feedback
        )

        # 如果标记成功，发送通知（统一使用消息级通知机制）
        if success:
            try:
                _NOTIFICATION_EXECUTOR.submit(
                    _send_good_case_notification_for_chat_history_async,
                    chat_history_id,
                    is_good_case,
                    user_name
                )
                logger.info(f"已异步提交Good Case通知任务，chat_history_id: {chat_history_id}")
            except Exception as e:
                logger.warning(f"通知任务提交失败: {str(e)}")

        return success
    except Exception as e:
        logger.exception(f"Error marking message as good case for chat_history_id: {chat_history_id}, {str(e)}", exc_info=True)
        return False


def mark_good_case_by_chat_history_id(chat_history_id: int, is_good_case: bool = True, user_name: str = None,
                                    feedback_tags: list = None, custom_feedback: str = None, **kwargs) -> bool:
    """
    Mark or unmark a specific chat history message as a good case.

    Args:
        chat_history_id (int): The ID of the chat history message
        is_good_case (bool, optional): Whether to mark as good case (True) or unmark (False). Defaults to True.
        user_name (str, optional): The name of the user who marked the good case. Used for notification. Defaults to None.
        feedback_tags (list, optional): List of selected feedback tags. Defaults to None.
        custom_feedback (str, optional): Custom feedback text from user. Defaults to None.
        **kwargs: 接受任意额外的关键字参数（如card_id等），用于兼容事件处理器传递的额外参数

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    # 参数验证
    if not chat_history_id or not isinstance(chat_history_id, int) or chat_history_id <= 0:
        logger.exception(f"Invalid chat_history_id: {chat_history_id}")
        return False

    action = "marked" if is_good_case else "unmarked"
    feedback_info = ""
    if feedback_tags or custom_feedback:
        feedback_info = f" with feedback (tags: {feedback_tags}, text: {bool(custom_feedback)})"
    logger.info(f"User {action} chat history {chat_history_id} as good case, user_name: {user_name}{feedback_info}")



    # 使用新一代CaseRepository架构
    try:
        from src.repositories.chatbi.case_repository import CaseRepository
        
        if is_good_case:
            success = CaseRepository.mark_chat_history_as_case(
                chat_history_id=chat_history_id,
                case_type='good',
                marked_by=user_name,
                feedback_tags=feedback_tags,
                custom_feedback=custom_feedback
            )
        else:
            success = CaseRepository.unmark_chat_history_as_case(
                chat_history_id=chat_history_id,
                case_type='good'
            )
            
    except Exception as e:
        logger.exception(f"标记chat_history {chat_history_id}为good case时出错: {e}")
        return False

    if success:
        try:
            _NOTIFICATION_EXECUTOR.submit(
                _send_good_case_notification_for_chat_history_async,
                chat_history_id,
                is_good_case,
                user_name
            )
            logger.info(f"已异步提交Good Case通知任务，chat_history_id: {chat_history_id}")
        except Exception as e:
            logger.warning(f"通知任务提交失败: {str(e)}")

    return success


def is_good_case(conversation_id: str, username: Optional[str] = None, email: Optional[str] = None) -> bool:
    """
    Check if a conversation is marked as a good case.

    Args:
        conversation_id (str): The ID of the conversation
        username (str, optional): The username to filter by. Defaults to None (kept for API compatibility).
        email (str, optional): The email to filter by. Defaults to None (kept for API compatibility).

    Returns:
        bool: True if the conversation is marked as a good case, False otherwise
    """
    logger.info(f"Checking if conversation {conversation_id} is a good case")
    # Note: username and email parameters are kept for API compatibility but not used
    # since the good_case table design doesn't require user filtering
    return get_conversation_good_case_status(conversation_id)
