"""
Bad Case 重试服务模块
处理用户点击badcase按钮后的自动重试机制
"""

import asyncio
import threading
from typing import Optional

from src.utils.logger import logger
from src.services.chatbot.bad_case_service import mark_bad_case_by_chat_history_id
from src.services.chatbot.history_service import save_user_message
from src.repositories.chatbi.history import get_chat_history_by_id
from src.services.feishu.query_processor import FeishuQueryProcessor
from src.services.chatbot.sku_choice_service import _get_real_user_info_from_chat_history_sync


def handle_bad_case_with_retry(
    conversation_id: str,
    user_name: str,
    chat_history_id: int = None,
    open_message_id: str = None,
    **kwargs
) -> bool:
    """处理badcase标记并启动重试机制
    
    Args:
        conversation_id: 对话ID
        user_name: 用户名
        chat_history_id: 聊天历史ID
        open_message_id: 飞书原始消息ID
        **kwargs: 其他参数
        
    Returns:
        bool: 处理是否成功
    """
    try:
        logger.info(f"开始处理badcase重试 - user: {user_name}, conversation_id: {conversation_id}, chat_history_id: {chat_history_id}")
        
        # 1. 首先执行原有的badcase标记功能
        success = mark_bad_case_by_chat_history_id(
            chat_history_id=chat_history_id,
            is_bad_case=True,
            user_name=user_name
        )
        
        if not success:
            logger.error(f"标记badcase失败，无法继续重试流程")
            return False
            
        # 2. 获取用户信息用于写入重试消息
        if not chat_history_id:
            logger.error("缺少chat_history_id，无法获取用户信息")
            return False
            
        chat_record = get_chat_history_by_id(chat_history_id)
        if not chat_record:
            logger.error(f"无法找到chat_history记录: {chat_history_id}")
            return False
            
        user_email = chat_record.get('email')
        username = chat_record.get('username')
        
        if not user_email or not username:
            logger.error(f"chat_history记录缺少用户信息: {chat_record}")
            return False
            
        # 3. 写入重试提示消息到chat_history
        retry_message = "数据不对哦，请你仔细检查边界条件，充分考虑之后再试试。"
        
        message_saved = save_user_message(
            username=username,
            email=user_email,
            conversation_id=conversation_id,
            content=retry_message
        )
        
        if not message_saved:
            logger.error("保存重试消息失败")
            return False
            
        logger.info(f"已保存重试消息到chat_history: {retry_message}")
        
        # 4. 使用query_processor处理重试消息，就像用户发送了这条消息一样
        if not open_message_id:
            logger.warning("缺少open_message_id，无法处理重试查询")
            return True  # badcase标记已成功，只是无法处理重试

        # 构建用户信息字典，获取真实的open_id（复用SKU选择器的函数）
        user_info_dict = _get_real_user_info_from_chat_history_sync(chat_history_id, username)

        # 在后台线程中异步处理重试查询，就像SKU选择器一样
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(_process_retry_query(
                open_message_id, retry_message, user_info_dict, conversation_id
            ))
        except RuntimeError:
            # 没有运行中的事件循环，在后台线程中运行
            threading.Thread(
                target=lambda: asyncio.run(
                    _process_retry_query(open_message_id, retry_message, user_info_dict, conversation_id)
                ),
                daemon=True
            ).start()

        logger.info(f"badcase重试流程启动成功 - conversation_id: {conversation_id}")
        return True
        
    except Exception as e:
        logger.exception(f"处理badcase重试时出错: {e}")
        return False


async def _process_retry_query(
    open_message_id: str,
    retry_message: str,
    user_info_dict: dict,
    conversation_id: str
):
    """处理重试查询的异步函数，就像SKU选择器一样

    Args:
        open_message_id: 飞书原始消息ID
        retry_message: 重试消息内容
        user_info_dict: 用户信息字典
        conversation_id: 对话ID
    """
    try:
        logger.info(f"开始处理badcase重试查询 - open_message_id: {open_message_id}, message: {retry_message}")

        # 直接调用query_processor处理重试消息，就像用户发送了这条消息一样
        await FeishuQueryProcessor.handle_agent_query(
            message_id=open_message_id,
            user_query=retry_message,
            user_info_dict=user_info_dict,
            root_id=None,
            parent_id=None,
            image_url=None,
            conversation_id=conversation_id
        )

        logger.info(f"badcase重试查询处理完成 - open_message_id: {open_message_id}")

    except Exception as e:
        logger.exception(f"处理badcase重试查询时出错: {e}")



