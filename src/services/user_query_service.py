"""
用户查询应用服务模块

提供用户信息查询功能，包括：
- 通过用户姓名或邮箱查询 open_id 和相关用户信息
- 通过 open_id 获取完整用户信息（包括avatar处理、api_token等）
遵循DDD架构模式：API层 → 应用服务 → 领域服务 → 仓储层 → 数据库
"""

import json
from typing import Dict, Any, Optional, List
from src.services.domain.user_domain_service import UserDomainService
from src.repositories.chatbi.user import UserRepository, ChatbiUserRepository
from src.models.user_info_class import User
from src.utils.logger import logger
from src.utils.in_memory_cache import in_memory_cache
from src.utils.user_utils import get_api_token


class UserQueryService:
    """用户查询应用服务类 - 协调领域服务和仓储层"""

    def __init__(self, user_repository: Optional[UserRepository] = None):
        """
        初始化应用服务，注入依赖

        Args:
            user_repository: 用户仓储接口实现，如果为None则使用默认实现
        """
        self._user_repository = user_repository or ChatbiUserRepository()
        self._user_domain_service = UserDomainService(self._user_repository)

    def _convert_user_to_dict(self, user: User) -> Dict[str, str]:
        """将用户实体转换为字典格式，保持向后兼容"""
        return user.to_dict()

    @in_memory_cache(expire_seconds=600)  # 缓存10分钟
    def get_user_info_by_open_id(self, open_id: str) -> Optional[Dict[str, Any]]:
        """
        根据open_id从数据库获取完整用户信息

        Args:
            open_id: 用户的open_id

        Returns:
            Optional[Dict[str, Any]]: 用户信息字典，未找到时返回None
        """
        try:
            user_info = self._user_repository.find_full_user_info_by_open_id(open_id)

            if not user_info:
                return None

            # 处理avatar字段：如果是JSON字符串则尝试反序列化，否则直接使用字符串URL
            avatar_data = user_info.get('avatar')
            if avatar_data and isinstance(avatar_data, str):
                # 先尝试作为JSON解析，如果失败则当作普通字符串URL处理
                try:
                    parsed_avatar = json.loads(avatar_data)
                    # 如果解析成功且是字典，提取avatar_thumb或其他字段
                    if isinstance(parsed_avatar, dict):
                        avatar_data = (parsed_avatar.get("avatar_thumb") or
                                     parsed_avatar.get("avatar_middle") or
                                     parsed_avatar.get("avatar_big") or
                                     parsed_avatar.get("avatar_origin"))
                    else:
                        avatar_data = parsed_avatar
                except (json.JSONDecodeError, TypeError):
                    # 如果JSON解析失败，说明是普通字符串URL，直接使用
                    pass

            # 将avatar处理后的数据更新到用户数据中
            user_info['avatar'] = avatar_data

            # 如果有union_id，获取summerfarm_api_token
            union_id = user_info.get('union_id')
            if union_id:
                api_token = get_api_token(union_id=union_id)
                user_info['summerfarm_api_token'] = api_token

            return user_info

        except Exception as e:
            logger.exception(f"根据open_id获取用户信息失败: {open_id}, error: {e}")
            return None

    @staticmethod
    def get_user_info_by_username(username: str) -> Optional[Dict[str, str]]:
        """
        根据用户名从user表获取用户信息

        Args:
            username: 用户姓名

        Returns:
            Dict包含用户信息，或None表示未找到
        """
        service = UserQueryService()
        user = service._user_domain_service.find_user_by_username(username)
        return service._convert_user_to_dict(user) if user else None

    @staticmethod
    def get_user_info_by_email(email: str) -> Optional[Dict[str, str]]:
        """
        根据邮箱地址获取用户信息

        Args:
            email: 用户邮箱地址

        Returns:
            Dict包含用户信息，或None表示未找到
        """
        service = UserQueryService()
        user = service._user_domain_service.find_user_by_email(email)
        return service._convert_user_to_dict(user) if user else None

    @staticmethod
    def search_users(search_term: str, limit: int = 5) -> List[Dict[str, str]]:
        """
        模糊搜索用户信息

        Args:
            search_term: 搜索关键词（姓名或邮箱）
            limit: 限制返回结果数量

        Returns:
            List[Dict[str, str]]: 用户列表
        """
        service = UserQueryService()
        users = service._user_domain_service.search_users(search_term, limit)
        return [service._convert_user_to_dict(user) for user in users]

    @staticmethod
    def get_open_id_by_user_query(input_str: str) -> Optional[Dict[str, str]]:
        """
        根据输入字符串查询用户信息（支持姓名或邮箱）

        Args:
            input_str: 可以是姓名或邮箱地址

        Returns:
            Dict包含用户信息，或None表示未找到
        """
        service = UserQueryService()
        user = service._user_domain_service.find_user_by_query(input_str)
        return service._convert_user_to_dict(user) if user else None

    @staticmethod
    def list_all_users(limit: int = 10) -> List[Dict[str, str]]:
        """
        获取所有用户列表（用于调试）

        Args:
            limit: 限制返回数量

        Returns:
            List[Dict[str, str]]: 用户列表
        """
        service = UserQueryService()
        users = service._user_domain_service.list_users(limit)
        return [service._convert_user_to_dict(user) for user in users]


# 全局服务实例
user_query_service = UserQueryService()


@in_memory_cache(expire_seconds=600)  # 缓存10分钟
def get_user_info_by_open_id(open_id: str) -> Optional[Dict[str, Any]]:
    """
    根据open_id从数据库获取用户信息的便捷函数

    Args:
        open_id: 用户的open_id

    Returns:
        Optional[Dict[str, Any]]: 用户信息字典，未找到时返回None
    """
    return user_query_service.get_user_info_by_open_id(open_id)