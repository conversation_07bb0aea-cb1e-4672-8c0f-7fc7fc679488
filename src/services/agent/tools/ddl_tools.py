from typing import List
from src.utils.logger import logger
from src.utils.resource_manager import load_resource
from src.services.agent.tools.tool_manager import tool_manager
from src.services.xianmudb.query_service import execute_business_query

async def fetch_ddl_for_table(table_names: str) -> str:
    """根据表名获取对应的DDL内容。

    Args:
        table_names: 表名，支持以下格式：
                    1. 单表: orders
                    2. 多表（逗号分隔）: orders,products,merchant

    Returns:
        包含表的DDL语句。如果找不到对应的DDL文件，则返回错误信息。
    """
    # 解析表名列表
    table_list = _parse_table_names(table_names)

    ddl_results = []

    for table_name in table_list:
        # 清理表名（去除空格等）
        table_name = table_name.strip()
        if not table_name:
            continue

        ddl_content = _get_ddl_content(table_name)
        ddl_results.append(f"{ddl_content}")

    return "\n\n---\n\n".join(ddl_results)


def _parse_table_names(table_names: str) -> List[str]:
    """解析表名字符串为表名列表。

    Args:
        table_names: 表名字符串，支持以下格式：
                    1. 单表: orders
                    2. 多表（逗号分隔）: orders,products,merchant

    Returns:
        表名列表
    """
    if not isinstance(table_names, str):
        logger.warning(f"输入参数必须是字符串，当前类型: {type(table_names)}, 值: {table_names}")
        return [str(table_names)]

    table_names = table_names.strip()
    logger.info(f"解析表名字符串: {table_names}")

    # 如果包含逗号，按逗号分割
    if ',' in table_names:
        logger.info("检测到逗号分隔格式")
        table_list = [name.strip() for name in table_names.split(',')]
        return [name for name in table_list if name]  # 过滤空字符串

    # 单表格式
    logger.info("识别为单表格式")
    return [table_names] if table_names else []


def _get_ddl_content(table_name: str) -> str:
    """获取单个表的DDL内容。
    
    Args:
        table_name: 表名
        
    Returns:
        DDL内容或错误信息
    """
    # 根据表名构造文件名
    filename = f"{table_name}_ddl.sql"
    
    # 使用资源管理器加载DDL文件
    ddl_content = load_resource('tables_ddl', filename)

    if ddl_content is None:
        logger.warning(f"DDL文件未找到: {filename}, 将尝试从数据库中获取DDL")
        ddl_content = _get_ddl_from_database(table_name)

    return ddl_content


def _get_ddl_from_database(table_name: str) -> str:
    """从数据库中获取DDL。
    
    Args:
        table_name: 表名
        
    Returns:
        DDL内容或错误信息
    """
    try:
        sql = f"show create table {table_name}"
        result = execute_business_query(sql)
        if result.success:
            return result.data
        else:
            return f"Error, table not found: {table_name}"
    except Exception as e:
        logger.exception(f"从数据库获取DDL失败: {e}")
        return f"Error retrieving DDL for table: {table_name}, reason: {str(e)}"


# 注册为工具函数
tool_manager.register_as_function_tool(fetch_ddl_for_table)