"""
鲜沐商城商品实时销量查询工具。
"""

import aiohttp
from typing import List, Dict, Any, Optional, Tuple
from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.utils.logger import logger
from src.services.agent.tools.tool_manager import tool_manager


async def query_product_realtime_sales_quantity_in_warehouse(
    wrapper: RunContextWrapper[UserInfo],
    pd_name: Optional[str] = None,
    sku_list: Optional[List[str]] = None,
    page_size: int = 100,
) -> Tuple[List[Dict[str, Any]], str]:
    """查询商品在仓库维度的实时销量数据。
    
    该工具用于查询鲜沐商城中商品在各个仓库的实时销量、历史销量（1-7天）以及实时库存信息。
    可以根据商品名称或SKU列表进行精确查询。
    
    Args:
        pd_name: 商品名称关键词，支持模糊搜索。例如："安佳淡奶油"。
        sku_list: SKU代码列表，用于精确查询特定商品。例如：["56143", "N001S01R005"]。
        page_size: 返回结果数量，默认为100，最大不超过500。
        
    Returns:
        Tuple[List[Dict[str, Any]], str]: 查询结果列表和描述信息。
        结果包含：sku（商品编码）、pdName（商品名称）、warehouseName（仓库名称）、
        realTimeSalesQuantity（今日实时销量）、historySalesOne（最近1天销量）、
        historySalesTwo（最近2天销量）、availableQuantity（可用库存）、
        quantity（总库存）、onlineQuantity（在线库存）等字段。
    """
    user_info = wrapper.context
    api_token = user_info.summerfarm_api_token
    
    if not api_token:
        error_msg = "用户未提供鲜沐商城API令牌"
        logger.warning(error_msg)
        return [], f"查询商品销量失败: {error_msg}"
    
    if not pd_name and not sku_list:
        error_msg = "必须提供商品名称或SKU列表中的至少一个查询条件"
        logger.warning(error_msg)
        return [], f"查询商品销量失败: {error_msg}"
    
    logger.info(
        f"开始查询商品实时销量，商品名称: {pd_name}, SKU列表: {sku_list}, 页大小: {page_size}"
    )
    
    try:
        # 构建请求数据
        request_data = {
            "pageIndex": 1,
            "pageSize": min(page_size, 500),  # 限制最大页大小为500
            "orderType": 2,
            "categoryTypeNos": [],
            "extTypeList": [0],
            "saleType": 1,
            "subTypeList": [2, 1, 3],
            "skuList": sku_list or []
        }
        
        # 如果提供了商品名称，添加到请求中
        if pd_name:
            request_data["pdName"] = pd_name
        
        # 构建请求头
        headers = {
            "Content-Type": "application/json",
            "token": api_token
        }
        
        # API 地址
        api_url = "https://admin.summerfarm.net/pms-service/inventory-store/query/page"
        
        # 设置超时时间为30秒
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(
                api_url, headers=headers, json=request_data
            ) as response:
                response_text = await response.text()
                
                if response.status != 200:
                    error_msg = f"API调用失败，状态码: {response.status}, 响应内容: {response_text}"
                    logger.error(error_msg)
                    return [], f"查询商品销量失败: {error_msg}"
                
                try:
                    result_data = await response.json()
                except Exception as json_error:
                    logger.error(f"解析响应JSON失败: {json_error}, 响应内容: {response_text}")
                    return [], f"解析响应数据失败: {str(json_error)}"
                
                # 检查API返回的业务状态
                if result_data.get("status") != 200:
                    error_msg = result_data.get("msg", "未知错误")
                    logger.error(f"API返回错误: {error_msg}")
                    return [], f"查询商品销量失败: {error_msg}"
                
                # 提取商品列表数据
                data = result_data.get("data", {})
                product_list = data.get("list", [])
                
                if not product_list:
                    return [], f"未找到符合条件的商品销量数据"
                
                # 处理返回数据，提取关键销量信息
                processed_results = []
                for item in product_list:
                    processed_item = {
                        "sku": item.get("sku"),
                        "pdName": item.get("pdName"),
                        "warehouseName": item.get("warehouseName"),
                        "warehouseNo": item.get("warehouseNo"),
                        "realTimeSalesQuantity": item.get("realTimeSalesQuantity", 0),
                        "historySalesOne": item.get("historySalesOne", 0),
                        "historySalesTwo": item.get("historySalesTwo", 0),
                        "historySalesThree": item.get("historySalesThree", 0),
                        "historySalesFour": item.get("historySalesFour", 0),
                        "historySalesFive": item.get("historySalesFive", 0),
                        "historySalesSix": item.get("historySalesSix", 0),
                        "historySalesSeven": item.get("historySalesSeven", 0),
                        "availableQuantity": item.get("availableQuantity", 0),
                        "quantity": item.get("quantity", 0),
                        "onlineQuantity": item.get("onlineQuantity", 0),
                        "frozenQuantity": item.get("frozenQuantity", 0),
                        "roadQuantity": item.get("roadQuantity", 0),
                        "weight": item.get("weight"),
                        "safeQuantity": item.get("safeQuantity", 0)
                    }
                    processed_results.append(processed_item)
                
                # 生成描述信息
                total_count = len(processed_results)
                warehouse_stats = {}
                for item in processed_results:
                    warehouse = item["warehouseName"]
                    if warehouse not in warehouse_stats:
                        warehouse_stats[warehouse] = 0
                    warehouse_stats[warehouse] += 1
                
                description_parts = []
                if pd_name:
                    description_parts.append(f"商品名称: {pd_name}")
                if sku_list:
                    description_parts.append(f"SKU数量: {len(sku_list)}")
                
                warehouse_desc = ", ".join([
                    f"{warehouse}({count}个商品)" 
                    for warehouse, count in warehouse_stats.items()
                ])
                description_parts.append(f"仓库分布: {warehouse_desc}")
                
                description = f"成功查询到 {total_count} 个商品的销量数据，{', '.join(description_parts)}"
                
                logger.info(f"商品销量查询完成: {description}")
                return processed_results, description
                
    except Exception as e:
        error_msg = f"查询商品销量时发生异常: {str(e)}"
        logger.exception(error_msg)
        return [], f"查询失败: {error_msg}"


# 注册工具
tool_manager.register_as_function_tool(query_product_realtime_sales_quantity_in_warehouse)