import os
import re
import subprocess
from typing import List, Dict, Tuple
from src.utils.logger import logger
from src.services.agent.tools.tool_manager import tool_manager


async def search_ddl_by_content(keywords: str, max_results: int = 10) -> str:
    """基于内容搜索ODPS表DDL定义文件。
    
    使用正则表达式在DDL文件中搜索相关内容，支持中文关键词搜索。
    不再依赖精确的表名匹配，而是基于文件内容进行模糊搜索。
    
    Args:
        keywords: 搜索关键词，支持中文。可以是单个词或多个词（用空格分隔）
                 例如: "SaaS订单", "客户 订单", "库存 仓库"
        max_results: 最多返回的表DDL数量，默认10个
        
    Returns:
        包含匹配到的DDL文件内容的字符串。每个DDL之间用分隔符分开。
        如果没有找到匹配的文件，返回相应的提示信息。
    """
    try:
        # 获取DDL文件目录路径
        ddl_dir = "resources/odps_refined_tables"
        
        if not os.path.exists(ddl_dir):
            return f"错误：DDL目录不存在: {ddl_dir}"
        
        # 搜索匹配的文件
        matched_files = _search_files_by_content(ddl_dir, keywords, max_results)
        
        if not matched_files:
            return f"未找到包含关键词 '{keywords}' 的DDL文件"
        
        # 读取并返回匹配文件的内容
        results = []
        for file_path, relevance_score in matched_files:
            file_name = os.path.basename(file_path)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    results.append(f"=== {file_name} (相关度: {relevance_score:.2f}) ===\n{content}")
            except Exception as e:
                logger.warning(f"读取文件失败 {file_path}: {e}")
                results.append(f"=== {file_name} ===\n读取文件失败: {str(e)}")
        
        return "\n\n" + "\n\n".join(results)
        
    except Exception as e:
        logger.exception(f"搜索DDL内容时发生错误: {e}")
        return f"搜索过程中发生错误: {str(e)}"


def _search_files_by_content(ddl_dir: str, keywords: str, max_results: int) -> List[Tuple[str, float]]:
    """在DDL目录中搜索包含关键词的文件。
    
    Args:
        ddl_dir: DDL文件目录路径
        keywords: 搜索关键词
        max_results: 最大返回结果数
        
    Returns:
        包含文件路径和相关度分数的元组列表，按相关度降序排列
    """
    try:
        # 将关键词分割为单词列表
        keyword_list = _split_keywords(keywords)
        
        # 使用grep命令搜索文件
        matched_files = _grep_search_files(ddl_dir, keyword_list)
        
        # 计算相关度分数并排序
        scored_files = []
        for file_path in matched_files:
            score = _calculate_relevance_score(file_path, keyword_list)
            if score > 0:
                scored_files.append((file_path, score))
        
        # 按相关度分数降序排序
        scored_files.sort(key=lambda x: x[1], reverse=True)
        
        # 返回前max_results个结果
        return scored_files[:max_results]
        
    except Exception as e:
        logger.exception(f"搜索文件时发生错误: {e}")
        return []


def _split_keywords(keywords: str) -> List[str]:
    """将关键词字符串分割为单词列表。
    
    Args:
        keywords: 关键词字符串
        
    Returns:
        关键词列表
    """
    # 使用空格、逗号、分号等分隔符分割关键词
    keyword_list = re.split(r'[,，\s;；]+', keywords.strip())
    # 过滤空字符串
    return [kw.strip() for kw in keyword_list if kw.strip()]


def _grep_search_files(ddl_dir: str, keyword_list: List[str]) -> List[str]:
    """使用grep命令搜索包含关键词的文件。
    
    Args:
        ddl_dir: DDL文件目录路径
        keyword_list: 关键词列表
        
    Returns:
        匹配的文件路径列表
    """
    matched_files = set()
    
    try:
        for keyword in keyword_list:
            if not keyword:
                continue
                
            # 使用grep命令搜索包含关键词的文件
            # -l: 只显示文件名，不显示匹配的行
            # -r: 递归搜索
            # -i: 忽略大小写
            # --include="*.md": 只搜索.md文件
            cmd = [
                'grep', '-l', '-r', '-i',
                '--include=*.sql',
                keyword,
                ddl_dir
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30  # 30秒超时
            )
            
            if result.returncode == 0:
                # grep找到了匹配的文件
                files = result.stdout.strip().split('\n')
                matched_files.update(f for f in files if f.strip())
            elif result.returncode == 1:
                # grep没有找到匹配，这是正常情况
                logger.debug(f"关键词 '{keyword}' 没有找到匹配的文件")
            else:
                # grep命令执行出错
                logger.warning(f"grep命令执行失败，关键词: {keyword}, 错误: {result.stderr}")
                
    except subprocess.TimeoutExpired:
        logger.warning("grep搜索超时")
    except Exception as e:
        logger.exception(f"执行grep搜索时发生错误: {e}")
    
    return list(matched_files)


def _calculate_relevance_score(file_path: str, keyword_list: List[str]) -> float:
    """计算文件与关键词的相关度分数。
    
    Args:
        file_path: 文件路径
        keyword_list: 关键词列表
        
    Returns:
        相关度分数（0-1之间的浮点数）
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().lower()
        
        total_score = 0.0
        total_keywords = len(keyword_list)
        
        if total_keywords == 0:
            return 0.0
        
        for keyword in keyword_list:
            keyword_lower = keyword.lower()
            
            # 计算关键词在文件中出现的次数
            count = content.count(keyword_lower)
            
            if count > 0:
                # 基础分数：关键词存在
                keyword_score = 0.3
                
                # 额外分数：基于出现次数（最多0.4分）
                frequency_score = min(0.4, count * 0.1)
                
                # 位置加权：如果关键词出现在注释中，给予额外分数
                if _keyword_in_comments(content, keyword_lower):
                    keyword_score += 0.3
                
                total_score += keyword_score + frequency_score
        
        # 归一化分数到0-1范围
        return min(1.0, total_score / total_keywords)
        
    except Exception as e:
        logger.warning(f"计算文件相关度时发生错误 {file_path}: {e}")
        return 0.0


def _keyword_in_comments(content: str, keyword: str) -> bool:
    """检查关键词是否出现在注释中。
    
    Args:
        content: 文件内容
        keyword: 关键词
        
    Returns:
        如果关键词出现在注释中返回True
    """
    # 查找所有注释行（以#开头的行）
    comment_lines = []
    for line in content.split('\n'):
        line = line.strip()
        if '#' in line:
            # 提取#后面的注释部分
            comment_part = line[line.find('#'):].lower()
            comment_lines.append(comment_part)
    
    # 检查关键词是否在注释中
    comment_text = ' '.join(comment_lines)
    return keyword in comment_text


# 注册为工具函数
tool_manager.register_as_function_tool(search_ddl_by_content)
