"""
卡片操作处理器配置模块
用于定义各种卡片操作的处理逻辑配置
"""

# 卡片操作处理器配置
ACTION_HANDLERS_CONFIG = {
    "good_case": {
        "service_module": "src.services.chatbot.good_case_service",
        "service_function": "mark_good_case",
        "service_params": {"is_good_case": True},
        "after_action_module": "src.services.feishu.message_apis",
        "after_action_function": "after_goodcase_mark",
        "toast": {
            "type": "success",
            "content": "已标记为Good Case，感谢反馈！",
        }
    },
    "bad_case": {
        "service_module": "src.services.chatbot.bad_case_retry_service",
        "service_function": "handle_bad_case_with_retry",
        "service_params": {},
        "after_action_module": "src.services.feishu.card_operations",
        "after_action_function": "after_badcase_mark",
        "toast": {
            "type": "info",
            "content": "已标记为Badcase，正在为您重新生成回复...",
        }
    },
    "choices_feedback": {
        "service_module": "src.services.chatbot.sku_choice_service",
        "service_function": "handle_sku_choice",
        "service_params": {},
        "after_action_module": "src.services.feishu.card_operations",
        "after_action_function": "after_sku_choice_selection",
        "toast": {
            "type": "success",
            "content": "已收到您的选择，正在处理中...",
        }
    },
    "recommendation_question": {
        "service_module": "src.services.chatbot.recommendation_question_service",
        "service_function": "handle_recommendation_question_choice",
        "service_params": {},
        "after_action_module": "src.services.feishu.card_operations",
        "after_action_function": "after_recommendation_question_selection",
        "toast": {
            "type": "success",
            "content": "已收到您的问题，正在为您查询...",
        }
    },
    # 示例：添加新的操作类型
    # "favorite": {
    #     "service_module": "src.services.chatbot.favorite_service",
    #     "service_function": "mark_favorite",
    #     "service_params": {"is_favorite": True},
    #     "after_action_module": "src.services.feishu.message_apis",
    #     "after_action_function": "after_favorite_mark",
    #     "toast": {
    #         "type": "success",
    #         "content": "已收藏，感谢反馈！",
    #     }
    # },
    # "share": {
    #     "service_module": "src.services.chatbot.share_service",
    #     "service_function": "share_conversation",
    #     "service_params": {"share_type": "public"},
    #     "after_action_module": "src.services.feishu.message_apis",
    #     "after_action_function": "after_share_action",
    #     "toast": {
    #         "type": "success",
    #         "content": "分享成功！",
    #     }
    # }
}


def get_action_config(action: str) -> dict:
    """获取指定操作的配置
    
    Args:
        action: 操作类型
        
    Returns:
        dict: 操作配置，如果不存在则返回None
    """
    return ACTION_HANDLERS_CONFIG.get(action)


def get_supported_actions() -> list:
    """获取所有支持的操作类型
    
    Returns:
        list: 支持的操作类型列表
    """
    return list(ACTION_HANDLERS_CONFIG.keys())


def register_action_handler(action: str, config: dict) -> None:
    """动态注册新的操作处理器
    
    Args:
        action: 操作类型
        config: 操作配置
    """
    ACTION_HANDLERS_CONFIG[action] = config


def unregister_action_handler(action: str) -> bool:
    """注销操作处理器
    
    Args:
        action: 操作类型
        
    Returns:
        bool: 是否成功注销
    """
    if action in ACTION_HANDLERS_CONFIG:
        del ACTION_HANDLERS_CONFIG[action]
        return True
    return False
