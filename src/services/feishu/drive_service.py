"""飞书云盘服务模块
负责管理用户的飞书云盘文档文件夹
"""

import requests
from datetime import datetime
from typing import Optional, Dict, Any, List
from src.utils.logger import logger
from src.services.feishu.token_service import TokenService
from src.repositories.chatbi.user import UserRepository, ChatbiUserRepository


class DriveService:
    """飞书云盘服务类"""

    def __init__(self, user_repository: Optional[UserRepository] = None):
        """
        初始化飞书云盘服务

        Args:
            user_repository: 用户仓储接口实现，如果为None则使用默认实现
        """
        self.user_repository = user_repository or ChatbiUserRepository()
    
    @staticmethod
    def get_user_root_folder_token(user_access_token: str) -> Optional[str]:
        """获取用户云盘根目录token
        
        Args:
            user_access_token: 用户访问token
            
        Returns:
            Optional[str]: 根目录token，获取失败返回None
        """
        url = "https://open.feishu.cn/open-apis/drive/explorer/v2/root_folder/meta"
        headers = {"Authorization": f"Bearer {user_access_token}"}
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    return result["data"]["token"]
                else:
                    logger.exception(f"获取根目录token失败: {result}")
                    return None
            else:
                logger.exception(f"获取根目录token请求失败: status={response.status_code}, response={response.text}")
                return None
        except Exception as e:
            logger.exception(f"获取根目录token时出错: {e}", exc_info=True)
            return None
    
    @staticmethod
    def list_folder_files(user_access_token: str, folder_token: str) -> Optional[List[Dict[str, Any]]]:
        """获取文件夹下的文件列表
        
        Args:
            user_access_token: 用户访问token
            folder_token: 文件夹token
            
        Returns:
            Optional[List[Dict[str, Any]]]: 文件列表，获取失败返回None
        """
        url = f"https://open.feishu.cn/open-apis/drive/v1/files?direction=DESC&folder_token={folder_token}&order_by=CreatedTime"
        headers = {"Authorization": f"Bearer {user_access_token}"}
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    return result["data"]["files"]
                else:
                    logger.exception(f"获取文件列表失败: {result}")
                    return None
            else:
                logger.exception(f"获取文件列表请求失败: status={response.status_code}, response={response.text}")
                return None
        except Exception as e:
            logger.exception(f"获取文件列表时出错: {e}", exc_info=True)
            return None
    
    @staticmethod
    def find_chatbi_folder(files: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """在文件列表中查找ChatBI文件夹
        
        Args:
            files: 文件列表
            
        Returns:
            Optional[Dict[str, Any]]: ChatBI文件夹信息，未找到返回None
        """
        current_month = datetime.now().strftime("%Y%m")
        target_folder_name = f"ChatBI-{current_month}"
        
        for file in files:
            if file.get("type") == "folder" and file.get("name") == target_folder_name:
                return file
        
        return None
    
    @staticmethod
    def create_chatbi_folder(user_access_token: str, parent_folder_token: str) -> Optional[Dict[str, Any]]:
        """创建ChatBI文件夹
        
        Args:
            user_access_token: 用户访问token
            parent_folder_token: 父文件夹token
            
        Returns:
            Optional[Dict[str, Any]]: 创建的文件夹信息，创建失败返回None
        """
        url = "https://open.feishu.cn/open-apis/drive/v1/files/create_folder"
        headers = {
            "Authorization": f"Bearer {user_access_token}",
            "Content-Type": "application/json"
        }
        
        current_month = datetime.now().strftime("%Y%m")
        folder_name = f"ChatBI-{current_month}"
        
        data = {
            "folder_token": parent_folder_token,
            "name": folder_name
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    folder_info = {
                        "token": result["data"]["token"],
                        "name": folder_name,
                        "url": result["data"]["url"]
                    }
                    logger.info(f"成功创建ChatBI文件夹: {folder_info}")
                    return folder_info
                else:
                    logger.exception(f"创建文件夹失败: {result}")
                    return None
            else:
                logger.exception(f"创建文件夹请求失败: status={response.status_code}, response={response.text}")
                return None
        except Exception as e:
            logger.exception(f"创建文件夹时出错: {e}", exc_info=True)
            return None
    
    def update_user_folder_info(self, open_id: str, folder_token: str, folder_name: str) -> bool:
        """更新用户表中的文件夹信息"""
        try:
            ok = self.user_repository.update_user_folder_info(open_id, folder_token, folder_name)
            if ok:
                logger.info(f"成功更新用户文件夹信息: open_id={open_id}, folder_token={folder_token}, folder_name={folder_name}")
            return ok
        except Exception as e:
            logger.exception(f"更新用户文件夹信息失败: {e}", exc_info=True)
            return False

    def get_user_folder_info(self, open_id: str) -> Optional[Dict[str, str]]:
        """获取用户的文件夹信息"""
        try:
            info = self.user_repository.get_user_folder_info(open_id)
            if not info:
                logger.warning(f"未找到用户信息: open_id={open_id}")
            return info
        except Exception as e:
            logger.exception(f"获取用户文件夹信息失败: {e}", exc_info=True)
            return None
    
    def ensure_user_chatbi_folder(self, open_id: str) -> Optional[str]:
        """确保用户拥有ChatBI文件夹，如果不存在则创建
        
        这是主要的业务逻辑方法，会：
        1. 检查用户是否已有当前月份的ChatBI文件夹
        2. 如果没有，获取根目录并查找或创建文件夹
        3. 更新用户表中的文件夹信息
        
        Args:
            open_id: 用户open_id
            
        Returns:
            Optional[str]: ChatBI文件夹的token，操作失败返回None
        """
        try:
            # 获取用户访问token
            user_access_token = TokenService.get_user_access_token(open_id)
            if not user_access_token:
                logger.exception(f"无法获取用户访问token: open_id={open_id}")
                return None
            
            # 检查用户当前的文件夹信息
            current_month = datetime.now().strftime("%Y%m")
            expected_folder_name = f"ChatBI-{current_month}"
            
            user_folder_info = self.get_user_folder_info(open_id)
            if (user_folder_info and 
                user_folder_info.get("folder_name") == expected_folder_name and 
                user_folder_info.get("folder_token")):
                # 用户已有当前月份的文件夹
                logger.info(f"用户已有当前月份的ChatBI文件夹: {expected_folder_name}")
                return user_folder_info["folder_token"]
            
            # 获取根目录token
            root_folder_token = DriveService.get_user_root_folder_token(user_access_token)
            if not root_folder_token:
                logger.exception(f"无法获取用户根目录token: open_id={open_id}")
                return None
            
            # 获取根目录下的文件列表
            files = DriveService.list_folder_files(user_access_token, root_folder_token)
            if files is None:
                logger.exception(f"无法获取根目录文件列表: open_id={open_id}")
                return None
            
            # 查找当前月份的ChatBI文件夹
            chatbi_folder = DriveService.find_chatbi_folder(files)
            
            if chatbi_folder:
                # 找到了文件夹，更新用户表
                folder_token = chatbi_folder["token"]
                folder_name = chatbi_folder["name"]
                logger.info(f"找到现有的ChatBI文件夹: {folder_name}")
            else:
                # 没找到，创建新文件夹
                logger.info(f"未找到当前月份的ChatBI文件夹，开始创建: {expected_folder_name}")
                folder_info = DriveService.create_chatbi_folder(user_access_token, root_folder_token)
                if not folder_info:
                    logger.exception(f"创建ChatBI文件夹失败: open_id={open_id}")
                    return None
                
                folder_token = folder_info["token"]
                folder_name = folder_info["name"]
            
            # 更新用户表中的文件夹信息
            if self.update_user_folder_info(open_id, folder_token, folder_name):
                logger.info(f"成功确保用户拥有ChatBI文件夹: open_id={open_id}, folder_token={folder_token}")
                return folder_token
            else:
                logger.exception(f"更新用户文件夹信息失败: open_id={open_id}")
                return None
                
        except Exception as e:
            logger.exception(f"确保用户ChatBI文件夹时出错: {e}", exc_info=True)
            return None


# 全局服务实例
drive_service = DriveService()