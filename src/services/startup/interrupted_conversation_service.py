"""
被中断对话处理领域服务

该服务用于处理应用重启时被中断的用户查询，避免用户查询因系统重启而丢失。
在应用启动时自动检查并处理被中断的对话。

遵循DDD架构原则，通过仓储接口访问数据。
"""

from datetime import datetime
from typing import List, Dict, Any, Optional

from src.utils.logger import logger
from src.repositories.chatbi.interrupted_conversation import InterruptedConversationRepository


class InterruptedConversationDomainService:
    """被中断对话处理领域服务类"""
    
    def __init__(self, repository: InterruptedConversationRepository):
        """
        初始化领域服务
        
        Args:
            repository (InterruptedConversationRepository): 被中断对话仓储
        """
        self.repository = repository
        self.app_startup_time = datetime.now()
        logger.info(f"被中断对话处理领域服务初始化，应用启动时间: {self.app_startup_time}")
    
    def process_interrupted_conversations(self) -> Dict[str, Any]:
        """
        处理被中断的对话
        
        Returns:
            Dict[str, Any]: 处理结果统计信息
        """
        logger.info("开始处理被中断的对话...")
        
        try:
            # 查找被中断的对话
            interrupted_messages = self._find_interrupted_conversations()
            
            if not interrupted_messages:
                logger.info("未发现被中断的对话")
                return {
                    "success": True,
                    "processed_count": 0,
                    "updated_conversations": 0,
                    "message": "未发现被中断的对话"
                }
            
            logger.info(f"发现 {len(interrupted_messages)} 条被中断的assistant消息")
            
            # 处理每条被中断的消息
            processed_count = 0
            updated_conversations = set()
            
            for message in interrupted_messages:
                try:
                    success = self._process_single_interrupted_message(message)
                    if success:
                        processed_count += 1
                        updated_conversations.add(message['conversation_id'])
                        logger.debug(f"成功处理被中断消息 ID: {message['id']}, 对话ID: {message['conversation_id']}")
                    else:
                        logger.warning(f"处理被中断消息失败 ID: {message['id']}")
                except Exception as e:
                    logger.exception(f"处理单条被中断消息时发生错误 ID: {message.get('id', 'unknown')}: {e}")
                    continue
            
            result = {
                "success": True,
                "processed_count": processed_count,
                "updated_conversations": len(updated_conversations),
                "message": f"成功处理 {processed_count} 条被中断消息，涉及 {len(updated_conversations)} 个对话"
            }
            
            logger.info(f"被中断对话处理完成: {result['message']}")
            return result
            
        except Exception as e:
            error_msg = f"处理被中断对话时发生错误: {e}"
            logger.exception(error_msg, exc_info=True)
            return {
                "success": False,
                "processed_count": 0,
                "updated_conversations": 0,
                "error": error_msg
            }
    
    def _find_interrupted_conversations(self) -> List[Dict[str, Any]]:
        """
        查找被中断的对话消息
        
        通过仓储接口查找被中断的assistant消息
        
        Returns:
            List[Dict[str, Any]]: 被中断的消息列表
        """
        return self.repository.find_interrupted_assistant_messages(self.app_startup_time)
    
    def _process_single_interrupted_message(self, message: Dict[str, Any]) -> bool:
        """
        处理单条被中断的消息
        
        处理逻辑：
        1. 将消息状态更新为已完成 (is_in_process = 0)
        2. 在消息内容末尾追加系统重启提示
        
        Args:
            message (Dict[str, Any]): 被中断的消息信息
            
        Returns:
            bool: 处理是否成功
        """
        message_id = message['id']
        conversation_id = message['conversation_id']
        current_content = message.get('content', '') or ''
        
        # 构建新的消息内容，在原内容基础上追加系统重启提示
        termination_message = "\n\n因系统重启，您的任务已终止，您可回复'请重试'继续和ChatBI对话"
        
        # 避免重复追加提示信息
        if "因系统重启，您的任务已终止" not in current_content:
            new_content = current_content + termination_message
        else:
            new_content = current_content
            logger.debug(f"消息 ID {message_id} 已包含系统重启提示，跳过追加")
        
        # 通过仓储更新消息
        success = self.repository.update_interrupted_message(message_id, new_content)
        
        if success:
            logger.info(f"成功更新被中断消息 ID: {message_id}, 对话ID: {conversation_id}")
        else:
            logger.warning(f"未能更新被中断消息 ID: {message_id}")
            
        return success
    
    def get_interrupted_conversations_count(self) -> int:
        """
        获取当前被中断的对话数量（用于监控）
        
        Returns:
            int: 被中断的对话数量
        """
        return self.repository.count_interrupted_messages(self.app_startup_time)
    
    def check_conversation_interruption_status(self, conversation_id: str) -> Dict[str, Any]:
        """
        检查特定对话的中断状态（用于调试）
        
        Args:
            conversation_id (str): 对话ID
            
        Returns:
            Dict[str, Any]: 对话中断状态信息
        """
        if not conversation_id:
            return {"error": "对话ID不能为空"}
        
        try:
            results = self.repository.find_conversation_assistant_messages(conversation_id, self.app_startup_time)
            
            if not results:
                return {
                    "conversation_id": conversation_id,
                    "status": "no_assistant_messages",
                    "message": "该对话中未找到assistant消息"
                }
            
            interrupted_messages = [
                msg for msg in results
                if msg['is_in_process'] == 1 and msg['created_at'] < self.app_startup_time
            ]
            
            return {
                "conversation_id": conversation_id,
                "total_assistant_messages": len(results),
                "interrupted_messages_count": len(interrupted_messages),
                "interrupted_messages": interrupted_messages,
                "status": "interrupted" if interrupted_messages else "normal"
            }
            
        except Exception as e:
            logger.exception(f"检查对话中断状态时发生错误: {e}", exc_info=True)
            return {
                "conversation_id": conversation_id,
                "error": f"检查失败: {str(e)}"
            }


def create_interrupted_conversation_service() -> InterruptedConversationDomainService:
    """
    创建被中断对话处理领域服务实例
    
    Returns:
        InterruptedConversationDomainService: 领域服务实例
    """
    from src.repositories.chatbi.interrupted_conversation import create_interrupted_conversation_repository
    
    repository = create_interrupted_conversation_repository()
    return InterruptedConversationDomainService(repository)


def process_interrupted_conversations_on_startup() -> Dict[str, Any]:
    """
    应用启动时处理被中断的对话（便捷函数）
    
    Returns:
        Dict[str, Any]: 处理结果
    """
    service = create_interrupted_conversation_service()
    return service.process_interrupted_conversations()