# Repository Guidelines

## 项目结构与模块职责
- `src/` 为主业务代码根目录，按照领域驱动设计拆分：`api/` 暴露 Flask Blueprint；`services/` 聚合领域服务、聊天机器人与 Feishu 集成；`repositories/` 负责数据访问与持久化；`models/` 定义领域模型；`db/` 管理数据库连接池；`utils/` 提供通用工具。`resources/` 存放 Agent 配置与提示词，`templates/` 和 `static/` 处理前端资源，`app.py`/`main.py` 为应用入口。
- 每个目录可能包含自己的 `AGENT*.md` 说明，修改该目录下代码前请先阅读。从仓储层调用数据库，禁止绕过仓储直接执行 SQL。

## 构建、运行与调试
- **同步依赖**：`uv pip sync` 会依据 `pyproject.toml` 与 `uv.lock` 搭建一致的虚拟环境。
- **开发启动**：`uv run python app.py --port 5700 --debug`，需要 `.env` 中配置 MySQL（默认 3308）与 Feishu 凭证。
- **脚本执行**：统一使用 `uv run python <path>`，确保依赖上下文一致。对于调试任务，可结合 `chatbi_data_migration.py`、`docs/` 中的 SQL/脚本运行。

## 核心工作流程
1. **规划阶段**：分析需求、辨识边界，基于 DDD 绘制 API 层 → 领域服务 → 仓储 → 数据库的分层方案，并评估风险。
2. **确认阶段**：向需求方展示架构（职责划分、数据流、关键决策），等待明确同意后再开始编码。
3. **实现阶段**：遵循批准方案逐层实现，保持仓储/服务复用，持续验证行为符合设计。

## 编码规范与命名
- 代码遵循 SOLID、DRY、KISS 原则；函数长度控制在 50 行左右，参数超过 4 个时考虑封装对象；嵌套层级不超过 3 层。
- Python 使用 4 空格缩进；函数以动词短语命名，变量蛇形命名，常量全大写，布尔以 `is/has/can` 前缀。仅在必要处添加中文注释，侧重解释设计理由。
- 复用既有服务与仓储；配置或密钥放入 `.env`，严禁硬编码。

## 测试要求
- 现有测试集中在仓库根目录下的 `test_*.py`，如 `test_agent_direct.py`。运行单元/集成测试请执行 `uv run pytest`，需要本地 MySQL、Feishu 模拟或环境变量就绪。
- 新增测试文件命名为 `test_<module>.py`，测试函数使用 `test_<behavior>`，推荐 Given/When/Then 模式描述场景。

## 提交与 PR 指南
- Commit 信息使用“类型: 简述”或动词开头（示例：`fix: adjust feishu card params`），保持原子化，不混入无关改动。
- PR 需包含：变更摘要、测试说明、关联 issue/工单链接；涉及 UI 或卡片需附截图/日志。确保 CI 通过后再申请合并。

## 安全与配置提示
- 切勿提交真实密钥或数据库凭证，使用 `.env.*` 模板管理。如需共享配置，改用环境变量或安全存储方案。
- 调整 Feishu 回调、数据库 schema、任务调度时同步更新 `docs/` 与迁移脚本，并在 PR 中说明部署与回滚策略。

## 附加：DDD 既有实体与仓储
- 领域模型已固化：推荐子域定义于 `src/models/recommendation.py`，聊天历史子域定义于 `src/models/user_info_class.py`，禁止重复声明。
- 推荐仓储接口位于 `src/repositories/chatbi/recommendation.py`，用户仓储位于 `src/repositories/chatbi/user.py`，其它 bad/good case、history 仓储沿用现有实现。业务代码应依赖这些接口，示例：
  ```python
  from src.services.domain.recommendation_domain_service import RecommendationDomainService
  from src.repositories.chatbi.recommendation import ChatbiRecommendationRepository

  repo = ChatbiRecommendationRepository()
  service = RecommendationDomainService(repo)
  recommendations = service.get_user_recommendations(user_email, count=6)
  ```

## 工作提示与禁止事项
- 中文为默认沟通语言；完成任务后可建议测试方法但不强制提供用例。
- 禁止重复造轮子，优先复用共享逻辑或开源库；配置统一管理，避免散落常量。
- 完整交付包括：按设计实现、提供必要说明、避免修改关键 API 地址或破坏现有功能。
