{"cells": [{"cell_type": "code", "execution_count": null, "id": "49900ce6", "metadata": {}, "outputs": [], "source": ["from src.services.odps.odps_client import odps,get_odps_sql_result_as_df\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "\n", "tables = odps.list_tables(project=\"summerfarm_tech\", prefix=\"app_\")\n", "\n", "today = datetime.now()\n", "\n", "def get_string_columns_only(df:pd.DataFrame):\n", "    string_columns = {}\n", "    \n", "    for col in df.columns:\n", "        if df[col].dtype == \"object\":\n", "            # Get a non-null sample to check the actual type\n", "            sample = df[col].dropna()\n", "            if not sample.empty:\n", "                first_value = sample.iloc[0]\n", "                # Only include if it's actually a string, not Decimal or other objects\n", "                if isinstance(first_value, str):\n", "                    string_columns[col] = list(df[col].unique())[:50]\n", "    \n", "    return string_columns\n", "\n", "def fetch_table_ddl(table):\n", "    if table.last_data_modified_time < today - timedelta(days=30):\n", "        print(table.name, \"is older than 30 days\", table.last_data_modified_time)\n", "        return\n", "    if \"app_anchor_\" in table.name or \"_extra_h\" in table.name:\n", "        print(table.name, \"is anchor table or extra table，跳过\")\n", "        return\n", "\n", "    print(table.name, table.last_data_modified_time)\n", "    partition_to_fetch_data=table.partitions.get_max_partition()\n", "    sql_to_fetch_head=f\"select * from {table.name} where {partition_to_fetch_data} limit 10000\"\n", "    head_data = None\n", "    print(\"sql_to_fetch_head:\", sql_to_fetch_head)\n", "    head_data = get_odps_sql_result_as_df(sql_to_fetch_head)\n", "    if head_data is None:\n", "        print(table.name, \"is empty\")\n", "        return\n", "    head_data_desc=head_data.describe().to_markdown()\n", "    head_data_json=head_data.head(10).astype(str).to_json(force_ascii=False)\n", "\n", "    column_unique_values = {}\n", "    for col in get_string_columns_only(head_data):\n", "        column_unique_values[col] = list(head_data[col].unique())[:50]\n", "        if len(column_unique_values[col]) < len(head_data[col].unique()):\n", "            column_unique_values[col].append(f\"...还有{len(head_data[col].unique())-50}个unique的值，请注意甄别是否是枚举值\")\n", "\n", "    table_ddl_content = f\"\"\"\n", "## {table.name}\n", "* comment: {table.comment}\n", "* last_data_modified_time: {table.last_data_modified_time}\n", "\n", "### schema:\n", "{table.get_ddl()}\n", "\n", "### head data:\n", "{head_data_json}\n", "\n", "### head data desc(generated by pandas.dataframe.describe()):\n", "{head_data_desc}\n", "\n", "### string column unique values:\n", "{column_unique_values}\n", "    \"\"\".strip()\n", "    # save the content into file resources/odps_raw_tables/{table.name}.md\n", "    with open(f\"resources/odps_raw_tables/{table.name}.md\", \"w\") as f:\n", "        f.write(table_ddl_content)\n", "\n", "# fetch_table_ddl(odps.get_table(\"app_chatbi_visit_order_attribution_df\"))"]}, {"cell_type": "code", "execution_count": null, "id": "65511955", "metadata": {}, "outputs": [], "source": ["# 请实现批量的fetch_table_ddl：\n", "from concurrent.futures import ThreadPoolExecutor\n", "with ThreadPoolExecutor(max_workers=10) as executor:\n", "    for table in tables:\n", "        try:\n", "            executor.submit(fetch_table_ddl, table)\n", "        except Exception as e:\n", "            print(table.name, \"fetch data failed\", e)"]}, {"cell_type": "code", "execution_count": null, "id": "65507a7f", "metadata": {}, "outputs": [], "source": ["app_chatbi_bd_hierarchy_df_ddl = \"\"\n", "with open(\"resources/tables_ddl/app_chatbi_bd_hierarchy_df_ddl.sql\", \"r\") as f:\n", "    app_chatbi_bd_hierarchy_df_ddl = f.read()\n", "\n", "system_prompt = f\"\"\"你是一位专业的数据分析师，拥有超过10年的数据建模经验。\n", "你的任务是根据用户输入的ODPS表名，以及表的DDL、表的最大分区的数据之Top5行记录、表的最大分区的数据之describe()信息，优化表的DDL语句。\n", "请确保优化后的DDL语句能够满足以下要求：\n", "1. **保持表的名称不变**\n", "2. **保持表的Schema不变**\n", "3. **保持表的分区字段不变**\n", "4. 使用中文补充表的注释、字段的注释、字段的类型说明特别是对于日期类型，要说明是年月日还是年月日时分秒\n", "5. 补充枚举类型字段的取值范围\n", "\n", "产出的结果可高度参考以下示例：\n", "{app_chatbi_bd_hierarchy_df_ddl}\n", "\n", "请注意，不需要说明你是如何改写的，只需要把修改后的DDL语句输出即可。\n", "\"\"\"\n", "\n", "\n", "def call_openai_api_to_refine_ddl(\n", "    table_name: str, system_prompt: str = system_prompt\n", ") -> str:\n", "    print(f\"开始处理表：{table_name}\")\n", "    try:\n", "        table_ddl = \"\"\n", "        with open(f\"resources/odps_raw_tables/{table_name}.md\", \"r\") as f:\n", "            table_ddl = f.read()\n", "        import openai\n", "        import os\n", "        api_key=os.getenv(\"OPENAI_API_KEY\")\n", "        api_base=os.getenv(\"OPENAI_API_BASE\")\n", "        # 创建 OpenAI 客户端\n", "        client = openai.OpenAI(api_key=api_key, base_url=api_base)\n", "\n", "        # 构建消息\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"请优化以下表的DDL：\\n\\n```\\n{table_ddl}\\n```\",\n", "            },\n", "        ]\n", "\n", "        # 调用 API\n", "        response = client.chat.completions.create(\n", "            model=\"deepseek-v3-1\",\n", "            messages=messages,\n", "            temperature=0,\n", "            max_tokens=8000,\n", "            timeout=600,\n", "        )\n", "\n", "        # 提取结果\n", "        enhanced_prompt = response.choices[0].message.content.strip()\n", "\n", "        if not enhanced_prompt:\n", "            raise Exception(\"API 返回空结果\")\n", "\n", "        # write result into file:\n", "        with open(f\"resources/odps_refined_tables/{table_name}_ddl.sql\", \"w\") as f:\n", "            f.write(enhanced_prompt)\n", "        \n", "        return enhanced_prompt\n", "\n", "    except Exception as e:\n", "        print(f\"OpenAI API 调用失败: {e}\")\n", "        raise Exception(f\"AI 服务调用失败: {str(e)}\")\n", "\n", "print(\"开始处理表：app_chatbi_visit_order_attribution_df\")\n", "call_openai_api_to_refine_ddl(\"app_chatbi_visit_order_attribution_df\")"]}, {"cell_type": "code", "execution_count": null, "id": "ff233b8b", "metadata": {}, "outputs": [], "source": ["# 遍历所有表，并发的调用，5个并发：\n", "from concurrent.futures import ThreadPoolExecutor\n", "import os\n", "with ThreadPoolExecutor(max_workers=10) as executor:\n", "    for table_name in os.listdir(\"resources/odps_raw_tables\"):\n", "        if \"_tmp_\" in table_name:\n", "            print(table_name, \"is tmp file, skip\")\n", "            continue\n", "        if \"_wi_\" in table_name or \"_mi_\" in table_name:\n", "            di_file = table_name.replace(\"_wi_\", \"\").replace(\"_mi_\", \"\")\n", "            if os.path.exists(\"resources/odps_raw_tables/\" + di_file):\n", "                print(table_name, \"is wi/mi file, and di file exists, skip\")\n", "                continue\n", "        if table_name.endswith(\".md\"):\n", "            executor.submit(call_openai_api_to_refine_ddl, table_name.replace(\".md\", \"\"))\n", "            print(table_name, \"is being processed\")\n", "        else:\n", "            print(table_name, \"is not md file, skip\")"]}, {"cell_type": "code", "execution_count": null, "id": "340f251c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}